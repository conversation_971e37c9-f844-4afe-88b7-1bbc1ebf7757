import {
	createBrowserRouter,
	Navigate,
	Outlet,
	RouteObject,
} from 'react-router-dom'
import { ToastContainer } from 'react-toastify'
import 'react-toastify/dist/ReactToastify.css'
import { AuthProvider } from '../contexts'
import { QueryClientWrapper, ThemeWrapper } from '../lib'
import { CompanyRegistration } from './CompanyRegistration/companyRegistration'
import { CompanyValidation } from './CompanyRegistration/companyValidation'
import { CreatePassword } from './createPassword/CreatePassword'
import { CreditProjectDetails, CreditsProjects } from './creditsProjects'
import {
	AddConstants,
	Admin,
	ArtisanProNetwork,
	BatchDetails,
	Batches,
	BiomassPage,
	BiomassTypes,
	CompanyDetailsScreen,
	CreateProject,
	Credits,
	CRM,
	CSinkNetwork,
	CSinkNetworkDetails,
	Dashboard,
	EntityManagement,
	FarmDetails,
	Farmer,
	FarmerDetails,
	Files,
	Home,
	InternalProjectDetails,
	InternalProjects,
	Inventory,
	Map,
	Mixing,
	Packaging,
	PendingTask,
	Production,
	Projects,
	Queries,
	Settings,
	Stocks,
	UserManagement,
} from './dashboard'
import { EmailBypass } from './dashboard/Admin/EmailBypass'
import { Geotag } from './dashboard/Admin/Geotag'
import { OtpBypass } from './dashboard/Admin/OtpBypass'
import ProjectDetailsScreen from './dashboard/Admin/ProjectDetails/ProjectDetailsScreen'
import { CompanysTable } from './dashboard/Admin/Queries/Components'
import { ArtisanProDetails } from './dashboard/ArtisanPro'
import { ForgotPassword } from './forgotPassword'
import { Login } from './login'

const GlobalProvider = () => {
	// if (VITE_PUBLIC_APP_ENV === 'PROD') return <MaintenancePage />
	return (
		<ThemeWrapper>
			<QueryClientWrapper>
				<ToastContainer
					position='top-right'
					autoClose={2000}
					hideProgressBar
					toastStyle={{
						textTransform: 'capitalize',
					}}
					newestOnTop={false}
					closeOnClick
					rtl={false}
					theme='dark'
				/>
				<Outlet />
			</QueryClientWrapper>
		</ThemeWrapper>
	)
}

const AuthenticatedRoutes: RouteObject = {
	path: '/',
	Component: AuthProvider,
	children: [
		{
			path: 'login',
			Component: Login,
		},
		{
			path: 'forgot-password',
			Component: ForgotPassword,
		},
		{
			path: 'create-new-password',
			Component: CreatePassword,
		},
		{
			path: 'invitation',
			Component: CreatePassword,
		},
		{
			path: '/dashboard',
			Component: Dashboard,
			children: [
				{
					path: 'home',
					Component: Home,
				},
				{
					path: 'admin',
					element: <Outlet />,
					children: [
						{ index: true, Component: Admin },
						{ path: 'files', Component: Files },
						{
							path: 'projects',
							element: <Outlet />,
							children: [
								{ index: true, Component: Projects },
								{
									path: ':id/edit',
									element: <CreateProject edit />,
								},
								{
									path: ':id/details',
									Component: ProjectDetailsScreen,
								},
								{
									path: 'create',
									Component: CreateProject,
								},
							],
						},

						{ path: 'otp-bypass', Component: OtpBypass },

						{ path: 'email-bypass', Component: EmailBypass },
						{ path: 'queries', Component: Queries },
						{ path: 'crm', Component: CRM },
						{ path: 'company-registerations', Component: CompanysTable },
						{
							path: 'company-details/:id',
							Component: CompanyDetailsScreen,
						},
						{ path: 'geo-tag', Component: Geotag },
						{ path: 'map', Component: Map },
						{ path: 'user-management', Component: UserManagement },
						{ path: 'biomass', Component: BiomassTypes },
						{ path: 'entity-management', Component: EntityManagement },
						{ path: 'pending-task', Component: PendingTask },
						{ path: 'add-constants', Component: AddConstants },
						{ path: 'settings', Component: Settings },
					],
				},
				{
					path: 'production',
					element: <Outlet />,
					children: [
						{ index: true, Component: Production },
						{
							path: 'biomass',
							element: <Outlet />,
							children: [{ index: true, Component: BiomassPage }],
						},
						{
							path: 'batches',
							element: <Outlet />,
							children: [
								{ index: true, Component: Batches },
								{
									path: ':id/details',
									Component: BatchDetails,
								},
							],
						},
						{ path: 'inventory', Component: Inventory },
						{ path: 'mixing', Component: Mixing },
						{ path: 'packaging', Component: Packaging },
					],
				},
				{
					path: 'c-sink-network',
					element: <Outlet />,
					children: [
						{ index: true, Component: CSinkNetwork },
						{
							path: ':cSinkNetworkId/details',
							Component: CSinkNetworkDetails,
						},
					],
				},

				{
					path: 'artisan-pro',
					element: <Outlet />,
					children: [
						{ index: true, Component: ArtisanProNetwork },
						// artisan pro details
						{
							path: ':artisanProId/details',
							Component: ArtisanProDetails,
						},
					],
				},
				{
					path: 'farmers',
					element: <Outlet />,
					children: [
						{ index: true, Component: Farmer },
						{ path: ':farmerId/details', Component: FarmerDetails },
						{ path: ':farmId/farm-details', Component: FarmDetails },
					],
				},
				{
					path: 'credits',
					element: <Outlet />,
					children: [
						{ index: true, Component: Credits },
						{
							path: 'projects',
							Component: InternalProjects,
						},
						{
							path: 'stocks/:stockId',
							Component: Stocks,
						},
						{
							path: 'projects/:projectId/details',
							Component: InternalProjectDetails,
						},
					],
				},
			],
		},
		{
			path: '/production',
			Component: Production,
		},
		{
			path: 'credits',
			element: <Outlet />,
			children: [
				{
					path: 'projects',
					Component: CreditsProjects,
				},
				{
					path: 'projects/:projectId/details',
					Component: CreditProjectDetails,
				},
			],
		},
		{
			path: 'account-management',
			element: <Outlet />,
			children: [
				{
					path: 'registration',
					Component: CompanyRegistration,
				},
				{
					path: 'validation',
					Component: CompanyValidation,
				},
			],
		},
		{
			path: '/credits/stocks/:stockId',
			Component: Stocks,
		},
	],
}

const DefaultRoute: RouteObject = {
	path: '/',
	element: <Navigate to={'/login'} />,
}

export const App = createBrowserRouter([
	{
		path: '/',
		Component: GlobalProvider,
		children: [DefaultRoute, AuthenticatedRoutes],
	},
])
