import {
	ActionInformationDrawer,
	CustomDataGrid,
	CustomHeader,
} from '@/components'
import {
	CustomTagComponent,
	TagComponentWithToolTip,
} from '@/components/CustomTagComponent'
import { MultipleAvatar } from '@/components/MultipleAvatar.tsx'
import { Add, ArrowLeftRounded, PlaceOutlined } from '@mui/icons-material'
import { TabContext, TabList, TabPanel } from '@mui/lab'
import DownloadIcon from '@/assets/icons/download.svg'
import artisanProIcon from '@/assets/icons/artisanProIcon.svg'
import siteIcon from '@/assets/icons/siteIcon.svg'

import {
	alpha,
	Avatar,
	Box,
	Button,
	FormControl,
	IconButton,
	InputLabel,
	Link,
	MenuItem,
	Select,
	Stack,
	styled,
	Tab,
	TextField,
	ToggleButton,
	ToggleButtonGroup,
	Tooltip,
	Typography,
	useTheme,
} from '@mui/material'
import {
	GridRenderCellParams,
	GridSearchIcon,
	GridColDef,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { useArtisanProDetails } from './useArtisanProDetails'
import { SiteList } from './SiteList'
import { Details, ImageURL, KilnOperator } from '@/interfaces'
import { useEffect, useMemo, useState } from 'react'
import EditIcon from '@/assets/icons/editIcon.svg'
import { ArtisanProNetworkDetailsChart } from '../ArtisianProNetwork'
import { EditNetwork, StrategyContent } from '@/components/EditNetwork'
import { AddSite } from '@/components/AddSite'
import {
	capitalizeFirstLetter,
	convertKgToTon,
	getGoogleMapLink,
} from '@/utils/helper'
import { FootprintDetails } from '@/components/FootprintDetails'
import { EntityTabEnum } from '@/utils/constant'
import { CsinkOperatorList } from '@/components/CsinkOperatorList'

export enum artisanTabEnum {
	sites = 'sites',
	buyers = 'buyers',
}
export function ArtisanProDetails() {
	const theme = useTheme()
	const [siteChartDetails, setSiteChartDetails] = useState<Details>()
	const [chartState, setChartState] = useState<string>('AP')
	const [openEditArtisanModal, setOpenEditArtisanModal] =
		useState<EntityTabEnum | null>(null)
	const [openAddSiteModal, setOpenAddSiteModal] = useState<boolean>(false)
	const [showFootprintDetails, setShowFootprintDetails] = useState(false)
	const [showMethaneStrategy, setShowMethaneStrategy] = useState(false)

	const [operatorsDrawer, setOperatorsDrawer] = useState<{
		open: boolean
		data: KilnOperator[]
	}>({
		open: false,
		data: [],
	})
	const [
		showBiomassPreProccessingStrategy,
		setShowBiomassPreProccessingStrategy,
	] = useState(false)

	const {
		allSitesQuery,
		navigate,
		handleTabChange,
		paramsTab,
		handleSiteTabChange,
		siteTab,
		artisanProDetails,
		getBuyersQuery,
		buyersList,
		selectedSiteDetails,
		artisanProsData,
		refetchAllDetails,
	} = useArtisanProDetails()

	const getCount = (length: number) => {
		if (length > 3) {
			return `+ ${length - 3}`
		}
		return ''
	}
	const MultipleAvatarWrapper: React.FC<{
		images: ImageURL[]
		length: number
	}> = ({ images, length }) => {
		return (
			<Stack direction='row' alignItems='center' columnGap={1}>
				<MultipleAvatar size={30} imageList={images} MaxAvatar={3} />
				<Typography variant='subtitle1'>{getCount(length)}</Typography>
			</Stack>
		)
	}
	const ToolTipComponent: React.FC<{
		dataArr: { name: string; description?: string }[]
	}> = ({ dataArr }) => {
		return (
			<Stack rowGap={2} padding={1}>
				{dataArr.map((data, index) => (
					<Stack key={`${data?.name}-${index}`}>
						<Typography>{data.name}</Typography>
						{data?.description ? (
							<Typography>({data?.description})</Typography>
						) : null}
					</Stack>
				))}
			</Stack>
		)
	}
	const batchDetailsList = useMemo(
		() => [
			{
				label: 'Preferred Biomass',
				value: artisanProsData?.details?.preferredCrops.length ? (
					<MultipleAvatarWrapper
						images={
							artisanProsData?.details?.preferredCrops?.map(
								(biomass) => biomass?.image
							) || []
						}
						length={artisanProsData?.details?.bags?.length}
					/>
				) : (
					0
				),
				tooltipComponent: (
					<ToolTipComponent
						dataArr={(artisanProsData?.details?.preferredCrops ?? [])?.map(
							(item) => ({
								name: item.name,
							})
						)}
					/>
				),
				showTooltip: !!artisanProsData?.details?.preferredCrops?.length,
				clickEvents: () =>
					setOpenEditArtisanModal(EntityTabEnum.preferredBiomass),
			},
			{
				label: 'Biomass Pre-Processing',
				value: (
					<Box
						component={Stack}
						onClick={() => setShowBiomassPreProccessingStrategy(true)}
						sx={{ cursor: 'pointer' }}>
						{(artisanProsData?.biomassPreprocessingDetails.dryingStrategy || '')
							?.length > 25
							? `Drying - ${artisanProsData?.biomassPreprocessingDetails?.dryingStrategy?.slice(
									0,
									25
							  )}...`
							: `Drying  ${
									artisanProsData?.biomassPreprocessingDetails
										?.dryingStrategy ?? ''
							  }`}
					</Box>
				),
				tooltipComponent: (
					<ToolTipComponent
						dataArr={[
							{
								name: `Drying - ${capitalizeFirstLetter(
									artisanProsData?.biomassPreprocessingDetails?.dryingType?.replace(
										'_',
										' '
									) || ''
								)}`,
								description:
									artisanProsData?.biomassPreprocessingDetails
										?.dryingStrategy || '',
							},
							{
								name: `Shredding - ${capitalizeFirstLetter(
									artisanProsData?.biomassPreprocessingDetails?.shreddingType?.replace(
										'_',
										' '
									) || ''
								)}`,
								description:
									artisanProsData?.biomassPreprocessingDetails
										?.shreddingStrategy || '',
							},
						]}
					/>
				),
				showTooltip: !!artisanProsData?.methaneCompensateStrategies?.length,
				clickEvents: () => setShowBiomassPreProccessingStrategy(true),
			},
			{
				label: 'Methane Compensation Strategy',
				value:
					Number(artisanProsData?.methaneCompensateStrategies?.length) > 1 ? (
						<Button
							onClick={() =>
								setOpenEditArtisanModal(
									EntityTabEnum.methaneCompensationStrategy
								)
							}
							variant='text'
							size='small'
							color='inherit'>
							View Details
						</Button>
					) : (
						<Box
							component={Stack}
							onClick={() =>
								setOpenEditArtisanModal(
									EntityTabEnum.methaneCompensationStrategy
								)
							}
							sx={{ cursor: 'pointer' }}>
							{(
								artisanProsData?.methaneCompensateStrategies?.[0]
									?.description || ''
							)?.length > 30
								? `${capitalizeFirstLetter(
										artisanProsData?.methaneCompensateStrategies?.[0]
											?.biomassName
								  )} ${capitalizeFirstLetter(
										artisanProsData?.methaneCompensateStrategies?.[0]?.methaneCompensateType?.replace(
											'_',
											' '
										)
								  )}  ${capitalizeFirstLetter(
										artisanProsData?.methaneCompensateStrategies?.[0]?.compensateType?.replace(
											'_',
											' '
										)
								  )}
													  ${artisanProsData?.methaneCompensateStrategies?.[0]?.description?.slice(
															0,
															30
														)}...`
								: `${capitalizeFirstLetter(
										artisanProsData?.methaneCompensateStrategies?.[0]
											?.biomassName
								  )} - ${capitalizeFirstLetter(
										artisanProsData?.methaneCompensateStrategies?.[0]?.methaneCompensateType?.replace(
											'_',
											' '
										)
								  )}  ${
										artisanProsData?.methaneCompensateStrategies?.[0]
											?.description || ''
								  }`}
						</Box>
					),
				tooltipComponent: (
					<ToolTipComponent
						dataArr={[
							{
								name: `${
									artisanProsData?.methaneCompensateStrategies?.[0]
										?.methaneCompensateType
										? `${capitalizeFirstLetter(
												artisanProsData?.methaneCompensateStrategies?.[0]
													?.biomassName
										  )} - ${capitalizeFirstLetter(
												artisanProsData?.methaneCompensateStrategies?.[0]?.methaneCompensateType?.replace(
													'_',
													' '
												)
										  )}`
										: ''
								}`,

								description: artisanProsData?.methaneCompensateStrategies?.[0]
									?.methaneCompensateType
									? capitalizeFirstLetter(
											artisanProsData?.methaneCompensateStrategies?.[0]?.description?.replace(
												'_',
												' '
											)
									  )
									: '',
							},
						]}
					/>
				),
				showTooltip: artisanProsData?.methaneCompensateStrategies?.length === 1,
				clickEvents: () =>
					setOpenEditArtisanModal(EntityTabEnum.methaneCompensationStrategy),
			},
		],
		[
			MultipleAvatarWrapper,
			artisanProsData?.biomassPreprocessingDetails.dryingStrategy,
			artisanProsData?.biomassPreprocessingDetails?.dryingType,
			artisanProsData?.biomassPreprocessingDetails?.shreddingStrategy,
			artisanProsData?.biomassPreprocessingDetails?.shreddingType,
			artisanProsData?.details?.bags,
			artisanProsData?.details?.preferredCrops,
			artisanProsData?.methaneCompensateStrategies,
		]
	)
	const hideChartByLength = (length?: number) => {
		return Number(length) > 1 ? false : true
	}
	const batchDetailsListChart = useMemo(
		() => [
			{
				label: 'Biomass',
				value: (
					<ArtisanProNetworkDetailsChart
						hidden={
							chartState === 'AP'
								? hideChartByLength(artisanProsData?.details?.biomass?.length)
								: hideChartByLength(siteChartDetails?.biomass?.length)
						}
						data={
							chartState === 'AP'
								? {
										labels:
											artisanProsData?.details?.biomass?.map(
												(biomass) => biomass.cropName
											) || [],
										datasets:
											artisanProsData?.details?.biomass?.map((biomass) =>
												convertKgToTon(biomass.biomassProduced)
											) || [],
										extraDataSet: artisanProsData?.details?.biomass?.map(
											(item) => convertKgToTon(item.biomassAvailable)
										),
										showBiomassTooltip: true,
								  }
								: {
										labels:
											siteChartDetails?.biomass?.map(
												(biomass) => biomass.cropName
											) || [],
										datasets:
											siteChartDetails?.biomass.map(
												(biomass) => biomass.biomassProduced
											) || [],
										extraDataSet: siteChartDetails?.biomass?.map(
											(item) => item.biomassAvailable
										),
										showBiomassTooltip: true,
								  }
						}
					/>
				),
				hidden:
					chartState === 'AP'
						? !artisanProsData?.details?.biomass?.length
						: !siteChartDetails?.biomass?.length,
			},
			{
				label: 'Biochar',
				value: (
					<ArtisanProNetworkDetailsChart
						hidden={
							chartState === 'AP'
								? hideChartByLength(artisanProsData?.details?.biochar?.length)
								: hideChartByLength(siteChartDetails?.biochar?.length)
						}
						data={
							chartState === 'AP'
								? {
										labels:
											artisanProsData?.details?.biochar.map(
												(biochar) => biochar.cropName
											) || [],
										datasets:
											artisanProsData?.details?.biochar.map(
												(biochar) => biochar.biocharProducedInTonne
											) || [],
										showUnit: 'ton',
								  }
								: {
										labels:
											siteChartDetails?.biochar.map(
												(biochar) => biochar.cropName
											) || [],
										datasets:
											siteChartDetails?.biochar.map(
												(biochar) => biochar.biocharProducedInTonne
											) || [],
										showUnit: 'ton',
								  }
						}
					/>
				),
				hidden:
					chartState === 'AP'
						? !artisanProsData?.details?.biochar?.length
						: !siteChartDetails?.biochar?.length,
			},
			{
				label: 'Carbon Credits',
				value: (
					<ArtisanProNetworkDetailsChart
						hidden={
							chartState === 'AP'
								? hideChartByLength(
										artisanProsData?.details?.carbonCredits?.length
								  )
								: hideChartByLength(siteChartDetails?.carbonCredits?.length)
						}
						data={
							chartState === 'AP'
								? {
										labels:
											artisanProsData?.details?.carbonCredits.map(
												(carbonCredits) => carbonCredits.cropName
											) || [],
										datasets:
											artisanProsData?.details?.carbonCredits.map(
												(carbonCredits) => carbonCredits.carbonCredits
											) || [],
										showUnit: 'tCo2',
								  }
								: {
										labels:
											siteChartDetails?.carbonCredits.map(
												(carbonCredits) => carbonCredits.cropName
											) || [],
										datasets:
											siteChartDetails?.carbonCredits.map(
												(carbonCredits) => carbonCredits.carbonCredits
											) || [],
										showUnit: 'tCo2',
								  }
						}
					/>
				),
				hidden:
					chartState === 'AP'
						? !artisanProsData?.details?.carbonCredits?.length
						: !siteChartDetails?.carbonCredits?.length,
			},
		],
		[
			artisanProsData?.details?.biochar,
			artisanProsData?.details?.biomass,
			artisanProsData?.details?.carbonCredits,
			chartState,
			siteChartDetails?.biochar,
			siteChartDetails?.biomass,
			siteChartDetails?.carbonCredits,
		]
	)
	const buyersGridColumn: GridColDef<GridValidRowModel>[] = [
		{
			field: 'name',
			headerName: `Buyer's Name`,
			minWidth: 180,
			flex: 1,
			renderCell: (params: GridRenderCellParams) => {
				const number =
					params?.row?.countryCode && params?.row?.number
						? `(${params?.row?.countryCode}-${params?.row?.number})`
						: ''
				return (
					<Stack>
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params?.row?.name ?? ''}
						</Typography>
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{number ?? ''}
						</Typography>
					</Stack>
				)
			},
		},

		{
			field: 'address',
			headerName: 'Location',
			minWidth: 150,
			flex: 1,
			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{params?.row?.postalLocation ?? ''}
					</Typography>
				)
			},
		},
		{
			field: '',
			headerName: 'Detailed Address',
			minWidth: 210,
			flex: 1,
			headerAlign: 'center',
			align: 'center',
			renderCell: (params: GridRenderCellParams) => {
				const detailedAddress = `${params?.row?.district} (${params?.row?.state}) ${params?.row?.pinCode}`

				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{detailedAddress ?? ''}
					</Typography>
				)
			},
		},
	]

	const GridHeaderComponents = () => {
		return (
			<Stack className='grid-header-component' columnGap={2}>
				<TextField
					label='Search'
					name='search'
					variant='outlined'
					className='search-textFiled'
					InputProps={{
						startAdornment: <GridSearchIcon />,
					}}
				/>

				<FormControl className='form-controller'>
					<InputLabel id='tag'>Tag</InputLabel>
					<Select labelId='tag' label='tag'>
						<MenuItem value={'active'}>Active</MenuItem>
						<MenuItem value={'archived'}>Archived</MenuItem>
					</Select>
				</FormControl>
				<FormControl className='form-controller'>
					<InputLabel id='status'>Status</InputLabel>
					<Select labelId='status' label='status'>
						<MenuItem value={'active'}>Active</MenuItem>
						<MenuItem value={'archived'}>Archived</MenuItem>
					</Select>
				</FormControl>
			</Stack>
		)
	}

	useEffect(() => {
		if (!openEditArtisanModal || !openAddSiteModal) {
			return () => {
				refetchAllDetails()
			}
		}
	}, [openEditArtisanModal, openAddSiteModal])

	const disableEditandAdd = useMemo(() => {
		return (
			artisanProDetails?.isCsinkManagerSuspended ||
			artisanProDetails?.isBiomassAggregatorSuspended ||
			artisanProDetails?.isArtisanProNetworkSuspended ||
			artisanProDetails?.isArtisanProSuspended
		)
	}, [
		artisanProDetails?.isArtisanProSuspended,
		artisanProDetails?.isArtisanProNetworkSuspended,
		artisanProDetails?.isBiomassAggregatorSuspended,
		artisanProDetails?.isCsinkManagerSuspended,
	])

	return (
		<>
			<ActionInformationDrawer
				open={operatorsDrawer.open}
				onClose={() => setOperatorsDrawer({ open: false, data: [] })}
				anchor='right'
				component={
					<CsinkOperatorList
						onClose={() => setOperatorsDrawer({ open: false, data: [] })}
						operatorsData={operatorsDrawer.data}
						heading='Artisan Pro Operators List'
					/>
				}
			/>

			<StyledContained>
				<Box className='header'>
					<CustomHeader
						headingComponent={
							<Stack direction='row' alignItems='center'>
								<Button
									variant='text'
									startIcon={<ArrowLeftRounded />}
									className='back-button'
									onClick={() => navigate(-1)}>
									Artisan Pro
								</Button>
								<Typography variant='h6' color={theme.palette.neutral['500']}>
									&nbsp;/ {artisanProsData?.artisianProNetworkName} &nbsp;/
									{artisanProsData?.shortCode}
								</Typography>
							</Stack>
						}
						endComponent={
							<Box display='flex' alignItems='center' gap={2}>
								{disableEditandAdd && (
									<Typography color='primary' variant='caption'>
										This Artisan pro is suspended, that is why you can not Add/
										Edit anything
									</Typography>
								)}
								<Button
									size='small'
									variant='contained'
									startIcon={<Add />}
									// disabled={disableEditandAdd}
									onClick={() => setOpenAddSiteModal(true)}>
									Add Site{' '}
								</Button>
								<Button
									variant='outlined'
									className='edit_btn'
									startIcon={<Box component='img' src={EditIcon} />}
									onClick={() => setOpenEditArtisanModal(EntityTabEnum.bags)}>
									Edit
								</Button>
								<IconButton
									onClick={() => console.log('hi')}
									sx={{
										border: `${theme.spacing(0.125)} solid ${
											theme.palette.neutral['100']
										}`,
										borderRadius: theme.spacing(1.25),
									}}>
									<Box
										component='img'
										src={DownloadIcon}
										height={20}
										width={20}
									/>
								</IconButton>
							</Box>
						}
					/>
				</Box>

				<Stack className='hero-section'>
					<Stack className='network-details-container'>
						<Stack
							display='flex'
							flexDirection='column'
							gap={2}
							sx={{ mt: 2, ml: 1 }}>
							<Stack sx={{ minWidth: '160px' }}>
								<Typography variant='body2' textTransform='capitalize'>
									{artisanProsData?.name ?? ''}
								</Typography>
								<Typography
									variant='overline'
									textTransform='none'>{`Artisan pro ID: ${
									artisanProsData?.shortCode ?? ''
								}`}</Typography>
							</Stack>

							<Stack>
								<CustomTagComponent
									label='Address'
									lighterHeading
									value={
										<Stack flexDirection={'row'} gap={0.5}>
											{artisanProsData?.location ? (
												<Stack
													direction='row'
													alignItems='center'
													component={Link}
													target='_blank'
													underline='hover'
													textTransform='none'
													color='common.black'
													href={getGoogleMapLink(
														`${artisanProsData?.location?.x}`,
														`${artisanProsData?.location?.y}`
													)}>
													<PlaceOutlined
														color='primary'
														sx={{ width: 20, height: 20 }}
													/>
												</Stack>
											) : null}
											<Typography variant='subtitle1'>
												{artisanProsData?.address ?? '-'}
											</Typography>
										</Stack>
									}
								/>
								<CustomTagComponent
									label='Emissions'
									lighterHeading
									value={
										<Box
											className='cursor-pointer'
											component={Stack}
											onClick={() => setShowFootprintDetails(true)}>
											<Typography variant='subtitle1'>
												{`Transportation : ${
													Number(
														artisanProsData?.totalCarbonEmissionInGram || 0
													) / 1000
												} kgCO2e`}
											</Typography>
										</Box>
									}
								/>
								<CustomTagComponent
									label='Admin'
									value={
										<Box
											component={Stack}
											alignItems='end'
											gap={1}
											onClick={() =>
												setOpenEditArtisanModal(EntityTabEnum.admins)
											}
											sx={{
												cursor: 'pointer',
											}}>
											<Tooltip
												title={
													<Stack direction='column'>
														{(artisanProsData?.managerDetails ?? [])?.map(
															(item) => (
																<Typography
																	key={item?.managerId}
																	variant='caption'>
																	{item?.managerName} {item?.managerEmail}
																	<br />
																	{item?.managerPhone
																		? `(${item?.countryCode}-${item?.managerPhone})`
																		: ''}
																</Typography>
															)
														)}
													</Stack>
												}
												placement='bottom-start'
												arrow>
												<Box>
													<MultipleAvatarWrapper
														images={
															artisanProsData?.managerDetails?.map(
																(manager) => manager?.profileImageUrl
															) || []
														}
														length={
															artisanProsData?.managerDetails?.length || 0
														}
													/>
												</Box>
											</Tooltip>
										</Box>
									}
									lighterHeading
								/>
								<CustomTagComponent
									label='Operator'
									value={
										<Tooltip
											title={(artisanProsData?.operatorDetails ?? [])?.map(
												(item) => (
													<Typography key={item?.id} textAlign='center'>
														{item?.name}
														<br />
														{item?.phoneNo
															? `(${item?.countryCode}-${item?.phoneNo})`
															: ''}
													</Typography>
												)
											)}
											placement='bottom-start'
											sx={{
												cursor: 'pointer',
											}}
											onClick={() => {
												setOperatorsDrawer({
													open: true,
													data: artisanProsData?.operatorDetails || [],
												})
											}}
											arrow>
											<Box>
												<MultipleAvatarWrapper
													images={
														artisanProsData?.operatorDetails?.map(
															(operator) => operator?.profileImageUrl
														) || []
													}
													length={artisanProsData?.operatorDetails?.length || 0}
												/>
											</Box>
										</Tooltip>
									}
									lighterHeading
								/>
							</Stack>
						</Stack>
						<Stack className='main_container' flex={1}>
							<Stack className='detail-container'>
								<Typography variant='h5' sx={{ mt: 2 }}>
									Artisan pro detail
								</Typography>
								<Stack className='sub-section'>
									{batchDetailsList.map(
										(
											{
												label,
												value,
												tooltipComponent,
												showTooltip,
												clickEvents,
											},
											index
										) => (
											<Stack key={index} className='desc-grid-item'>
												<Stack className='desc'>
													<TagComponentWithToolTip
														lighterHeading
														label={label}
														value={value}
														tooltipComponent={tooltipComponent}
														showTooltip={showTooltip}
														clickEvents={clickEvents}
													/>
												</Stack>
											</Stack>
										)
									)}
								</Stack>
								<Stack flexDirection='row' paddingTop={theme.spacing(2.5)}>
									<Box>
										<ToggleButtonGroup
											exclusive
											size='small'
											value={chartState}>
											<ToggleButton
												title='Artisan Pro'
												sx={{
													backgroundColor:
														chartState === 'AP'
															? theme.palette.action.selected
															: 'transparent',
												}}
												onClick={() => setChartState('AP')}
												value='map'>
												<Box
													component='img'
													src={artisanProIcon}
													height={24}
													width={24}
												/>
											</ToggleButton>
											<ToggleButton
												title='Site'
												sx={{
													backgroundColor:
														chartState === 'Sites'
															? theme.palette.action.selected
															: 'transparent',
												}}
												onClick={() => setChartState('Sites')}
												value='list'>
												<Box
													component='img'
													src={siteIcon}
													height={24}
													width={24}
												/>
											</ToggleButton>
										</ToggleButtonGroup>
									</Box>
									<Stack className='charts-container'>
										{batchDetailsListChart.map(
											({ label, value, hidden }, index) =>
												!hidden && (
													<Stack key={index} className='charts-subconatiner'>
														<Typography
															variant='subtitle2'
															sx={{ color: theme.palette.neutral[300], p: 1 }}>
															{label}
														</Typography>
														<Box>{value}</Box>
													</Stack>
												)
										)}
									</Stack>
								</Stack>
							</Stack>
						</Stack>
					</Stack>
				</Stack>
				<Stack className='container'>
					<TabContext value={paramsTab}>
						<Box sx={{ borderBottom: 0, borderColor: 'divider' }}>
							<Stack
								flexDirection='row'
								justifyContent='space-between'
								alignItems='center'>
								<Stack flexDirection='row' gap={theme.spacing(3.5)}>
									<TabList onChange={handleTabChange}>
										<Tab
											label={'Sites'}
											value={artisanTabEnum.sites}
											sx={{
												position: 'relative',
												minWidth: theme.spacing(12),
											}}
											icon={
												<Avatar
													sx={{
														position: 'absolute',
														right: 2,
														top: 5,
														height: theme.spacing(2.5),
														fontSize: theme.spacing(1.5),
														width: theme.spacing(2.5),
														backgroundColor:
															paramsTab === artisanTabEnum.sites
																? theme.palette.primary.main
																: '',
													}}>
													{artisanProDetails?.siteCount ?? 0}
												</Avatar>
											}
											iconPosition='top'
										/>
										<Tab
											label={'Buyers'}
											value={artisanTabEnum.buyers}
											sx={{
												position: 'relative',
												minWidth: theme.spacing(12),
											}}
											icon={
												<Avatar
													sx={{
														position: 'absolute',
														right: 2,
														top: 5,
														height: theme.spacing(2.5),
														fontSize: theme.spacing(1.5),
														width: theme.spacing(2.5),
														backgroundColor:
															paramsTab === artisanTabEnum.buyers
																? theme.palette.primary.main
																: '',
													}}>
													{artisanProDetails?.buyerCount ?? 0}
												</Avatar>
											}
											iconPosition='top'
										/>
									</TabList>
								</Stack>
							</Stack>
							<TabPanel value={artisanTabEnum.sites} sx={{ p: 0 }}>
								<Stack
									className='details-header'
									flexDirection='row'
									padding={theme.spacing(1, 1, 1, 0)}
									gap={theme.spacing(2)}>
									<Stack className='sitelist-container'>
										{allSitesQuery?.data?.siteList?.map((item) => (
											<Button
												key={item?.id}
												sx={{
													borderRadius: theme.spacing(1.5),
													justifyContent: 'flex-start',
													...(siteTab === item?.id
														? {
																backgroundColor: theme.palette.error.light,
														  }
														: { color: '' }),
													minWidth: theme.spacing(28),
												}}
												onClick={(e) => handleSiteTabChange(e, item)}>
												<Stack
													flexDirection='column'
													padding={theme.spacing(1.2)}>
													<Typography
														variant='subtitle2'
														sx={{
															color:
																siteTab === item?.id
																	? theme.palette.primary.light
																	: theme.palette.neutral[500],
														}}>
														{item?.name}
													</Typography>
												</Stack>
											</Button>
										))}
									</Stack>
									<SiteList
										open={!allSitesQuery?.data?.siteList?.length}
										siteDetails={selectedSiteDetails}
										artisanProDetails={artisanProDetails}
										// disableEditandAdd={disableEditandAdd}
										setSiteChartDetails={setSiteChartDetails}
									/>
								</Stack>
							</TabPanel>

							<TabPanel value={artisanTabEnum.buyers}>
								<CustomDataGrid
									showPagination={true}
									rows={buyersList ?? []}
									columns={buyersGridColumn}
									rowCount={getBuyersQuery?.data?.count ?? 0}
									headerComponent={<GridHeaderComponents />}
									sx={{
										border: `1px solid ${theme.palette.neutral['100']}`,
									}}
								/>
							</TabPanel>
						</Box>
					</TabContext>
				</Stack>
				{openEditArtisanModal ? (
					<ActionInformationDrawer
						open={!!openEditArtisanModal}
						onClose={() => setOpenEditArtisanModal(null)}
						anchor='right'
						component={
							<EditNetwork
								isCsink={false}
								openTab={openEditArtisanModal}
								handleClose={() => setOpenEditArtisanModal(null)}
								artisanProDetails={artisanProDetails}
							/>
						}
					/>
				) : null}
				{showBiomassPreProccessingStrategy ? (
					<ActionInformationDrawer
						open={showBiomassPreProccessingStrategy}
						onClose={() => setShowBiomassPreProccessingStrategy(false)}
						anchor='right'
						component={
							<StrategyContent
								handleClose={() => setShowBiomassPreProccessingStrategy(false)}
								type={EntityTabEnum.biomassProcessingStrategy}
								artisanProDetails={artisanProDetails}
							/>
						}
					/>
				) : null}
				{showMethaneStrategy ? (
					<ActionInformationDrawer
						open={showMethaneStrategy}
						onClose={() => setShowMethaneStrategy(false)}
						anchor='right'
						component={
							<StrategyContent
								handleClose={() => setShowMethaneStrategy(false)}
								type={EntityTabEnum.methaneCompensationStrategy}
								artisanProDetails={artisanProDetails}
							/>
						}
					/>
				) : null}
				{showFootprintDetails ? (
					<ActionInformationDrawer
						open={showFootprintDetails}
						onClose={() => setShowFootprintDetails(false)}
						anchor='right'
						component={
							<FootprintDetails
								footprint={artisanProsData?.details?.footprints}
								onClose={() => setShowFootprintDetails(false)}
								open={showFootprintDetails}
							/>
						}
					/>
				) : null}
				{openAddSiteModal ? (
					<ActionInformationDrawer
						open={openAddSiteModal}
						onClose={() => setOpenAddSiteModal(false)}
						anchor='right'
						component={
							<AddSite handleClose={() => setOpenAddSiteModal(false)} />
						}
					/>
				) : null}
			</StyledContained>
		</>
	)
}

const StyledContained = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.cursor-pointer': {
		cursor: 'pointer',
	},
	'.view-all': {
		...theme.typography.subtitle2,
		padding: 0,
	},
	'.header': {
		display: 'flex',
		alignItems: 'center',
		padding: theme.spacing(2.5, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		'.heading': {
			color: theme.palette.neutral[500],
		},
	},
	'.hero-section': {
		padding: theme.spacing(2, 2, 0),
		// background: theme.palette.neutral['50'],
		'.network-details-container': {
			flexDirection: 'row',
			alignItems: 'flex-start',
			gap: theme.spacing(8),
			border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(2),
			background: theme.palette.common.white,
			padding: theme.spacing(2),
			'.customTag': {
				'.pointer': {
					cursor: 'pointer',
				},
				paddingLeft: 0,
				alignItems: 'flex-start',
			},
		},
	},
	'.main_container': {
		padding: theme.spacing(0),
		'.detail-container': {
			padding: theme.spacing(0, 2.5),
			[theme.breakpoints.down('md')]: {
				flexWrap: 'wrap',
				rowGap: theme.spacing(1),
			},
			width: '100%',
			height: '100%',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.35)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
			'.charts-container': {
				marginLeft: '-24px', // according to UI
				padding: theme.spacing(4, 1),
				gap: theme.spacing(6),
				justifyContent: 'space-evenly',
				flexWrap: 'wrap',
				width: '100%',
				display: 'grid',
				gridTemplateColumns: 'repeat(4, 1fr)',
				'.charts-subconatiner': {
					display: 'flex',
					flexDirection: 'column',
					alignItems: 'center',
				},
			},

			'.sub-section': {
				width: '100%',
				display: 'grid',
				gridTemplateColumns: 'repeat(3, 1fr)',
			},

			'.apex-tooltip': {
				padding: theme.spacing(1),
				display: 'flex',
				flexDirection: 'column',
				p: {
					margin: 0,
				},
			},
			'.desc-grid-item': {
				'.desc': {
					'.title': {
						color: theme.palette.neutral['300'],
					},
				},
			},
		},
	},
	'.details-header': {
		padding: theme.spacing(2),
		border: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']}`,
		'.sitelist-container': {
			gap: theme.spacing(1),
			maxHeight: theme.spacing(100),
			overflowX: 'hidden',
			overflowY: 'auto',
			'& ::-webkit-scrollbar': {
				display: 'none',
			},
			'-ms-overflow-style': 'none' /* IE and Edge */,
			'scrollbar-width': 'none' /* Firefox */,
		},
		'.site_btn': {
			maxWidth: theme.spacing(25),
			overflow: 'hidden',
			minWidth: theme.spacing(10),
			whiteSpace: 'nowrap',
			textOverflow: 'ellipsis',
			display: 'block',
		},
		'.trained_btn': {
			...theme.typography.caption,
		},
		'.trained_success': {
			background: theme.palette.success.light,
			color: theme.palette.success.main,
		},
		'.trained_error': {
			background: theme.palette.error.light,
			color: theme.palette.error.main,
		},
		'.customTag': {
			padding: 0,
		},
		'.map-container': {
			height: 220,
			width: 640,
			padding: theme.spacing(0, 2),
			borderRadius: theme.spacing(2),
		},
	},

	'.container': {
		marginTop: theme.spacing(2),
		'.grid-header-component': {
			width: '100%',
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				minWidth: 334,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(12),
				width: '100%',
				'.MuiOutlinedInput-notchedOutline': {
					borderRadius: theme.spacing(1.25),
				},
			},
		},
	},
	'.edit_btn': {
		textTransform: 'none',
		borderColor: theme.palette.neutral[300],
		color: theme.palette.neutral[300],
		borderRadius: 8,
	},
	'.site_list': {
		'::-webkit-scrollbar': {
			display: 'none',
		},
	},
}))
