import { authAxios, useAuthContext } from '@/contexts'
import {
	Farm,
	GetFarmerListResponse,
	IArtisanProDetails,
	ILocation,
	ISite,
	ISiteList,
} from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { SyntheticEvent, useCallback, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { artisanTabEnum } from '../ArtisanProDetails'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'

export const useSiteList = () => {
	const { artisanProId } = useParams()
	const navigate = useNavigate()
	const [searchParams, setSearchParams] = useSearchParams()
	const [isActionInfoDrawer, setIsActionInfoDrawer] = useState(false)
	const [showConfirmationDialog, setShowConfirmationDialog] = useState(false)
	const [showSuspendConfirmationDialog,setShowSuspendConfirmationDialog] = useState(false);
	const { userDetails } = useAuthContext()
	const queryClient = useQueryClient()

	const [isPackagingDrawer, setPackagingDrawer] = useState(false)
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsPage = searchParams.get('page') || defaultPage

	const paramsTab = searchParams.get('tab') || artisanTabEnum.sites

	const siteTab = searchParams.get('siteTab') || ''

	const handleTabChange = useCallback(
		(e: SyntheticEvent, newValue: string) => {
			e.preventDefault()
			setSearchParams(
				(prev) => {
					prev.set('tab', newValue)
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const getSiteDetails = async () => {
		if (!siteTab) return
		try {
			const { data } = await authAxios.get<ISite>(
				`/artisian-pro/${artisanProId}/site/${siteTab}`
			)
			return data
		} catch (err: any) {
			toast(err?.response?.data?.messageToUser ?? 'something went wrong')
		}
	}

	const sitedetailsQuery = useQuery({
		queryKey: ['siteDetails', artisanProId, siteTab, paramsLimit, paramsPage],
		queryFn: getSiteDetails,
		enabled: !!siteTab,
	})

	const getFarmers = async () => {
		const { data } = await authAxios.get<GetFarmerListResponse>(
			`/artisian-pro/${artisanProId}/farmers?limit=${paramsLimit}&page=${paramsPage}`
		)
		return data
	}

	const farmerListQuery = useQuery({
		queryKey: ['getArtisanFarmers', artisanProId, paramsLimit, paramsPage],
		queryFn: getFarmers,
		enabled: !!siteTab,
	})

	const getFarmList = useCallback(async () => {
		const { data } = await authAxios.get<{ farms: Farm[] }>(
			`/new/artisan-pro/${artisanProId}/farms`
		)
		return data
	}, [artisanProId])

	const farmListQuery = useQuery({
		queryKey: ['getArtisanfarmerList', artisanProId, paramsLimit, paramsPage],
		queryFn: getFarmList,
		select: (data) => {
			return data.farms?.map((farm) => ({
				position: {
					lat: farm?.location?.x || 0,
					lng: farm?.location?.y || 0,
				},
				name: farm?.landmark || '',
				id: farm?.id,
				desc: farm?.fieldSize
					? `(${farm?.fieldSize} ${farm?.fieldSizeUnit})`
					: '',
			}))
		},
		enabled: !!artisanProId && !!siteTab,
	})

	const handleSiteTabChange = useCallback(
		(e: SyntheticEvent, newValue: string) => {
			e.preventDefault()
			setSearchParams(
				(prev) => {
					prev.set('siteTab', newValue)
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const handleSaveKmlMutation = useMutation({
		mutationKey: ['SaveKml'],
		mutationFn: async (mapData: ILocation[]) => {
			const networkId = searchParams.get('networkId')
			if (!networkId) {
				return
			}
			const payload = {
				kmlCoordinates: mapData,
			}
			const api = `artisian-pro/${artisanProId}/site/${siteTab}/kml-coordinates`
			await authAxios.put(api, payload)
		},
		onSuccess: () => {
			sitedetailsQuery.refetch()
			toast('KML added')
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { userToMessage: string })?.userToMessage)
		},
	})
	const handleDeleteSiteMutation = useMutation({
		mutationKey: ['deleteSite'],
		mutationFn: async () => {
			const api = `artisian-pro/${artisanProId}/site/${siteTab}`
			await authAxios.delete(api)
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: ['allSites'],
			})
			setSearchParams(
				(prev) => {
					prev.delete('siteTab')
					return prev
				},
				{ replace: true }
			)

			setShowConfirmationDialog(false)

			toast('Site deleted')
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { userToMessage: string })?.userToMessage)
		},
	})

	const handleSuspendSiteMutation = useMutation({
		mutationKey: ['suspendSite'],
		mutationFn: async () => {
			const api = `artisian-pro/${artisanProId}/site/${siteTab}/toggle-suspend?isSuspend=${!sitedetailsQuery?.data?.isSuspended}`
			await authAxios.put(api);
		},
		onSuccess: () => {
			
			sitedetailsQuery.refetch()

			setShowSuspendConfirmationDialog(false)

			if(sitedetailsQuery.data?.isSuspended) {
				toast('Site Active');
			}else{
				toast('Site Suspended');
			}
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { userToMessage: string })?.userToMessage)
		},

	})

	const handleSaveKml = useCallback(
		async (mapData: ILocation[]) => {
			await handleSaveKmlMutation.mutateAsync(mapData)
		},
		[handleSaveKmlMutation]
	)

	const handleViewBatchesClick = (
		siteDetails: ISiteList | undefined,
		atisanProDetails: IArtisanProDetails | undefined
	) => {
		const redidrectTo = `/dashboard/production/batches?baId=${
			atisanProDetails?.biomassAggregatorId ?? ''
		}&&isArtisan=true&networkId=${siteDetails?.artisianProID}&siteId=${
			siteDetails?.id
		}`
		navigate(redidrectTo)
	}

	const refetchAllDetails = async () => {
		sitedetailsQuery.refetch()
	}
	return {
		sitedetailsQuery,
		navigate,
		isActionInfoDrawer,
		farmerListQuery,
		setIsActionInfoDrawer,
		farmerList: farmerListQuery?.data?.farmers,
		farmListQuery,
		isPackagingDrawer,
		setPackagingDrawer,
		handleTabChange,
		paramsTab,
		setSearchParams,
		handleSiteTabChange,
		handleSaveKml,
		siteTab,
		handleViewBatchesClick,
		refetchAllDetails,
		showConfirmationDialog,
		setShowConfirmationDialog,
		showSuspendConfirmationDialog,
		setShowSuspendConfirmationDialog,
		handleDeleteSite: () => handleDeleteSiteMutation.mutate(),
		handleSuspendSite: () => handleSuspendSiteMutation.mutate(),
		userDetails,
	}
}
