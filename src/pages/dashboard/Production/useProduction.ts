import { authAxios, useAuthContext } from '@/contexts'
import {
	GetBioCharMixingResponse,
	GetBioCharProducedResponse,
	GetProductionGraphDataResponse,
	PeriodEnum,
} from '@/interfaces'
import {
	IQuantityData,
	MeasuringUnits,
	NetworkTabs,
	SiteKilnResponse,
} from '@/types'
import { userRoles } from '@/utils/constant'
import { convertLitreToMeterCube, roundNumber } from '@/utils/helper'
import { useQuery } from '@tanstack/react-query'
import moment from 'moment'
import { useCallback, useMemo, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

import { format, parseISO } from 'date-fns'

export const useProduction = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const { userDetails } = useAuthContext()
	const graphTab = searchParams.get('graphTab') || 'biomass'
	const paramsNetworkTab = searchParams.get('networkTab') || NetworkTabs.all
	const networkIds = searchParams.get('networkIds') || ''
	const siteIds = searchParams.get('siteId') || ''
	const [unit, setUnit] = useState<MeasuringUnits>(MeasuringUnits.m3)
	const paramsPeriod = searchParams.get('period') || PeriodEnum.mtd
	const paramsBaId = searchParams.get('biomassAggregatorIds') || ''
	const paramsSiteId = searchParams.get('siteId') || ''
	const DATE_FORMAT = 'DD-MM-YYYY'
	const INTERNAL_DATE_FORMAT = 'YYYY-MM-DD'
	const paramsStartDate = searchParams.get('startDate')
		? moment(searchParams.get('startDate'), DATE_FORMAT).format(
				INTERNAL_DATE_FORMAT
		  )
		: ''
	const paramsEndDate = searchParams.get('endDate')
		? moment(searchParams.get('endDate'), DATE_FORMAT).format(
				INTERNAL_DATE_FORMAT
		  )
		: ''
	const paramsChartType = searchParams.get('chartType') || 'bar'

	const paramsValues: { [key: string]: { value: string; key: string } } =
		useMemo(
			() => ({
				[NetworkTabs.all]: {
					value: paramsBaId,
					key: 'biomassAggregatorIds',
				},
				[NetworkTabs.artisanPro]: {
					value: paramsSiteId,
					key: 'siteId',
				},
				[NetworkTabs.network]: {
					value: paramsSiteId,
					key: 'siteId',
				},
			}),
			[paramsBaId, paramsSiteId]
		)

	const getBioCharProduced = useQuery({
		queryKey: [
			'bioCharProduced',
			paramsNetworkTab,
			paramsValues,
			paramsBaId,
			paramsNetworkTab,
			siteIds,
			networkIds,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				networkIds: networkIds,
				subNetwork: paramsNetworkTab === 'all' ? '' : paramsNetworkTab,
				[paramsValues[paramsNetworkTab]?.key]:
					paramsValues[paramsNetworkTab]?.value,
			})
			const { data } = await authAxios.get<GetBioCharProducedResponse>(
				`/new/biochar-produced?${queryParams.toString()}`
			)
			return data
		},
	})
	const getBioCharMixing = useQuery({
		queryKey: [
			'bioCharMixing',
			paramsNetworkTab,
			paramsValues,
			paramsBaId,
			networkIds,
			siteIds,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				networkIds: networkIds,

				subNetwork: paramsNetworkTab === 'all' ? '' : paramsNetworkTab,
				[paramsValues[paramsNetworkTab]?.key]:
					paramsValues[paramsNetworkTab]?.value,
			})
			const { data } = await authAxios.get<GetBioCharMixingResponse>(
				`/new/biochar-mixing?${queryParams.toString()}`
			)
			return data
		},
	})

	const getQuantityData = useQuery({
		queryKey: [
			'quantityData',
			paramsNetworkTab,
			paramsValues,
			paramsBaId,
			siteIds,
			networkIds,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				networkIds: networkIds,
				subNetwork: paramsNetworkTab === 'all' ? '' : paramsNetworkTab,
				[paramsValues[paramsNetworkTab]?.key]:
					paramsValues[paramsNetworkTab]?.value,
			})
			const { data } = await authAxios.get<IQuantityData>(
				`new/biochar-details?${queryParams.toString()}`
			)
			return data
		},
	})

	const fetchSites = useQuery({
		queryKey: ['fetchSites', paramsNetworkTab],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				subNetwork: paramsNetworkTab,
			})
			const { data } = await authAxios.get<SiteKilnResponse>(
				`/new/sites-kilns?${queryParams.toString()}`
			)
			return data
		},
		select: (data) => {
			return (data?.siteKilns ?? []).map((item) => ({
				value: (item.isArtisan ? item?.siteId : item?.kilnId) || '',
				label: (item.isArtisan ? item.siteName : item.kilnName) || '',
			}))
		},
		enabled: paramsNetworkTab !== 'all',
	})

	const fetchBA = useQuery({
		queryKey: ['allBAForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<{
				biomassAggregators: { id: string; name: string; shortName: string }[]
			}>(`/biomass-aggregator?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.biomassAggregators?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled: [userRoles.Admin, userRoles.CsinkManager].includes(
			userDetails?.accountType as userRoles // need to add more account permissions
		),
	})

	const getGraphData = useQuery({
		queryKey: [
			'productionGraphData',
			paramsNetworkTab,
			paramsPeriod,
			paramsValues,
			paramsEndDate,
			paramsStartDate,
			paramsBaId,
			networkIds,
			siteIds,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				networkIds: networkIds,
				subNetwork: paramsNetworkTab === 'all' ? '' : paramsNetworkTab,
				period: [PeriodEnum.custom, PeriodEnum.wtd].includes(
					paramsPeriod as PeriodEnum
				)
					? 'day'
					: paramsPeriod,
				startDate: paramsStartDate,
				endDate: paramsEndDate,
				[paramsValues[paramsNetworkTab]?.key]:
					paramsValues[paramsNetworkTab]?.value,
			})
			const { data } = await authAxios.get<GetProductionGraphDataResponse>(
				`/new/production-graph?${queryParams.toString()}`
			)
			return data
		},
	})

	const handleNetworkTabChange = useCallback(
		(value: string) => {
			const nsp = new URLSearchParams()
			if (value === NetworkTabs.all) {
				nsp.delete('networkTab')
				setSearchParams(nsp)
				return
			}
			nsp.set('networkTab', value)
			setSearchParams(nsp)
		},
		[setSearchParams]
	)

	const handleSelectUnit = useCallback((value: string) => {
		setUnit(value as MeasuringUnits)
	}, [])

	const allValues: {
		bioCharProduced: {
			[key: string]: {
				approved: number
				pending: number
				rejected: number
				total: number
			}
		}
		mixedBioChar: {
			[key: string]: {
				mixed: {
					total: number
					open: number
					packed: number
					openDistributed: number
					packedDistributed: number
				}
				unMixed: {
					total: number
					open: number
					packed: number
					packedDistributed: number
				}
			}
		}
	} = useMemo(() => {
		// values in ltr
		const approvedProducedBioChar =
			getBioCharProduced.data?.approvedBiochar || 0
		const pendingProducedBioChar = getBioCharProduced.data?.pendingBiochar || 0
		const rejectedProducedBioChar =
			getBioCharProduced.data?.rejectedBiochar || 0
		const totalProducedBioChar =
			approvedProducedBioChar + pendingProducedBioChar + rejectedProducedBioChar

		const totalApprovedBiochar = getBioCharProduced?.data?.approvedBiochar || 0

		const mixedBioChar = getBioCharMixing?.data?.totalMixedBiochar || 0
		const openUnmixedBioChar = getBioCharMixing?.data?.unMixedOpenBiochar || 0
		const openMixedBioChr =
			getBioCharMixing?.data?.totalUnPackedMixedBiochar || 0
		const packedMixedBioChar =
			getBioCharMixing?.data?.totalPackedMixedBiochar || 0
		const openDistributedMixedBioChar =
			getBioCharMixing?.data?.totalOpenDistributedMixedBiochar || 0
		const packedDistributedMixedBioChar =
			getBioCharMixing?.data?.totalPackedDistributedMixedBiochar || 0
		const packedDistributedUnMixedBioChar =
			getBioCharMixing?.data?.totalPackedDistributedBiocharOnly || 0
		const packedUnmixedBioChar =
			getBioCharMixing?.data?.totalPackedBiocharOnly || 0
		const unMixedBioChar = openUnmixedBioChar + packedDistributedUnMixedBioChar

		//biochar production quantities in tonnes
		const approvedProducedBioCharinTonnes =
			getBioCharProduced?.data?.approvedBiocharinTonnes || 0
		const pendingProducedBioCharinTonnes =
			getBioCharProduced?.data?.pendingBiocharInTonnes || 0
		const rejectedProducedBioCharinTonnes =
			getBioCharProduced?.data?.rejectedBiocharInTonnes || 0
		const totalProducedBioCharinTonnes =
			(getBioCharProduced?.data?.approvedBiocharinTonnes || 0) +
			(getBioCharProduced?.data?.pendingBiocharInTonnes || 0) +
			(getBioCharProduced?.data?.rejectedBiocharInTonnes || 0)

		const totalApprovedBiocharinTonnes =
			getBioCharProduced?.data?.approvedBiocharinTonnes || 0

		//BioChar mixing quantities in tonnes
		const mixedBioCharinTon =
			getBioCharMixing?.data?.totalMixedBiocharInTonnes || 0
		const openUnmixedBioCharinTon =
			getBioCharMixing?.data?.unMixedOpenBiocharInTonnes || 0
		const openMixedBioChrinTon =
			getBioCharMixing?.data?.totalUnPackedMixedBiocharInTonnes || 0
		const packedMixedBioCharinTon =
			getBioCharMixing?.data?.totalPackedMixedBiocharInTonnes || 0
		const openDistributedMixedBioCharinTon =
			getBioCharMixing?.data?.totalOpenDistributedMixedBiocharInTonnes || 0
		const packedDistributedMixedBioCharinTon =
			getBioCharMixing?.data?.totalPackedDistributedMixedBiocharInTonnes || 0
		const packedDistributedUnMixedBioCharinTon =
			getBioCharMixing?.data?.totalPackedDistributedBiocharOnlyInTonnes || 0
		const packedUnmixedBioCharinTon =
			getBioCharMixing?.data?.totalPackedBiocharOnlyInTonnes || 0
		const unMixedBioCharinTon =
			openUnmixedBioCharinTon + packedDistributedUnMixedBioCharinTon

		// values in m3
		const approvedProducedBioCharInM3 = convertLitreToMeterCube(
			approvedProducedBioChar
		)
		const pendingProducedBioCharInM3 = convertLitreToMeterCube(
			pendingProducedBioChar
		)
		const rejectedProducedBioCharInM3 = convertLitreToMeterCube(
			rejectedProducedBioChar
		)
		const totalProducedBioCharInM3 =
			convertLitreToMeterCube(totalProducedBioChar)

		const totalApprovedBiocharInM3 =
			convertLitreToMeterCube(totalApprovedBiochar)

		return {
			bioCharProduced: {
				[MeasuringUnits.ltr]: {
					approved: roundNumber(approvedProducedBioChar, 3),
					pending: roundNumber(pendingProducedBioChar, 3),
					rejected: roundNumber(rejectedProducedBioChar, 3),
					total: roundNumber(totalProducedBioChar, 3),
					produced: roundNumber(totalApprovedBiochar, 3),
				},
				[MeasuringUnits.m3]: {
					approved: roundNumber(approvedProducedBioCharInM3, 3),
					pending: roundNumber(pendingProducedBioCharInM3, 3),
					rejected: roundNumber(rejectedProducedBioCharInM3, 3),
					total: roundNumber(totalProducedBioCharInM3, 3),
					produced: roundNumber(totalApprovedBiocharInM3, 3),
				},
				[MeasuringUnits.ton]: {
					approved: roundNumber(approvedProducedBioCharinTonnes, 3),
					pending: roundNumber(pendingProducedBioCharinTonnes, 3),
					rejected: roundNumber(rejectedProducedBioCharinTonnes, 3),
					total: roundNumber(totalProducedBioCharinTonnes, 3),
					produced: roundNumber(totalApprovedBiocharinTonnes, 3),
				},
			},
			mixedBioChar: {
				[MeasuringUnits.ltr]: {
					mixed: {
						total: roundNumber(mixedBioChar, 3),
						open: roundNumber(openMixedBioChr, 3),
						packed: roundNumber(packedMixedBioChar, 3),
						openDistributed: roundNumber(openDistributedMixedBioChar, 3),
						packedDistributed: roundNumber(packedDistributedMixedBioChar, 3),
					},
					unMixed: {
						total: roundNumber(unMixedBioChar, 3),
						open: roundNumber(openUnmixedBioChar, 3),
						packed: roundNumber(packedUnmixedBioChar, 3),
						packedDistributed: roundNumber(packedDistributedUnMixedBioChar, 3),
					},
				},
				[MeasuringUnits.m3]: {
					mixed: {
						total: roundNumber(convertLitreToMeterCube(mixedBioChar), 3),
						open: roundNumber(convertLitreToMeterCube(openMixedBioChr), 3),
						packed: roundNumber(convertLitreToMeterCube(packedMixedBioChar), 3),
						openDistributed: roundNumber(
							convertLitreToMeterCube(openDistributedMixedBioChar),
							3
						),
						packedDistributed: roundNumber(
							convertLitreToMeterCube(packedDistributedMixedBioChar),
							3
						),
					},
					unMixed: {
						total: roundNumber(convertLitreToMeterCube(unMixedBioChar), 3),
						open: roundNumber(convertLitreToMeterCube(openUnmixedBioChar), 3),
						packed: roundNumber(
							convertLitreToMeterCube(packedUnmixedBioChar),
							3
						),
						packedDistributed: roundNumber(
							convertLitreToMeterCube(packedDistributedUnMixedBioChar),
							3
						),
					},
				},
				[MeasuringUnits.ton]: {
					mixed: {
						total: roundNumber(mixedBioCharinTon, 3),
						open: roundNumber(openMixedBioChrinTon, 3),
						packed: roundNumber(packedMixedBioCharinTon, 3),
						openDistributed: roundNumber(openDistributedMixedBioCharinTon, 3),
						packedDistributed: roundNumber(
							packedDistributedMixedBioCharinTon,
							3
						),
					},
					unMixed: {
						total: roundNumber(unMixedBioCharinTon, 3),
						open: roundNumber(openUnmixedBioCharinTon, 3),
						packed: roundNumber(packedUnmixedBioCharinTon, 3),
						packedDistributed: roundNumber(
							packedDistributedUnMixedBioCharinTon,
							3
						),
					},
				},
			},
		}
	}, [getBioCharMixing?.data, getBioCharProduced.data])

	const formatLabel = (dateStr: string, period: PeriodEnum) => {
		const parsedDate = parseISO(dateStr)
		switch (period) {
			case PeriodEnum.wtd:
				return format(parsedDate, 'EEE')
			case PeriodEnum.mtd:
				return format(parsedDate, "MMM''yy")
			case PeriodEnum.ytd:
				return format(parsedDate, 'yyyy')
			default:
				return format(parsedDate, 'yyyy-MM-dd')
		}
	}

	const graphData: { [key: string]: { label: string[]; data: number[] } } =
		useMemo(
			() => ({
				biomass: {
					label:
						getGraphData?.data?.totalBiomass?.map((i) =>
							formatLabel(i.period, paramsPeriod as PeriodEnum)
						) || [],
					data: getGraphData?.data?.totalBiomass?.map((i) => i.total) || [],
				},
				bioChar: {
					label:
						getGraphData?.data?.processDetail?.map((i) =>
							formatLabel(i.period, paramsPeriod as PeriodEnum)
						) || [],
					data:
						getGraphData?.data?.processDetail?.map((i) =>
							unit === MeasuringUnits.ton ? i.totalBiocharInTonnes : i.total
						) || [],
				},
				credits: {
					label:
						getGraphData?.data?.processDetail?.map((i) =>
							formatLabel(i.period, paramsPeriod as PeriodEnum)
						) || [],
					data:
						getGraphData?.data?.processDetail?.map(
							(i) => i.totalCarbonCredits
						) || [],
				},
			}),
			[
				getGraphData?.data?.processDetail,
				getGraphData?.data?.totalBiomass,
				paramsPeriod,
				unit,
			]
		)

	const handleEntityFilterValueChange = useCallback(
		(key: string, value: string) => {
			setSearchParams((prev) => {
				if (value) {
					const arr = Object.values(paramsValues).reduce((acc, curr) => {
						if (curr.key === key) {
							return acc
						}
						return [...acc, curr.key]
					}, [] as string[])
					arr.forEach((item) => {
						prev.delete(item)
					})
					prev.set(key, value)
				} else {
					prev.delete(key)
				}
				return prev
			})
		},

		[paramsValues, setSearchParams]
	)

	const handleChangeChartType = useCallback(
		(type: string) => {
			setSearchParams(
				(prev) => {
					if (type === 'bar') {
						prev.delete('chartType')
					} else {
						prev.set('chartType', type)
					}
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const getShiftedPeriodRange = (
		startDate: string,
		endDate: string,
		period: PeriodEnum,
		direction: 'prev' | 'next'
	) => {
		let unit: moment.unitOfTime.DurationConstructor
		let offset: number

		switch (period) {
			case PeriodEnum.wtd:
				unit = 'days'
				offset = direction === 'next' ? 7 : -7
				break
			case PeriodEnum.mtd:
				unit = 'months'
				offset = direction === 'next' ? 7 : -7
				break
			case PeriodEnum.ytd:
				unit = 'years'
				offset = direction === 'next' ? 3 : -3
				break
			default:
				unit = 'days'
				offset = direction === 'next' ? 7 : -7
		}
		const today = moment()
		const prevStartDate = moment(startDate, INTERNAL_DATE_FORMAT)
		const prevEndDate = moment(endDate, INTERNAL_DATE_FORMAT)
		let newStart = moment(startDate, INTERNAL_DATE_FORMAT).add(offset, unit)
		let newEnd = moment(endDate, INTERNAL_DATE_FORMAT).add(offset, unit)

		if (newEnd.isAfter(today, 'day')) {
			const originalDuration = moment(endDate, INTERNAL_DATE_FORMAT).diff(
				moment(startDate, INTERNAL_DATE_FORMAT),
				'days'
			)

			const adjustedEnd = today.clone()
			const adjustedStart = today.clone().subtract(originalDuration, 'days')

			return {
				startDate: adjustedStart.format(DATE_FORMAT),
				endDate: adjustedEnd.format(DATE_FORMAT),
			}
		}
		if (period === PeriodEnum.ytd) {
			const minStart = moment('2023-01-01', 'YYYY-MM-DD')
			if (newStart.isBefore(minStart)) {
				newStart = prevStartDate.clone()
				newEnd = prevEndDate.clone()
			}
		}

		return {
			startDate: newStart.format(DATE_FORMAT),
			endDate: newEnd.format(DATE_FORMAT),
		}
	}

	const handleNextGraphDate = () => {
		const today = moment().startOf('day')

		if (moment(paramsEndDate, INTERNAL_DATE_FORMAT).isSameOrAfter(today)) return

		const { startDate, endDate } = getShiftedPeriodRange(
			paramsStartDate,
			paramsEndDate,
			paramsPeriod as PeriodEnum,
			'next'
		)

		setSearchParams(
			(prev) => {
				prev.set('startDate', startDate)
				prev.set('endDate', endDate)
				prev.set('period', paramsPeriod)
				return prev
			},
			{ replace: true }
		)
	}

	const handlePrevGraphDate = () => {
		const { startDate, endDate } = getShiftedPeriodRange(
			paramsStartDate,
			paramsEndDate,
			paramsPeriod as PeriodEnum,
			'prev'
		)

		setSearchParams(
			(prev) => {
				prev.set('startDate', startDate)
				prev.set('endDate', endDate)
				prev.set('period', paramsPeriod)
				return prev
			},
			{ replace: true }
		)
	}

	const isNextGraphButtonDisabled = useMemo(() => {
		const today = moment().startOf('day')
		const end = moment(paramsEndDate, INTERNAL_DATE_FORMAT).startOf('day')

		return end.isSameOrAfter(today)
	}, [paramsEndDate])

	return {
		setSearchParams,
		searchParams,
		paramsNetworkTab,
		graphTab,
		handleNetworkTabChange,
		getBioCharProduced,
		unit,
		setUnit,
		handleSelectUnit,
		allValues,
		getQuantityData,
		getGraphData,
		graphData,
		fetchBA,
		fetchSites,
		paramsValues,
		handleEntityFilterValueChange,
		handleChangeChartType,
		paramsChartType,
		handlePrevGraphDate,
		handleNextGraphDate,
		isNextGraphButtonDisabled,
		paramsPeriod,
		paramsStartDate,
		paramsEndDate,
	}
}
