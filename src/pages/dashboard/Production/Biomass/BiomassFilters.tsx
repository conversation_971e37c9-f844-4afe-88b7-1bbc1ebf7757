import { Autocomplete, TextField } from '@mui/material'
import { FC, useState, useCallback, useEffect, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { userRoles } from '@/utils/constant'
import { Stack, styled } from '@mui/system'

enum SelectedPage {
	ARTISAN_PRO = 'artisanPro',
	NETWORK = 'network',
	ALL = 'all',
}

type PropsForBiomassFilter = {
	isProductionPage?: boolean
}

export const BiomassFilter: FC<PropsForBiomassFilter> = ({
	isProductionPage,
}) => {
	const { userDetails } = useAuthContext()
	const [searchParams, setSearchParams] = useSearchParams()
	const subNetwork =
		searchParams.get('subNetwork') || searchParams.get('networkTab') || ''

	const [selectedArtisan, setSelectedArtisan] = useState<{
		value: string
	} | null>(null)
	const [selectedCsinkNetwork, setSelectedCsinkNetwork] = useState<{
		value: string
	} | null>(null)
	const [selectedBAs, setSelectedBAs] = useState<
		{ value?: string; label?: string }[] | null
	>([])

	const [selectedSite, setSelectedSite] = useState<{ value: string } | null>(
		null
	)
	const [selectedFarmer, setSelectedFarmer] = useState<{
		value: string
	} | null>(null)

	const selectedPage =
		subNetwork === SelectedPage.ARTISAN_PRO
			? SelectedPage.ARTISAN_PRO
			: subNetwork === SelectedPage.NETWORK
			? SelectedPage.NETWORK
			: SelectedPage.ALL

	const FetchhBa = useQuery({
		queryKey: ['allBAForAddUser', userDetails?.accountType],
		queryFn: async () => {
			const { data } = await authAxios.get<{
				baDetails: { id: string; name: string; shortCode: string }[]
			}>('/drop-down/biomass-aggregators')

			return (
				data?.baDetails?.map((item) => ({
					label: `${item.name} (${item.shortCode})`,
					value: item.id,
				})) || []
			)
		},
		enabled:
			userDetails?.accountType === userRoles.Admin ||
			userDetails?.accountType === userRoles.CsinkManager,
	})

	const AllNetworkQuery = useQuery({
		queryKey: ['allNetwork'],
		queryFn: async () =>
			authAxios
				.get<{
					networks: { id: string; name: string; shortName: string }[]
				}>(`/new/networks?subNetwork=network`)
				.then(({ data }) =>
					data?.networks?.map((item) => ({
						label: `${item.name}`,
						value: item.id,
					}))
				),
	})

	const AllCsinkQuery = useQuery({
		queryKey: ['AllCsink'],
		queryFn: async () =>
			authAxios
				.get<{
					networks: { id: string; name: string; shortName: string }[]
				}>(`/new/networks?subNetwork=artisanPro`)
				.then(({ data }) =>
					data?.networks?.map((item) => ({
						label: `${item.name}`,
						value: item.id,
					}))
				),
	})

	const GetFarmersQuery = useQuery({
		queryKey: ['Farmers', selectedCsinkNetwork?.value],
		queryFn: async () => {
			if (!selectedCsinkNetwork?.value) return []

			const { data } = await authAxios.get<{
				siteDetails: {
					id: string
					name: string
					address: string
					siteId: string
				}[]
			}>(`drop-down/sites?networkIds=${selectedCsinkNetwork?.value}`)

			return (
				data?.siteDetails?.map((item) => ({
					label: item.name,
					value: item.id,
				})) || []
			)
		},
		enabled: !!selectedCsinkNetwork,
	})

	const GetSiteFiltersQuery = useQuery({
		queryKey: ['SiteFilters', selectedArtisan?.value],
		queryFn: async () => {
			if (!selectedArtisan?.value) return []

			const { data } = await authAxios.get<{
				siteDetails: { name: string; id: string }[]

			}>(`drop-down/sites?networkIds=${selectedArtisan?.value}`)

			return (
				data?.siteDetails?.map((item) => ({
					label: item.name,
					value: item.id,
				})) || []
			)
		},
		enabled: !!selectedArtisan,
	})

	const { data: baData = [], isLoading: isLoadingBA } = FetchhBa
	const { data: artisanData, isLoading: isLoadingArtisan } = AllCsinkQuery
	const { data: csinkNetworkData, isLoading: isLoadingCsink } = AllNetworkQuery
	const { data: farmers, isLoading: isLoadingFarmers } = GetFarmersQuery
	const { data: SiteFilters, isLoading: isLoadingSiteFilters } =
		GetSiteFiltersQuery

	const handleOnChange = useCallback(
		(selectedOption: any, queryKey: string) => {
			const nsp = new URLSearchParams(searchParams.toString())
			if (Array.isArray(selectedOption)) {
				const selectedValues = selectedOption
					.map((opt) => opt.value)
					.filter(Boolean)
					.join(',')
				if (selectedValues) {
					nsp.set(queryKey, selectedValues)
				} else {
					nsp.delete(queryKey)
				}
			} else if (selectedOption) {
				nsp.set(queryKey, selectedOption.value)
			} else {
				nsp.delete(queryKey)
			}

			if (searchParams.has('productionTab')) {
				nsp.set('productionTab', searchParams.get('productionTab')!)
			}

			setSearchParams(nsp, { replace: true })
		},
		[searchParams, setSearchParams]
	)

	useEffect(() => {
		const nsp = new URLSearchParams(searchParams.toString())
		let hasChanged = false

		if (!isProductionPage) nsp.set('subNetwork', selectedPage)

		if (selectedPage === SelectedPage.ARTISAN_PRO) {
			if (selectedArtisan) {
				if (nsp.get('networkIds') !== selectedArtisan.value) {
					nsp.set('networkIds', selectedArtisan.value)
					hasChanged = true
				}
			} else {
				if (nsp.has('networkIds')) {
					nsp.delete('networkIds')
					hasChanged = true
				}
			}

			if (selectedSite) {
				if (nsp.get('siteId') !== selectedSite.value) {
					nsp.set('siteId', selectedSite.value)
					hasChanged = true
				}
			} else {
				if (nsp.has('siteId')) {
					nsp.delete('siteId')
					hasChanged = true
				}
			}

			if (nsp.has('biomassAggregatorIds')) {
				nsp.delete('biomassAggregatorIds')
				hasChanged = true
			}
		}

		if (selectedPage === SelectedPage.NETWORK) {
			if (selectedCsinkNetwork) {
				if (nsp.get('networkIds') !== selectedCsinkNetwork.value) {
					nsp.set('networkIds', selectedCsinkNetwork.value)
					hasChanged = true
				}
			} else {
				if (nsp.has('networkIds')) {
					nsp.delete('networkIds')
					hasChanged = true
				}
			}

			if (selectedFarmer) {
				if (nsp.get('siteId') !== selectedFarmer.value) {
					nsp.set('siteId', selectedFarmer.value)
					hasChanged = true
				}
			} else {
				if (nsp.has('siteId')) {
					nsp.delete('siteId')
					hasChanged = true
				}
			}

			if (nsp.has('biomassAggregatorIds')) {
				nsp.delete('biomassAggregatorIds')
				hasChanged = true
			}
		}

		if (selectedPage === SelectedPage.ALL) {
			const baIds = selectedBAs?.map((ba) => ba.value).join(',')
			if (baIds) {
				if (nsp.get('biomassAggregatorIds') !== baIds) {
					nsp.set('biomassAggregatorIds', baIds)
					hasChanged = true
				}
			} else {
				if (nsp.has('biomassAggregatorIds')) {
					nsp.delete('biomassAggregatorIds')
					hasChanged = true
				}
			}

			if (nsp.has('siteId') || nsp.has('networkIds')) {
				nsp.delete('siteId')
				nsp.delete('networkIds')
				hasChanged = true
			}
		}

		if (searchParams.has('productionTab')) {
			nsp.set('productionTab', searchParams.get('productionTab')!)
		}

		if (hasChanged) {
			setSearchParams(nsp, { replace: true })
		}
	}, [
		selectedPage,
		selectedArtisan,
		selectedSite,
		selectedCsinkNetwork,
		selectedFarmer,
		selectedBAs,
		searchParams,
		setSearchParams,
		isProductionPage,
	])

	useEffect(() => {
		if (
			userDetails?.accountType &&
			userDetails?.accountType !== userRoles.CsinkManager &&
			userDetails?.accountType !== userRoles.Admin
		) {
			setSelectedBAs([
				{
					label: userDetails?.biomassAggregatorName,
					value: userDetails?.biomassAggregatorId,
				},
			])
		}
	}, [
		userDetails?.accountType,
		userDetails?.biomassAggregatorId,
		userDetails?.biomassAggregatorName,
	])

	const ShowBaSelection = useMemo(() => {
		return [
			userRoles.ArtisanPro,
			userRoles.cSinkNetwork,
			userRoles.artisanProNetworkManager,
		].includes(userDetails?.accountType as userRoles)
	}, [userDetails?.accountType])

	return (
		<>
			{userDetails?.accountType !== userRoles.cSinkNetwork &&
				selectedPage === SelectedPage.ARTISAN_PRO && (
					<>
						<Autocomplete
							value={selectedArtisan}
							className='auto-complete'
							onChange={(_, newValue: any) => {
								setSelectedArtisan(newValue)
								setSelectedSite(null)
								setSelectedFarmer(null)
								setSelectedBAs([])
								handleOnChange(newValue, 'networkIds')
							}}
							options={artisanData || []}
							loading={isLoadingArtisan}
							renderInput={(params) => (
								<TextField {...params} label='Select Artisan Pro' />
							)}
						/>
						{selectedArtisan && (
							<Autocomplete
								value={selectedSite}
								className='auto-complete'
								onChange={(_, newValue: any) => {
									setSelectedSite(newValue)
									handleOnChange(newValue, 'siteId')
								}}
								options={SiteFilters || []}
								loading={isLoadingSiteFilters}
								renderInput={(params) => (
									<TextField {...params} label='Select Site' />
								)}
							/>
						)}
					</>
				)}
			{(userDetails?.accountType == userRoles.ArtisanPro ||
				userDetails?.accountType !== 'artisan_pro_network_manager') &&
				selectedPage === SelectedPage.NETWORK && (
					<>
						<Autocomplete
							value={selectedCsinkNetwork}
							className='auto-complete'
							onChange={(_, newValue: any) => {
								setSelectedCsinkNetwork(newValue)
								setSelectedFarmer(null)
								setSelectedSite(null)
								setSelectedBAs([])

								handleOnChange(newValue, 'networkIds')
							}}
							options={csinkNetworkData || []}
							loading={isLoadingCsink}
							renderInput={(params) => (
								<TextField {...params} label='Select Csink Network' />
							)}
						/>
						{selectedCsinkNetwork && (
							<Autocomplete
								value={selectedFarmer}
								className='auto-complete'
								onChange={(_, newValue: any) => {
									setSelectedFarmer(newValue)
									handleOnChange(newValue, 'siteId')
								}}
								options={farmers || []}
								loading={isLoadingFarmers}
								renderInput={(params) => (
									<TextField {...params} label='Select Farmer' />
								)}
							/>
						)}
					</>
				)}

			{selectedPage === SelectedPage.ALL && (
				<StyledContainer>
					<Autocomplete
						multiple
						disableCloseOnSelect
						value={selectedBAs || []}
						className='auto-complete custom-autocomplete'
						disabled={ShowBaSelection}
						options={baData}
						loading={isLoadingBA}
						onChange={(_, newValue) => {
							setSelectedBAs(newValue)
						}}
						onClose={() => {
							setSelectedBAs(selectedBAs)
							handleOnChange(selectedBAs, 'biomassAggregatorIds')
						}}
						renderInput={(params) => (
							<TextField {...params} label='Select BAs' variant='outlined' />
						)}
						getLimitTagsText={(more) => `+${more} more`}
						renderTags={(value) => {
							const labelString = value
								?.map((option) => option.label)
								?.join(', ')
							return labelString.length > 27
								? labelString.slice(0, 22) + '...'
								: labelString
						}}
					/>
				</StyledContainer>
			)}
		</>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	'.custom-autocomplete': {
		minWidth: theme.spacing(34.5),
		maxWidth: theme.spacing(62.5),
		transition: 'width 0.3s ease-in-out',
	},
}))
