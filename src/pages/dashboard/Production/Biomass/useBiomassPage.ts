import { authAxios, useAuthContext } from '@/contexts'
import {
	GetBiomassAvailableResponse,
	GetBiomassOrCropResponse,
	GetProductionGraphDataResponse,
	IBiomassCollectionList,
} from '@/interfaces'
import { NetworkTabs, SiteKilnResponse, UpdateBiomassPayload } from '@/types'
import { defaultLimit, defaultPage, userRoles } from '@/utils/constant'
import { useMutation, useQuery } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useCallback, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export const useBiomassPage = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const paramsBAId = searchParams.get('biomassAggregatorIds') || ''
	const paramsNetworkId = searchParams.get('networkIds') || ''
	const paramsSiteId = searchParams.get('siteId') || ''
	const paramsKilnId = searchParams.get('kilnId') || ''
	const paramsSearchName = searchParams.get('searchName') || ''
	const graphTab = searchParams.get('graphTab') || 'biomass'
	const productionTab = searchParams.get('productionTab') || 'biomassCollection'
	const paramsSubnetwork = searchParams.get('subNetwork') || 'all'
	const editBiomassId = searchParams.get('editBiomassId') || ''
	const { userDetails } = useAuthContext()

	const biomassCollectionQuery = useQuery({
		queryKey: [
			'biomassCollection',
			paramsBAId,
			paramsNetworkId,
			paramsSearchName,
			paramsLimit,
			paramsPage,
			paramsKilnId,
			paramsSiteId,
			paramsSubnetwork,
		],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: paramsLimit,
				page: paramsPage,
				search: paramsSearchName,
				biomassAggregatorIds: paramsBAId,
				networkIds: paramsNetworkId,
				siteId: paramsSiteId,
				kilnId: paramsKilnId,
				subNetwork: paramsSubnetwork === 'all' ? '' : paramsSubnetwork,
			})
			return authAxios.get<{
				count: number
				siteBiomassList: IBiomassCollectionList[]
			}>(`/new/biomass-collection?${queryParams}`)
		},
		select: ({ data }) => data,
		enabled: productionTab === 'biomassCollection',
	})

	const biomassAvailableQuery = useQuery({
		queryKey: [
			'biomassAvailable',
			paramsSearchName,
			paramsBAId,
			paramsNetworkId,
			paramsSiteId,
			paramsKilnId,
			paramsLimit,
			paramsPage,
			paramsSubnetwork,
		],
		queryFn: () => {
			const queryParams = new URLSearchParams()

			queryParams.set('limit', paramsLimit)
			queryParams.set('page', paramsPage)
			if (paramsSearchName) queryParams.set('search', paramsSearchName)
			if (paramsNetworkId) queryParams.set('networkIds', paramsNetworkId)
			if (paramsSiteId) queryParams.set('siteId', paramsSiteId)
			if (paramsKilnId) queryParams.set('kilnId', paramsKilnId)
			if (paramsSubnetwork !== 'all')
				queryParams.set('subNetwork', paramsSubnetwork)
			if (paramsBAId) queryParams.set('biomassAggregatorIds', paramsBAId) // Only set if it's defined

			return authAxios.get<GetBiomassAvailableResponse>(
				`/new/biomass-available/v2?${queryParams.toString()}`
			)
		},
		select: ({ data }) => data,
		enabled: productionTab === 'biomassAvailable',
	})

	const getBiomassCrops = useQuery({
		queryKey: [
			'biomassCrops',
			paramsBAId,
			paramsSiteId,
			paramsKilnId,
			paramsSubnetwork,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				siteId: paramsSiteId,
				kilnId: paramsKilnId,
				subNetwork: paramsSubnetwork === 'all' ? '' : paramsSubnetwork,
				biomassAggregatorIds: paramsBAId,
			})
			const { data } = await authAxios.get<GetBiomassOrCropResponse>(
				`new/biomass-crops?${queryParams}`
			)
			return data
		},
	})

	const paramsValues: { [key: string]: { value: string; key: string } } =
		useMemo(
			() => ({
				[NetworkTabs.all]: {
					value: paramsBAId,
					key: 'biomassAggregatorIds',
				},
				[NetworkTabs.artisanPro]: {
					value: paramsSiteId,
					key: 'siteId',
				},
				[NetworkTabs.network]: {
					value: paramsKilnId,
					key: 'kilnId',
				},
			}),
			[paramsBAId, paramsKilnId, paramsSiteId]
		)

	const getGraphData = useQuery({
		queryKey: [
			'productionGraphData',
			paramsBAId,
			paramsSiteId,
			paramsKilnId,
			paramsSubnetwork,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				subNetwork: paramsSubnetwork === 'all' ? '' : paramsSubnetwork,
				period: 'month',
				[paramsValues[paramsSubnetwork]?.key]:
					paramsValues[paramsSubnetwork]?.value,
			})
			const { data } = await authAxios.get<GetProductionGraphDataResponse>(
				`/new/production-graph?${queryParams.toString()}`
			)
			return data
		},
	})

	const fetchSites = useQuery({
		queryKey: ['fetchSites', paramsSubnetwork],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				subNetwork: paramsSubnetwork,
			})
			const { data } = await authAxios.get<SiteKilnResponse>(
				`/new/sites-kilns?${queryParams.toString()}`
			)
			return data
		},
		select: (data) => {
			return (data?.siteKilns ?? []).map((item) => ({
				value: (item.isArtisan ? item?.siteId : item?.kilnId) || '',
				label: (item.isArtisan ? item.siteName : item.kilnName) || '',
			}))
		},
		enabled: paramsSubnetwork !== 'all',
	})

	const fetchBA = useQuery({
		queryKey: ['allBAForAddUser'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<{
				biomassAggregators: { id: string; name: string; shortName: string }[]
			}>(`/biomass-aggregator?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.biomassAggregators?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled: [
			userRoles.Admin,
			userRoles.CsinkManager,
			userRoles.BiomassAggregator,
		].includes(
			userDetails?.accountType as userRoles // need to add more account permissions
		),
	})

	const approvedBiomass = useMutation({
		mutationKey: ['approvedBiomass'],
		mutationFn: async (payload: UpdateBiomassPayload) => {
			await authAxios.put(`new/update-biomass`, payload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: () => {
			toast(`successfully update the biomass`)
			biomassCollectionQuery.refetch()
		},
	})

	const handleApprovedBiomass = useCallback(
		async (selectedBiomassCollection: UpdateBiomassPayload) => {
			approvedBiomass.mutate(selectedBiomassCollection)
			setSearchParams((params) => {
				params.delete('editBiomassId')
				return params
			})
		},
		[approvedBiomass, setSearchParams]
	)

	const handleEntityFilterValueChange = useCallback(
		(key: string, value: string) => {
			setSearchParams((prev) => {
				if (value) {
					const arr = Object.values(paramsValues).reduce((acc, curr) => {
						if (curr.key === key) {
							return acc
						}
						return [...acc, curr.key]
					}, [] as string[])
					arr.forEach((item) => {
						prev.delete(item)
					})
					prev.set(key, value)
				} else {
					prev.delete(key)
				}
				return prev
			})
		},

		[paramsValues, setSearchParams]
	)

	return {
		biomassCollection: biomassCollectionQuery?.data,
		biomasssAvailable: biomassAvailableQuery?.data,
		loadingBiomassAvailable: biomassAvailableQuery.isLoading, // loading states
		loadingBiomassCollection: biomassCollectionQuery.isLoading, // loading State
		setSearchParams,
		handleApprovedBiomass,
		editBiomassId,
		searchParams,
		paramsSubnetwork,
		graphTab,
		productionTab,
		getBiomassCrops,
		getGraphData,
		fetchBA,
		fetchSites,
		paramsValues,
		handleEntityFilterValueChange,
	}
}
