import { CustomDataGrid, DetailedDialog, MediaCarousal } from '@/components'
import {
	Box,
	Button,
	Chip,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { FC, useCallback, useMemo, useRef, useState } from 'react'
import {
	GridColDef,
	GridRenderCellParams,
	GridTreeNodeWithRender,
	GridValidRowModel,
} from '@mui/x-data-grid'
import {
	EnumSubNetwork,
	IFileData,
	IMediaWithDeleteParams,
	TBagsInfo,
	TPackagingListResponse,
	TResponseMixType,
} from '@/interfaces'
import { format } from 'date-fns'
import { useSearchParams } from 'react-router-dom'
import { useAuthContext } from '@/contexts'
import { Delete, PlayCircle } from '@mui/icons-material'
import { UseQueryResult } from '@tanstack/react-query'
import { userRoles } from '@/utils/constant'
import { DeletePackagingDialog } from '@/components/DeletePackagingDialog'
import { usePackaging } from '../hooks'
import ImageOutlinedIcon from '@mui/icons-material/ImageOutlined'
import { handleImageUpload, proxyImage } from '@/utils/helper'
import { toast } from 'react-toastify'
import { BiomassFilter } from '../../Biomass/BiomassFilters'
import { theme } from '@/lib/theme/theme'

interface IProps {
	allPackaging?: TPackagingListResponse
	isLoading: boolean
	showDownloadCheckbox: boolean
	selectedProcess: string[] | null
	deletePackaging: () => void
	setselectedProcess: React.Dispatch<React.SetStateAction<string[] | null>>
	fetchAllMixTypeQuery: UseQueryResult<TResponseMixType, Error>
}

const filterToResetOnTabChange = [
	'page',
	'limit',
	'siteId',
	'kilnId',
	'searchName',
]

export const AllPackagingPanel: FC<IProps> = ({
	allPackaging,
	isLoading,
	deletePackaging,
}) => {
	const { deleteImageHandler, setRowData, addImagesHandler } = usePackaging()
	const fileInputRef = useRef<HTMLInputElement>(null)
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsSubNetwork = searchParams.get('subNetwork') || EnumSubNetwork.all
	const paramsDeleteId = searchParams.get('deleteId') || ''
	const { userDetails } = useAuthContext()
	const [bagInfo, setBagInfo] = useState<TBagsInfo[] | null>(null)
	const [selectedMedia, setSelectedMedia] = useState<{
		selectedRow: string | null
		id: null | string
	}>({ selectedRow: null, id: null })

	const selectMediaMemo: IMediaWithDeleteParams[] = useMemo(() => {
		if (!selectedMedia?.selectedRow) return []
		const images =
			allPackaging?.result?.find((i) => i?.id === selectedMedia?.selectedRow)
				?.images ?? []
		const videos =
			allPackaging?.result
				?.find((i) => i?.id === selectedMedia?.selectedRow)
				?.videos?.map((i) => ({
					...i,
					type: 'video',
				})) ?? []
		return [...images, ...videos] as IMediaWithDeleteParams[]
	}, [allPackaging?.result, selectedMedia?.selectedRow])
	const networkName = useCallback(
		(params: GridValidRowModel) => {
			let value
			if (paramsSubNetwork === EnumSubNetwork.cSinkNetwork)
				value = params?.row?.csinkNetworkName
			else if (paramsSubNetwork === EnumSubNetwork.artisanPro)
				value = params?.row?.artisanProName
			else value = params?.row?.csinkNetworkName || params?.row?.artisanProName
			return value
		},
		[paramsSubNetwork]
	)

	const handleFileChange = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		const file = event.target?.files?.[0]

		if (!file) {
			toast.error('No file selected. Please choose a file to upload.')
			return
		}

		try {
			const data = await handleImageUpload(file)

			if (!data?.id) {
				throw new Error('File upload failed. No image ID returned.')
			}

			const imageIds = [data.id]
			addImagesHandler(imageIds)
			event.target.value = ''
		} catch (err) {
			toast.error(
				err instanceof Error
					? err.message
					: 'An unexpected error occurred during file upload.'
			)
		} finally {
			event.target.value = ''
		}
	}

	const AllPackagingColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'networkName',
				headerName: 'Network Name',
				minWidth: 250,
				flex: 1,
				sortable: false,
				renderCell: (params) => <Typography>{networkName(params)}</Typography>,
			},
			{
				field: 'mixType',
				headerName: 'Mix Type',
				minWidth: 200,
				flex: 1,
				sortable: false,
			},
			{
				field: 'totalBiocharQuantity',
				headerName: 'Biochar Qty',
				minWidth: 100,
				flex: 1,
				sortable: false,
			},
			{
				field: 'otherMaterialQuantity',
				headerName: `Other Mix Qty `,
				minWidth: 100,
				flex: 1,
				sortable: false,
			},
			{
				field: 'totalMixedQuantity',
				headerName: 'Total Qty',
				minWidth: 100,
				flex: 1,
				sortable: false,
			},
			{
				field: 'availableMixedQuantity',
				headerName: 'Available Qty',
				minWidth: 100,
				flex: 1,
				sortable: false,
			},
			{
				field: 'bagDetails',
				headerName: `Bag Details`,
				minWidth: 100,
				flex: 1,
				renderCell: (params) =>
					params?.row?.createdBags?.length > 0 ? (
						<Button
							onClick={() => {
								setBagInfo(params?.row?.createdBags)
							}}>
							View
						</Button>
					) : (
						<Stack pl={3}> -</Stack>
					),
			},
			{
				field: 'date',
				headerName: 'Date',
				minWidth: 100,
				flex: 1,
				sortable: false,
				valueGetter: (params) =>
					params?.row?.createdAt ? (
						format(params?.row?.createdAt, 'd MMM yyyy')
					) : (
						<Stack pl={3}> -</Stack>
					),
			},
			// TODO: refactor
			{
				field: 'media',
				headerName: `Media`,
				minWidth: 280,
				flex: 1,
				sortable: false,
				renderCell: (params) => {
					const images = params?.row?.images ?? []
					const videos =
						params?.row?.videos?.map((i: IFileData) => ({
							...i,
							type: 'video',
						})) ?? []
					const media: IMediaWithDeleteParams[] = [...images, ...videos]
					return (
						<>
							<Stack
								alignItems='center'
								flexDirection='row'
								flexWrap='wrap'
								paddingLeft={theme.spacing(0.5)}
								maxWidth={200}>
								{media?.length
									? media?.slice(0, 2)?.map((image, index) => (
											<IconButton
												key={index}
												sx={{ position: 'relative' }}
												onClick={() => {
													setRowData(params?.row)
													setSelectedMedia({
														selectedRow: params?.row?.id,
														id: image?.id,
													})
												}}>
												<Box
													component='img'
													src={
														image?.type === 'video'
															? image?.thumbnailURL
															: proxyImage(image?.path ?? '', '50:50')
													}
													alt='image'
													className='img-btn'
												/>
												{image?.type === 'video' ? (
													<IconButton
														sx={{ position: 'absolute', top: 7, left: 6 }}>
														<PlayCircle color='primary' />
													</IconButton>
												) : null}
											</IconButton>
									  ))
									: null}
								{media?.length > 2 ? (
									<Box
										className='img-btn'
										bgcolor='grey.300'
										sx={{
											display: 'flex',
											justifyContent: 'center',
											alignItems: 'center',
										}}
										onClick={(e) => {
											e.stopPropagation()
											setRowData(params?.row)
											setSelectedMedia({
												selectedRow: params?.row?.id,
												id: null,
											})
										}}>
										<Typography variant='subtitle2' textAlign='center'>
											+{media?.length - 2}
										</Typography>
									</Box>
								) : null}
								{params?.row &&
									userDetails?.accountType === userRoles.Admin && (
										<Stack direction='row' spacing={1} alignItems={'center'}>
											<IconButton
												onClick={() => {
													setRowData(params?.row)
													fileInputRef.current?.click()
												}}>
												<ImageOutlinedIcon />
											</IconButton>
										</Stack>
									)}
							</Stack>
						</>
					)
				},
			},
			...(userDetails?.accountType === userRoles.Admin
				? [
						{
							field: 'id',
							headerName: `Action`,
							minWidth: 100,
							flex: 1,
							renderCell: (
								params: GridRenderCellParams<
									GridValidRowModel,
									any,
									any,
									GridTreeNodeWithRender
								>
							) => (
								<Button
									onClick={() => {
										setSearchParams((newParams) => {
											newParams.set('deleteId', params?.row?.id ?? '')
											newParams.set('isArtisan', params?.row?.isArtisan ?? '')
											if (params.row.isArtisan) {
												newParams.set(
													'artisanProId',
													params?.row?.artisanProId ?? ''
												)
												newParams.set('deleteSiteId', params?.row?.siteId ?? '')
												newParams.delete('deleteKilnId')
												newParams.delete('csinkNetworkId')
											} else {
												newParams.set(
													'csinkNetworkId',
													params?.row?.csinkNetworkId ?? ''
												)
												newParams.set('deleteKilnId', params?.row?.kilnId ?? '')
												newParams.delete('deleteSiteId')
												newParams.delete('artisanProId')
											}
											return newParams
										})
									}}>
									<Delete />
								</Button>
							),
						},
				  ]
				: []),
		],
		[networkName, setRowData, setSearchParams, userDetails?.accountType]
	)

	const filterResetArray = useMemo(() => {
		switch (userDetails?.accountType) {
			case 'admin':
				return [...filterToResetOnTabChange, 'baId', 'networkId']
			case 'biomass_aggregator':
			case 'c_sink_manager':
				return [...filterToResetOnTabChange, 'networkId']
			default:
				return filterToResetOnTabChange
		}
	}, [userDetails?.accountType])

	const handleSubNetworkClick = useCallback(
		(value: string) => {
			const nsp = new URLSearchParams(searchParams)
			if (value === EnumSubNetwork.all) {
				nsp.delete('subNetwork')
				setSearchParams(nsp)
				return
			}
			filterResetArray.forEach((key) => {
				nsp.delete(key)
			})
			nsp.set('subNetwork', value)
			setSearchParams(nsp)
		},
		[filterResetArray, searchParams, setSearchParams]
	)

	const chipList = [
		{ label: 'All', value: EnumSubNetwork.all },
		{ label: 'C Sink Network', value: EnumSubNetwork.cSinkNetwork },
		{ label: 'Artisan Pro', value: EnumSubNetwork.artisanPro },
	]

	return (
		<>
			<input
				ref={fileInputRef}
				id='packagingImages'
				type='file'
				accept='.jpg, .jpeg, .png, .heic, .webp'
				style={{ display: 'none' }}
				onChange={handleFileChange}
			/>
			<Stack className='container'>
				<CustomDataGrid
					loading={isLoading}
					showPagination={true}
					rows={allPackaging?.result || []}
					columns={AllPackagingColumn}
					rowCount={allPackaging?.count ?? 0}
					headerComponent={
						<StyledContainer>
							<Stack className='header-filter-search' gap={2} flexWrap='wrap'>
								<Stack className='filter-Chips'>
									{chipList.map(({ label, value }) => {
										return (
											<Chip
												key={value}
												onClick={() => handleSubNetworkClick(value)}
												color={
													paramsSubNetwork === value ? 'primary' : 'default'
												}
												label={label}
											/>
										)
									})}

									<BiomassFilter isProductionPage={true} />
								</Stack>
								{/* <MixingTypeFilter
									mixingType={
										fetchAllMixTypeQuery?.data?.types?.map((item) => ({
											mixName: item?.name,
											id: item?.id,
										})) || []
									}
								/> */}
							</Stack>
						</StyledContainer>
					}
				/>
			</Stack>
			{paramsDeleteId !== '' ? (
				<DeletePackagingDialog
					open={paramsDeleteId !== ''}
					close={() => {
						setSearchParams((params) => {
							params.delete('deleteId')
							params.delete('isArtisan')
							params.delete('artisanProId')
							params.delete('csinkNetworkId')
							params.delete('deleteKilnId')
							params.delete('deleteSiteId')
							return params
						})
					}}
					save={() => {
						deletePackaging()
					}}>
					<Stack px={5}>
						<Typography variant='h4' textAlign={'center'}>
							Are you sure you want to delete this?
						</Typography>
						<Typography variant='subtitle1' textAlign={'center'}>
							Are you sure you want to delete this, as this will delete all the
							details and you can revert this.
						</Typography>
					</Stack>
				</DeletePackagingDialog>
			) : null}
			{bagInfo !== null ? (
				<DetailedDialog
					title='Bag Details'
					open={bagInfo !== null}
					close={() => setBagInfo(null)}>
					<Stack px={5}>
						{(bagInfo ?? [])?.map((items, index) => (
							<Stack key={index} mb={1} mt={1}>
								<Stack flexDirection='row' justifyContent='space-between'>
									<Stack
										direction='row'
										display='flex'
										alignItems='center'
										gap={2}>
										<Typography variant='body2'>
											{items?.name} &nbsp; :
										</Typography>

										<Typography color='primary.main' variant='body2'>
											{items?.inventoryCount}{' '}
											{items?.inventoryCount > 1 ? 'bags' : 'bag'}
										</Typography>
									</Stack>
								</Stack>
							</Stack>
						))}
					</Stack>
				</DetailedDialog>
			) : null}

			{/* {videoPreivew && (
				<CustomVideoPlayer
					open={videoPreivew}
					close={() => setVideoPreivew(false)}
					videoUrl={videoPreivewUrl}
				/>
			)} */}
			{selectMediaMemo?.length ? (
				<MediaCarousal
					open={!!selectedMedia?.selectedRow?.length}
					handleDelete={({ activeId }) => deleteImageHandler(activeId)}
					deleteMedia
					onClose={() => {
						setSelectedMedia({ selectedRow: null, id: null })
					}}
					gallery={selectMediaMemo ?? []}
					activeMedia={selectedMedia?.id ?? ''}
				/>
			) : null}
		</>
	)
}
const StyledContainer = styled(Stack)(({ theme }) => ({
	'.header-filter-search': {
		padding: theme.spacing(2, 0),
		'.filter-Chips': {
			padding: theme.spacing(1, 2, 0, 2),
			flexDirection: 'row',
			gap: theme.spacing(1),
		},
	},
	'.auto-complete': {
		width: 200,
		'.MuiOutlinedInput-root': {
			height: 38,
			'.MuiInputBase-input': {
				paddingTop: '2.5px',
				fontSize: theme.typography.subtitle1,
			},
		},
	},
}))
