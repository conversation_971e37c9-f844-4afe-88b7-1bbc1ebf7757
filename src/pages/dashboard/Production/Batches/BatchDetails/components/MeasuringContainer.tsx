import { TagComponent } from '@/components'
import { authAxios, useAuthContext } from '@/contexts'
import {
	IMeasuringContainer,
	ISamplingContainer,
	TRenderDrawerComponent,
} from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { TModal } from '@/types'
import { CustomTextField } from '@/utils/components'
import { containerShapesEnum, userRoles } from '@/utils/constant'
import {
	convertCubicMillimeterToLt,
	convertMeterToMillimeter,
	convertMillimeterToMeter,
} from '@/utils/helper'
import { Add, Close, Edit } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import {
	Box,
	Button,
	FormControl,
	IconButton,
	InputLabel,
	ListItemText,
	MenuItem,
	Select,
	SelectChangeEvent,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import React, { useCallback, useMemo, useState } from 'react'
import {
	FieldErrors,
	useForm,
	UseFormClearErrors,
	UseFormRegister,
	UseFormSetError,
	UseFormSetValue,
	UseFormWatch,
} from 'react-hook-form'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

type TProps = Pick<TModal, 'onClose'> &
	Pick<
		TRenderDrawerComponent['data'],
		'sampling' | 'containers' | 'networkDetails' | 'bioCharQty' | 'packedQty'
	>

type EditorItem = {
	id: string
	shortName: string
	volume: number
	count: number
	height: number
	isPartial: boolean
	isNew: boolean
}

// type sampling = {
//     ShortCode: string;
//     id: string;
//     name: string;
// }

type Form = {
	// samplingContainer: sampling;
	measuringContainer: EditorItem[]
	partialMeasuringContainer: EditorItem[]
}

type RenderContainerEditorProps = {
	allContainers: AllMeasuringContainersResponse['containers']
	register: UseFormRegister<Form>
	index: number
	watch: UseFormWatch<Form>
	id: keyof Form
	removeItem: (id: keyof Form, containerId: string) => void
	setValue: UseFormSetValue<Form>
	errors: FieldErrors<Form>
	setError: UseFormSetError<Form>
	clearErrors: UseFormClearErrors<Form>
}

type AllMeasuringContainersResponse = {
	containers: (Omit<IMeasuringContainer, 'shortName'> & {
		ShortName: string
		lowerBase: number
		upperBase: number
	})[]
	count: number
}

export const MeasuringContainer: React.FC<TProps> = ({
	sampling,
	packedQty,
	bioCharQty,
	containers,
	onClose,
	networkDetails,
}) => {
	const theme = useTheme()
	const [isEditModeOn, setIsEditModeOn] = useState<boolean>(false)
	const params = useParams()
	const queryClient = useQueryClient()
	const { userDetails } = useAuthContext()

	const filledContainers = useMemo(
		() =>
			containers
				?.filter((container) => !container?.isPartialFilled)
				?.map((container) => ({
					id: container.id,
					shortName: container.shortName,
					volume: container.volume / container.count,
					count: container.count,
					height: convertMillimeterToMeter(container.height),
					isPartial: container.isPartialFilled,
					isNew: false,
				})),
		[containers]
	)

	const partiallyFilledContainers = useMemo(
		() =>
			containers
				?.filter((container) => container?.isPartialFilled)
				.map((container) => ({
					id: container.id,
					shortName: container.shortName,
					volume: container.volume,
					count: container.count,
					height: convertMillimeterToMeter(container.measuringContainerHeight),
					isPartial: container.isPartialFilled,
					isNew: false,
				})),
		[containers]
	)

	const {
		register,
		watch,
		handleSubmit,
		formState: { errors },
		setValue,
		clearErrors,
		setError,
	} = useForm<Form>({
		defaultValues: {
			// samplingContainer:samplingContainers,
			measuringContainer: filledContainers,
			partialMeasuringContainer: partiallyFilledContainers,
		},
	})

	const allMeasuringContainers = useQuery({
		queryKey: ['allMeasuringContainers'],
		queryFn: async () => {
			const apiRoute = networkDetails?.artisanProId
				? `/artisian-pro/${networkDetails?.artisanProId}/site/${networkDetails?.siteId}/measuring-container?limit=1000&page=0`
				: `/cs-network/${networkDetails?.networkId}/container?limit=1000&page=0`

			return await authAxios.get<AllMeasuringContainersResponse>(apiRoute)
		},
	})

	const calculateVolume = useCallback(
		(id: string, height?: number) => {
			const container = (
				allMeasuringContainers?.data?.data?.containers || []
			).find((item) => item?.id === id)

			const shape = container?.shape
			const new_height = height
				? convertMeterToMillimeter(height)
				: container?.height ?? 0
			if (shape === containerShapesEnum.conical) {
				if (!container) return 0
				const lower_base_radius = (container?.lowerSurfaceDiameter ?? 0) / 2
				const upper_base_radius = (container?.upperSurfaceDiameter ?? 0) / 2
				const original_height = container?.height ?? 0

				const new_upper_base_radius =
					lower_base_radius +
					((upper_base_radius - lower_base_radius) * new_height) /
					original_height

				const new_volume =
					Math.PI *
					new_height *
					((new_upper_base_radius * new_upper_base_radius +
						lower_base_radius * lower_base_radius +
						lower_base_radius * new_upper_base_radius) /
						3.0)
				return convertCubicMillimeterToLt(new_volume)
			} else if (shape === containerShapesEnum.rectangular) {
				if (!container) return 0

				const short_base = container?.lowerBase ?? 0
				const long_base = container?.upperBase ?? 0
				const depth_original = container?.height ?? 0
				const frustum_length = container?.length ?? 0

				const new_long_base =
					short_base + (long_base - short_base) * (new_height / depth_original)

				const new_volume =
					new_height * frustum_length * ((new_long_base + short_base) / 2)
				return convertCubicMillimeterToLt(new_volume)
			} else if (shape === containerShapesEnum.cylinder) {
				const radius = (container?.diameter ?? 0) / 2
				const new_volume = Math.PI * radius ** 2 * new_height
				return convertCubicMillimeterToLt(new_volume)
			} else if (shape === containerShapesEnum.cuboid) {
				const lowerSide = container?.length ?? 0
				const upperSide = container?.breadth ?? 0
				const new_volume =
					(new_height / 3) *
					(upperSide * upperSide +
						lowerSide * lowerSide +
						Math.sqrt(upperSide * upperSide * lowerSide * lowerSide))
				return convertCubicMillimeterToLt(new_volume)
			} else {
				const volume =
					((allMeasuringContainers?.data?.data?.containers || [])?.find(
						(item) => item?.id === id
					)?.volume || 0) * new_height || 0
				const totalVolume =
					volume /
					((allMeasuringContainers?.data?.data?.containers || [])?.find(
						(item) => item?.id === id
					)?.height || 0) || 0

				return totalVolume || 0
			}
		},
		[allMeasuringContainers]
	)

	const removeContainer = useCallback(
		(id: keyof Form, containerId: string) => {
			const containers = watch(id)?.filter(
				(container) => container.id !== containerId
			)
			setValue(id, containers)
		},
		[setValue, watch]
	)

	const addContainer = useCallback(
		(id: keyof Form) => {
			const newContainer = {
				id: Math.random().toString(36).substring(2, 9),
				shortName: '',
				volume: 0,
				count: id === 'partialMeasuringContainer' ? 1 : 0,
				height: 0,
				isPartial: false,
				isNew: true,
			}
			setValue(id, [...watch(id), newContainer])
		},
		[setValue, watch]
	)

	const editContainerMutation = useMutation({
		mutationKey: ['editContainerMutation'],
		mutationFn: async (values: any) => {
			const apiRoute = networkDetails?.artisanProId
				? `/artisian-pro/${networkDetails?.artisanProId}/site/${networkDetails?.siteId
				}/kiln/${networkDetails.kilnId}/process/${params?.id || ''
				}/measuring-container`
				: `cs-network/${networkDetails?.networkId}/site/${networkDetails?.siteId
				}/process/${params?.id || ''}/measuring-container`

			const { data } = await authAxios.put(apiRoute, values)
			return data
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient?.refetchQueries({ queryKey: ['batchDetails', params?.id] })
			onClose()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleEditContainer = useCallback(
		(values: Form) => {
			const filledContainer = values?.measuringContainer?.map((item: any) => ({
				measuringContainerId: item.id,
				count: Number(item.count),
				isFullyFilled: true,
			}))

			const partiallyFilledContainer = values?.partialMeasuringContainer?.map(
				(item) => ({
					measuringContainerId: item.id,
					count: 1,
					height: convertMeterToMillimeter(Number(item.height ?? 0)),
				})
			)
			const totalBioCharQtyFilled = values?.measuringContainer?.reduce(
				(acc: number, curr) =>
					acc + curr.count * calculateVolume(curr?.id),
				0
			)
			const totalPartialBioCharQty = (
				values?.partialMeasuringContainer ?? []
			)?.reduce(
				(acc: number, curr) =>
					acc + calculateVolume(curr?.id, curr?.height),
				0
			)

			const payload = {
				measuringContainer: [...filledContainer, ...partiallyFilledContainer],
				bioCharQuantity: Math.floor(
					totalBioCharQtyFilled + totalPartialBioCharQty
				),
			}
			editContainerMutation.mutate(payload)
		},
		[calculateVolume, editContainerMutation]
	)

	return (
		<StyledStack>
			<Stack className='header'>
				<Typography variant='h4'>Biochar Details</Typography>
				<IconButton onClick={onClose}>
					<Close />
				</IconButton>
			</Stack>
			<Stack>
				<Stack direction='row' alignItems='center' width='100%'>
					<Typography variant='body2' sx={{ ml: 2 }}>
						Measuring Containers
					</Typography>
					<Stack direction='row'>
						{!isEditModeOn && userDetails?.accountType === userRoles.Admin && (
							<Button
								onClick={() => setIsEditModeOn(true)}
								startIcon={<Edit fontSize='small' />}
								sx={{ color: 'common.black' }}></Button>
						)}
					</Stack>
				</Stack>
			</Stack>

			<Stack className='container'>
				<Stack flex={1}>
					<Stack className='view_container'>
						{containers
							?.filter((container) => !container.isPartialFilled)
							?.map((container) => (
								<RenderContainer key={container.id} container={container} />
							))}
						{containers?.filter((container) => container.isPartialFilled)
							?.length ? (
							<>
								<Typography variant='subtitle2' padding={theme.spacing(1.5, 0)}>
									Partial Measuring Container
								</Typography>
								{containers
									?.filter((container) => container.isPartialFilled)
									?.map((container) => (
										<RenderContainer
											key={container.id}
											container={container}
											allContainer={
												allMeasuringContainers?.data?.data?.containers
											}
										/>
									))}
							</>
						) : null}
					</Stack>
					{isEditModeOn && (
						<Stack className='edit_container'>
							<ContainerWrapper
								title='Measuring Container'
								container={watch('measuringContainer')}
								renderItem={(item, index) => (
									<RenderContainerEditor
										key={`${item.id}`}
										allContainers={
											allMeasuringContainers?.data?.data?.containers ?? []
										}
										register={register}
										index={index}
										watch={watch}
										id='measuringContainer'
										removeItem={removeContainer}
										setValue={setValue}
										errors={errors}
										setError={setError}
										clearErrors={clearErrors}
									/>
								)}
								handleAddContainer={() => addContainer('measuringContainer')}
								hideAddBtn={
									(allMeasuringContainers?.data?.data?.containers ?? [])
										?.length === (watch('measuringContainer') ?? [])?.length
								}
							/>

							<ContainerWrapper
								title='Partial Container'
								container={watch('partialMeasuringContainer')}
								renderItem={(item, index) => (
									<RenderContainerEditor
										key={`${item.id}`}
										allContainers={
											allMeasuringContainers?.data?.data?.containers ?? []
										}
										register={register}
										index={index}
										watch={watch}
										id='partialMeasuringContainer'
										removeItem={removeContainer}
										setValue={setValue}
										errors={errors}
										setError={setError}
										clearErrors={clearErrors}
									/>
								)}
								handleAddContainer={() =>
									addContainer('partialMeasuringContainer')
								}
								hideAddBtn={
									(allMeasuringContainers?.data?.data?.containers ?? [])
										?.length ===
									(watch('partialMeasuringContainer') ?? [])?.length
								}
							/>
						</Stack>
					)}

					<Stack className='sampling_container'>
						<RenderSampling
							key={sampling?.id}
							sampling={sampling as ISamplingContainer}
						/>
						<Stack className='view_container'>
							<Stack className='container_item'>
								<Stack>
									<Typography variant='caption' sx={{ color: 'grey.500' }}>
										Total Produced Biochar Qty:
									</Typography>
									<Typography variant='body1'>{`${bioCharQty} ltr`}</Typography>
								</Stack>
								<Stack>
									<Typography variant='caption' sx={{ color: 'grey.500' }}>
										Total Packed Biochar Qty:
									</Typography>
									<Typography variant='body1'>{`${packedQty} ltr`}</Typography>
								</Stack>

								<Stack>
									<Typography variant='caption' sx={{ color: 'grey.500' }}>
										Total UnPacked Biochar Qty:
									</Typography>
									<Typography variant='body1'>
										{bioCharQty - packedQty}
										{' ltr'}
									</Typography>
								</Stack>
							</Stack>
						</Stack>
					</Stack>
				</Stack>

				{isEditModeOn && (
					<Stack
						direction='row'
						justifyContent='space-between'
						mb={10}
						gap={2}
						className='buttonContainer'>
						<Button
							onClick={onClose}
							sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
							Cancel
						</Button>
						<LoadingButton
							loading={editContainerMutation?.isPending}
							disabled={editContainerMutation?.isPending}
							onClick={handleSubmit(handleEditContainer)}
							variant='contained'>
							Save
						</LoadingButton>
					</Stack>
				)}
			</Stack>
		</StyledStack>
	)
}

type ContainerWrapperProps<T> = {
	title: string
	renderItem: (item: T, index: number) => React.ReactNode
	container: T[]
	handleAddContainer: () => void
	hideAddBtn?: boolean
}

const ContainerWrapper = <T,>({
	title,
	container,
	renderItem,
	handleAddContainer,
	hideAddBtn = false,
}: ContainerWrapperProps<T>) => {
	return (
		<Stack rowGap={3} pt={2}>
			<Typography>{title}</Typography>
			<Stack rowGap={3}>{container.map(renderItem)}</Stack>
			{!hideAddBtn && (
				<Stack alignItems='flex-end'>
					<Button
						startIcon={<Add fontSize='small' />}
						size='small'
						onClick={handleAddContainer}>
						Add Container
					</Button>
				</Stack>
			)}
		</Stack>
	)
}

const RenderContainer: React.FC<{
	container: IMeasuringContainer
	allContainer?: Omit<IMeasuringContainer, 'shortName'>[]
}> = ({ container, allContainer }) => {
	const containerVolumeForPartial =
		allContainer?.find((i) => i.id === container?.id)?.volume || 0

	const containerVolume = container?.isPartialFilled
		? containerVolumeForPartial
		: container.volume / container.count

	let shortcode = ''
	const lastCIndex = container?.shortName?.lastIndexOf('C')
	if (lastCIndex !== -1) {
		shortcode = container?.shortName?.substring(lastCIndex)
	}

	return (
		<Box className='container_item' key={container?.id}>
			<TagComponent
				className='item align_start'
				labelClassName='label'
				label='Name'
				value={`${container.name || ''} ${container.shortName && `(${shortcode})`
					}`}
			/>
			<TagComponent
				className='item'
				label='Container Count'
				value={container?.count || 0}
			/>
			{container?.isPartialFilled && (
				<TagComponent
					className='item'
					label='Entered Height'
					value={`${convertMillimeterToMeter(container?.measuringContainerHeight) || 0
						} m`}
				/>
			)}
			{container?.isPartialFilled && (
				<TagComponent
					className='item'
					label='Container Height'
					value={`${convertMillimeterToMeter(container?.height) || 0} m`}
				/>
			)}
			<TagComponent
				className='item'
				label='Total Volume'
				value={`${container?.volume || 0} ${container?.volume > 1 ? 'ltrs' : 'ltr'
					}`}
			/>
			<TagComponent
				className='item'
				label='Container Volume'
				value={`${Math.floor(containerVolume) || 0} ltr`}
			/>
		</Box>
	)
}

const RenderSampling: React.FC<{ sampling: ISamplingContainer }> = ({
	sampling,
}) => {
	return (
		<Box key={sampling?.id} className='samplingBorder'>
			<Typography sx={{ color: 'grey.500' }} variant='caption'>
				Sampling Container
			</Typography>
			<Stack direction='row' gap={theme.spacing(1)}>
				<Typography variant='body2'>Sampling Container</Typography>
				<Typography sx={{ color: 'grey.500' }}>
					{`${sampling?.name} ${sampling?.ShortCode && `(${sampling?.ShortCode})`}` || ''}
				</Typography>
			</Stack>
		</Box>
	)
}
const RenderContainerEditor: React.FC<RenderContainerEditorProps> = ({
	allContainers,
	register,
	index,
	watch,
	id,
	removeItem,
	setValue,
	clearErrors,
	errors,
	setError,
}) => {
	const onSelectChange = useCallback(
		(event: SelectChangeEvent) => {
			const temp = allContainers?.find((i) => i.id === event.target.value)
			const newContainer = {
				id: temp?.id ?? '',
				shortName: temp?.ShortName ?? '',
				volume: temp?.volume ?? 0,
				count: 0,
				height: 0,
				isPartial: temp?.isPartialFilled ?? false,
				isNew: true,
			}
			setValue(`${id}.${index}`, newContainer)
		},
		[allContainers, id, index, setValue]
	)

	const onCountChange = useCallback(
		(event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
			if (id === 'measuringContainer') {
				setValue(`${id}.${index}.count`, parseInt(event.target.value))
			} else {
				clearErrors(`${id}.${index}.id`)
				const containerHeight = convertMillimeterToMeter(
					allContainers?.find(
						(item) =>
							item?.id === watch(`partialMeasuringContainer.${index}.id`)
					)?.height ?? 0
				)
				if (Number(event.target?.value) > containerHeight) {
					setError(`${id}.${index}.id`, {
						message: `Height of Partial Containers can't be more than Container`,
					})
				}
			}
		},
		[allContainers, clearErrors, id, index, setError, setValue, watch]
	)

	return (
		<Stack className='editor_item'>
			<FormControl fullWidth className='form_control'>
				<InputLabel>Container</InputLabel>
				<Select
					className='select_input'
					label='Container'
					renderValue={(selected) => {
						const selectedContainer = allContainers.find(
							(item) => item.id === selected
						)
						return selectedContainer?.ShortName
					}}
					value={watch(`${id}.${index}.id`)}
					onChange={onSelectChange}>
					{allContainers
						?.filter(
							(item) =>
								!watch(id)
									?.map((i) => i.id)
									?.includes(item.id)
						)
						?.map((container) => (
							<MenuItem key={container?.id} value={container?.id}>
								<Stack>
									<ListItemText>{container?.ShortName}</ListItemText>
									<ListItemText>Volume: {container?.volume} ltr</ListItemText>
								</Stack>
							</MenuItem>
						))}
				</Select>
			</FormControl>
			<CustomTextField
				hideNumberArrows
				className='text_input'
				fullWidth
				label={id === 'measuringContainer' ? 'Count' : 'Height'}
				type='number'
				{...register(
					`${id}.${index}.${id === 'partialMeasuringContainer' ? 'height' : 'count'
					}`
				)}
				{...(id === 'partialMeasuringContainer' && {
					error: !!errors?.partialMeasuringContainer?.[index]?.id?.message,
					helperText: errors?.partialMeasuringContainer?.[index]?.id?.message,
				})}
				onChange={onCountChange}
				disabled={
					!allContainers?.find((item) => item.id === watch(`${id}.${index}.id`))
						?.id
				}
				InputLabelProps={{
					shrink: true,
				}}
			/>
			<IconButton onClick={() => removeItem(id, watch(`${id}.${index}.id`))}>
				<Box
					component='img'
					src='/images/delete_primary_color.svg'
					className='delete_icon'
				/>
			</IconButton>
		</Stack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	height: '100vh',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']
			}`,
	},
	'.container': {
		padding: theme.spacing(0, 2),
		gap: theme.spacing(5),
		flex: 1,
		'.sampling_container': {
			// marginTop: theme.spacing(4),
			paddingTop: theme.spacing(2),
			// borderTop: `${theme.spacing(0.125)} solid ${
			// 	theme.palette.neutral['100']
			// }`,

			'.view_container': {
				paddingTop: theme.spacing(2),
				paddingBottom: theme.spacing(3),
				gap: theme.spacing(5),
				'.container_item': {
					display: 'grid',
					gridTemplateColumns: '1fr 1fr',
					gap: theme.spacing(2),
					'.item': {
						':nth-of-type(even)': {
							justifyContent: 'end',
						},
					},
				},
			},
			'.samplingBorder': {
				// borderTop: `${theme.spacing(0.125)} solid ${
				// 	theme.palette.neutral['100']
				// }`,
			},
		},
		'.view_container': {
			gap: theme.spacing(2),
			'.container_item': {
				display: 'grid',
				gridTemplateColumns: '1fr 1fr',
				'.align_start': {
					alignItems: 'flex-start',
				},
				borderBottom: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']
					}`,
				gap: theme.spacing(1),
				'.item': {
					'.label': {
						minWidth: 'fit-content',
					},
					':nth-of-type(even)': {
						justifyContent: 'end',
					},
				},
			},
		},
		'.edit_container': {
			gap: theme.spacing(2),
			marginTop: theme.spacing(2),
			'.editor_item': {
				flexDirection: 'row',
				alignItems: 'start',
				columnGap: theme.spacing(2),
				'.form_control': {
					height: '100%',
					'.select_input': {
						height: theme.spacing(7),
					},
				},
				'.delete_icon': {
					width: theme.spacing(3),
					height: theme.spacing(3),
				},
			},
		},
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
		},
	},
}))
