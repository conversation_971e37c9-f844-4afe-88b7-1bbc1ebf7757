import fireIcon from '@/assets/icons/fire.svg'
import ImageOutlinedIcon from '@mui/icons-material/ImageOutlined'
import PlayCircleOutlinedIcon from '@mui/icons-material/PlayCircleOutlined'
import ArrowLeftRoundedIcon from '@mui/icons-material/ArrowLeftRounded'
import CloseRoundedIcon from '@mui/icons-material/CloseRounded'
import DoneIcon from '@mui/icons-material/Done'
import QueryBuilderRoundedIcon from '@mui/icons-material/QueryBuilderRounded'
import ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { BatchStatusTypeEnum, userRoles } from '@/utils/constant'

import {
	alpha,
	Box,
	Button,
	CircularProgress,
	Container,
	Grid,
	IconButton,
	Menu,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import React, { ReactElement, useCallback, useMemo, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
	AcceptOrReject,
	ActionInformationDrawer,
	CustomChip,
	MediaCarousal,
} from '../../../../../components'
import { CustomizedTimeline } from './CustomeTimeLine'
import { useBatchDetails } from './useBatchDetails'
import {
	BatchstatusType,
	ESupportingDocStep,
	IBatchDetails,
	IMediaWithDeleteParams,
	ISamplingContainer,
	TDrawerState,
	TRenderDrawerComponent,
} from '@/interfaces'
import { ThemeWrapper } from '@/lib'
import { BatchComments, MeasuringContainer } from './components'
import { BiomassDetails } from './components/BiomassDetails'
import { Info, Warning } from '@mui/icons-material'
import {
	MultipleTypeImageUploader,
	Payload,
} from '@/components/MultipleTypeImageUploader'
import { MediaUploadModal } from '@/components/MediaUploadModal'
import {
	formatProductionTime,
	getGoogleMapLink,
	roundNumber,
} from '@/utils/helper'
import { Confirmation } from '@/components/Confirmation'
import { ApproveRejectedDialog } from './components/ApproveRejectedDialog'

const formatTime = (
	startTime: string | undefined,
	endTime: string | undefined
) => {
	const { dateString, timingDifference, times, inProgress } =
		formatProductionTime(startTime, endTime)
	return (
		<Stack spacing={0.5}>
			<Stack direction='row' spacing={1}>
				<Typography variant='subtitle1'>{dateString}</Typography>
				{timingDifference && (
					<Typography variant='subtitle1'>{timingDifference}</Typography>
				)}
			</Stack>
			<Stack>
				{inProgress && (
					<Typography variant='subtitle1'>(In progress)</Typography>
				)}
			</Stack>
			<Stack>
				{times && <Typography variant='subtitle1'>{times}</Typography>}
			</Stack>
		</Stack>
	)
}

export const BatchDetails = ({
	id,
	showHeader = true,
}: {
	id?: string
	showHeader?: boolean
}) => {
	const navigate = useNavigate()
	const theme = useTheme()
	const {
		batchDetails,
		markedMedia,
		userDetails,
		handleDeleteMedia,
		handleSaveImage,
		handleSaveClickVideo,
		updateProcessStatusToNotAssessetMutate,
		showNotAssessetModal,
		setShowNotAssessetModal,
		BatchHistory,
		batchDetailsQuery,
	} = useBatchDetails({ publicId: id })
	const { mutate: handleUpdateProcessStatusToNotAssessetMutate } =
		updateProcessStatusToNotAssessetMutate
	const [supportingDocStep, setSupportingDocStep] = useState<
		keyof typeof ESupportingDocStep
	>(ESupportingDocStep.biomassCollection)
	const [showAcceptOrRejectDialog, setShowAcceptOrRejectDialog] =
		useState<boolean>(false)
	const [showMarkedDeleteModal, setshowMarkedDeleteModal] = useState(false)
	const [showImageUploadModal, setShowImageUploadModal] = useState(false)
	const [showMediaUploadModal, setShowMediaUploadModal] = useState(false)
	const [showInfoPopup, setShowInfoPopup] = useState(false)
	const [anchorElForPopUp, setAnchorElForPopUp] = useState<null | HTMLElement>(
		null
	)

	const currentBatchDetail = {
		artisianProId: batchDetails?.kilnProcessDetail?.artisanProId,
		csNetworkId: batchDetails?.kilnProcessDetail?.networkId,
		siteId: batchDetails?.kilnProcessDetail?.siteId,
		kilnId: batchDetails?.kilnProcessDetail?.kilnId,
		processId: batchDetails?.kilnProcessDetail?.id,
	}

	const handleGoBack = useCallback(() => {
		navigate(-1)
	}, [navigate])

	const handleMouseEnterInfoIcon = (event: React.MouseEvent<HTMLElement>) => {
		setAnchorElForPopUp(event.currentTarget)
		setShowAcceptOrRejectDialog(false)
		setShowInfoPopup(true)
	}
	const handleMouseLeaveInfoIcon = () => {
		setShowInfoPopup(false)
	}

	const [drawerState, setDrawerState] = useState<TDrawerState>({
		open: false,
		type: 'comment',
		data: {
			bioCharQty: 0,
			packedQty: 0,
			biomass: 0,
			sampling: {
				ShortCode: '',
				id: '',
				name: '',
			},
			containers: [],
			networkDetails: {
				artisanProId: '',
				siteId: '',
				networkId: '',
				kilnId: '',
			},
		},
	})

	const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
	const open = Boolean(anchorEl)

	const handleCloseMenu = () => {
		setAnchorEl(null)
	}

	const media = useMemo(() => {
		const media: IMediaWithDeleteParams[] = []

		markedMedia?.doneImages?.forEach((img) => {
			media.push({
				...img,
				type: 'image' as IMediaWithDeleteParams['type'],
			})
		})

		markedMedia?.images?.forEach((img) => {
			media.push({
				...img,
				type: 'image' as IMediaWithDeleteParams['type'],
			})
		})
		markedMedia?.stackImages?.forEach((img) => {
			media.push({
				...img,
				type: 'image' as IMediaWithDeleteParams['type'],
			})
		})
		markedMedia?.temperatureImages?.forEach((img) => {
			media.push({
				...img,
				type: 'image' as IMediaWithDeleteParams['type'],
			})
		})
		markedMedia?.videos?.forEach((video) => {
			media.push({
				...video,
				type: 'video' as IMediaWithDeleteParams['type'],
			})
		})

		return media
	}, [markedMedia])
	const networkStatus: {
		[key: string]: { [key: string]: string | ReactElement }
	} = {
		approved: {
			className: 'approved',
			icon: <DoneIcon color='success' fontSize='small' />,
		},
		pending: {
			className: 'pending',
			icon: <QueryBuilderRoundedIcon color='warning' fontSize='small' />,
		},
		rejected: {
			className: 'rejected',
			icon: <CloseRoundedIcon color='error' fontSize='small' />,
		},
		'admin-approved': {
			className: 'approved',
			icon: <DoneIcon color='success' fontSize='small' />,
		},
		'admin-rejected': {
			className: 'rejected',
			icon: <CloseRoundedIcon color='error' fontSize='small' />,
		},
		['not_assessed']: {
			class: 'pending',
			icon: <QueryBuilderRoundedIcon color='warning' fontSize='small' />,
		},
		started: {
			class: 'pending',
			icon: <QueryBuilderRoundedIcon color='warning' fontSize='small' />,
		},
		compensate: {
			class: 'compensate',
		},
	}

	const batchDetailsList = useMemo(
		() => [
			{
				label: 'Biomass Aggregator',
				value: `${
					batchDetails?.kilnProcessDetail?.biomassAggregatorName ?? ''
				} (${
					batchDetails?.kilnProcessDetail?.biomassAggregatorShortName ?? ''
				})`,
			},
			...(batchDetails?.kilnProcessDetail?.artisanProName
				? [
						{
							label: 'Artisan Pro',
							value: `${batchDetails?.kilnProcessDetail?.artisanProName} (${batchDetails?.kilnProcessDetail?.artisanProShortName})`,
						},
						// eslint-disable-next-line no-mixed-spaces-and-tabs
				  ]
				: [
						{
							label: 'C-Sink Network',
							value: `${batchDetails?.kilnProcessDetail?.networkName} (${batchDetails?.kilnProcessDetail?.networkShortName})`,
						},
						// eslint-disable-next-line no-mixed-spaces-and-tabs
				  ]),
			{
				label: 'Site Name',
				value: `${batchDetails?.kilnProcessDetail?.siteName ?? ''}`,
			},
			{
				label: 'Crop Name',
				value: batchDetails?.kilnProcessDetail?.cropName ?? '',
			},
			{
				label: 'Carbon %',
				value: `${batchDetails?.kilnProcessDetail?.carbonPercentage ?? ''} ${
					batchDetails?.kilnProcessDetail?.carbonPercentage !== null ? '%' : ''
				}`,
			},

			{
				label: (
					<Typography className='title' variant='subtitle2'>
						Emission (CO<sub>2</sub>) | Methane
					</Typography>
				),
				value: (
					<Typography variant='subtitle1'>
						{(batchDetails?.kilnProcessDetail?.co2Emission ?? 0) / 1000} tCO
						<sub>2</sub> |{' '}
						{batchDetails?.kilnProcessDetail?.methaneEmission ?? 0} kg
					</Typography>
				),
			},
			{
				label: 'Production Time',
				value: formatTime(
					batchDetails?.kilnProcessDetail?.createdAt,
					batchDetails?.kilnProcessDetail?.endTime
				),
			},
			{
				label: 'Kiln Name',
				// value: `${batchDetails?.kilnProcessDetail?.kilnName ?? ''}
				// 	(${batchDetails?.kilnProcessDetail?.kilnVolume ?? ''} ltr)`,
				value: (
					<Typography>
						<Button
							component='a'
							href={getGoogleMapLink(
								String(
									batchDetails?.kilnProcessDetail?.kilnCoordinate?.x ?? '0'
								),
								String(
									batchDetails?.kilnProcessDetail?.kilnCoordinate?.y ?? '0'
								)
							)}
							target='_blank'
							variant='text'
							sx={{
								p: 0,
								width: 'fit-content',
								color: 'black',
							}}>
							<Typography
								variant='subtitle1'
								sx={{
									textDecoration: 'underline',
								}}>
								{`${batchDetails?.kilnProcessDetail?.kilnName ?? ''}
								(${batchDetails?.kilnProcessDetail?.kilnVolume ?? ''} ltr)`}
							</Typography>
						</Button>
					</Typography>
				),
			},
			{
				label: 'Biomass Qty',
				// value: `${batchDetails?.kilnProcessDetail?.biomassQty ?? 0} kg`,
				value: (
					<Typography
						variant='subtitle1'
						onClick={() => {
							if (
								Number(batchDetails?.kilnProcessDetail?.biomassQty ?? 0) !== 0
							) {
								handleOpenBiomassDetails()
								handleCloseMenu()
							}
						}}
						sx={{
							cursor:
								Number(batchDetails?.kilnProcessDetail?.biomassQty ?? 0) !== 0
									? 'pointer'
									: 'default',
						}}>
						{batchDetails?.kilnProcessDetail?.biomassQty ?? 0} kg
					</Typography>
				),
			},
			{
				label: 'Biochar Qty',
				value: (
					<Typography
						variant='subtitle1'
						onClick={() => {
							if (Number(batchDetails?.kilnProcessDetail?.bioCharQty) !== 0) {
								handleCloseMenu()
								handleOpenMeasuringContainerDrawer()
							}
						}}
						sx={{
							cursor:
								Number(batchDetails?.kilnProcessDetail?.bioCharQty) !== 0
									? 'pointer'
									: 'default',
						}}>
						{`${batchDetails?.kilnProcessDetail?.bioCharQty ?? 0}`} ltrs
					</Typography>
				),
			},

			{
				label: 'Carbon Credit',
				value: (
					<Typography variant='subtitle1'>
						{roundNumber(
							Number(
								batchDetails?.kilnProcessDetail?.actualCarbonCredits ?? 0
							) / 1000,
							4
						)}{' '}
						tCO<sub>2</sub>
					</Typography>
				),
			},
			{
				label: 'Short Term Carbon Sink',
				value: (
					<Typography variant='subtitle1'>
						{(batchDetails?.kilnProcessDetail?.shortTermCarbonSink ?? 0) / 1000}{' '}
						tCO<sub>2</sub>
					</Typography>
				),
			},
		],
		[batchDetails?.kilnProcessDetail]
	)

	const batchCustomChips = {
		icon: networkStatus?.[batchDetails?.kilnProcessDetail?.status as string]
			?.icon,
		class:
			networkStatus?.[batchDetails?.kilnProcessDetail?.status as string]
				?.className,
	}

	const handleOpenCommentDrawer = useCallback(() => {
		setDrawerState((prev) => ({
			...prev,
			open: true,
			type: 'comment',
		}))
	}, [])

	const handleOpenMeasuringContainerDrawer = useCallback(() => {
		const formattedSampling: ISamplingContainer = {
			ShortCode: batchDetails?.samplingContainer?.ShortCode || '',
			id: batchDetails?.samplingContainer?.id || '',
			name: batchDetails?.samplingContainer?.name || '',
		}
		setDrawerState((prev) => ({
			...prev,
			open: true,
			type: 'container',
			data: {
				...prev.data,
				containers: batchDetails?.measuringContainers ?? [],
				bioCharQty: batchDetails?.kilnProcessDetail?.bioCharQty ?? 0,
				packedQty: batchDetails?.totalPackedQuantity ?? 0,
				sampling: formattedSampling,
				networkDetails: {
					artisanProId: batchDetails?.kilnProcessDetail?.artisanProId || '',
					siteId: batchDetails?.kilnProcessDetail?.siteId || '',
					networkId: batchDetails?.kilnProcessDetail?.networkId || '',
					kilnId: batchDetails?.kilnProcessDetail?.kilnId || '',
				},
			},
		}))
	}, [
		batchDetails?.kilnProcessDetail?.artisanProId,
		batchDetails?.kilnProcessDetail?.bioCharQty,
		batchDetails?.kilnProcessDetail?.kilnId,
		batchDetails?.kilnProcessDetail?.networkId,
		batchDetails?.kilnProcessDetail?.siteId,
		batchDetails?.measuringContainers,
		batchDetails?.samplingContainer?.ShortCode,
		batchDetails?.samplingContainer?.id,
		batchDetails?.samplingContainer?.name,
		batchDetails?.totalPackedQuantity,
	])

	const handleOpenBiomassDetails = useCallback(() => {
		setDrawerState((prev) => ({
			...prev,
			open: true,
			type: 'biomass',
			data: {
				...prev.data,
				biomass: batchDetails?.kilnProcessDetail?.biomassQty || 0,
				containers: batchDetails?.measuringContainers ?? [],
				networkDetails: {
					artisanProId: batchDetails?.kilnProcessDetail?.artisanProId || '',
					siteId: batchDetails?.kilnProcessDetail?.siteId || '',
					networkId: batchDetails?.kilnProcessDetail?.networkId || '',
					kilnId: batchDetails?.kilnProcessDetail?.kilnId || '',
				},
			},
		}))
	}, [
		batchDetails?.kilnProcessDetail?.artisanProId,
		batchDetails?.kilnProcessDetail?.biomassQty,
		batchDetails?.kilnProcessDetail?.kilnId,
		batchDetails?.kilnProcessDetail?.networkId,
		batchDetails?.kilnProcessDetail?.siteId,
		batchDetails?.measuringContainers,
	])

	const allMarkedDeletedMedia = useMemo(() => {
		const media: IMediaWithDeleteParams[] = []
		markedMedia?.stackImages?.map((img) =>
			media.push({ ...img, type: 'image', isProductionMedia: true })
		)

		markedMedia?.images?.map((img) =>
			media.push({ ...img, type: 'image', isProductionMedia: true })
		)
		markedMedia?.videos?.map((i) =>
			media.push({
				...i,
				type: 'video',
				isProductionMedia: true,
			})
		)
		markedMedia?.temperatureImages?.map((i) =>
			media.push({ ...i, type: 'image', isProductionMedia: true })
		)
		markedMedia?.doneImages?.map((i) =>
			media.push({ ...i, type: 'image', isProductionMedia: true })
		)

		return media
	}, [markedMedia])

	const permissionToShowMarkedDeletedMediaBtn = useMemo(() => {
		const roleBasePermission = [
			userRoles.Admin,
			userRoles.CsinkManager,
		]?.includes(userDetails?.accountType as userRoles)
		const processStatusBasePermission = ![
			BatchStatusTypeEnum.Rejected,
			BatchStatusTypeEnum.AdminRejected,
		]?.includes(batchDetails?.kilnProcessDetail?.status as BatchStatusTypeEnum)
		const dataAvailableBasePermission = allMarkedDeletedMedia?.length > 0

		return (
			roleBasePermission &&
			processStatusBasePermission &&
			dataAvailableBasePermission
		)
	}, [
		allMarkedDeletedMedia?.length,
		batchDetails?.kilnProcessDetail?.status,
		userDetails?.accountType,
	])

	const permissionToShowNotAssessedMediaBtn = useMemo(() => {
		const roleBasePermission = userDetails?.accountType === userRoles.Admin
		const processStatusBasePermission = [
			BatchStatusTypeEnum.AdminApproved,
			BatchStatusTypeEnum.Approved,
			BatchStatusTypeEnum.Rejected,
			BatchStatusTypeEnum.AdminRejected,
			BatchStatusTypeEnum.Compensated,
		]?.includes(batchDetails?.kilnProcessDetail?.status as BatchStatusTypeEnum)

		return roleBasePermission && processStatusBasePermission
	}, [batchDetails?.kilnProcessDetail?.status, userDetails?.accountType])

	const kilnStatus = batchDetails?.kilnProcessDetail?.status
	let displayStatus = ''

	switch (kilnStatus) {
		case 'not_assessed':
			displayStatus = 'not assessed'
			break
		case 'compensate':
			displayStatus = 'Compensated'
			break
		default:
			displayStatus = kilnStatus ?? ''
			break
	}
	if (batchDetailsQuery.isPending) {
		return (
			<StyledBox>
				<CircularProgress />
			</StyledBox>
		)
	}
	return (
		<>
			<ActionInformationDrawer
				open={drawerState.open}
				anchor='right'
				onClose={() => setDrawerState((prev) => ({ ...prev, open: false }))}
				component={
					<RenderDrawerComponent
						onClose={() => setDrawerState((prev) => ({ ...prev, open: false }))}
						type={drawerState.type}
						data={drawerState.data}
					/>
				}
			/>
			{showAcceptOrRejectDialog ? (
				<AcceptOrReject
					open={showAcceptOrRejectDialog}
					onClose={() => {
						setShowAcceptOrRejectDialog(false)
					}}
					networkId={batchDetails?.kilnProcessDetail?.networkId}
					artisanProId={batchDetails?.kilnProcessDetail?.artisanProId}
					kilnId={batchDetails?.kilnProcessDetail?.kilnId}
					siteId={batchDetails?.kilnProcessDetail?.siteId}
					reason={batchDetails?.kilnProcessDetail?.reason}
					status={batchDetails?.kilnProcessDetail?.status as BatchstatusType}
					approvalName={
						batchDetails?.kilnProcessDetail?.statusAccessedByName as string
					}
					approvalDate={
						(batchDetails?.kilnProcessDetail?.statusAccessedTime as Date) ??
						new Date()
					}
				/>
			) : null}
			{showImageUploadModal && userDetails?.accountType === userRoles.Admin ? (
				<MultipleTypeImageUploader
					open={showImageUploadModal}
					handleClose={() => {
						setShowImageUploadModal(false)
					}}
					batchDetail={currentBatchDetail}
					handleSaveClick={(payload: Payload) => {
						handleSaveImage(payload)
						setShowImageUploadModal(false)
					}}
					handleCancelClick={() => {
						setShowImageUploadModal(false)
					}}
					title={<Typography>Choose Image Type</Typography>}
					label={<Typography>Choose Image Type</Typography>}
					dialogeElement=''
				/>
			) : null}
			{showMediaUploadModal && userDetails?.accountType === userRoles.Admin ? (
				<MediaUploadModal
					open={showMediaUploadModal}
					handleClose={() => {
						setShowMediaUploadModal(false)
					}}
					batchDetail={currentBatchDetail}
					handleSaveClick={(payload: Payload) => {
						handleSaveClickVideo(payload)
						setShowMediaUploadModal(false)
					}}
					handleCancelClick={() => {
						setShowMediaUploadModal(false)
					}}
					title={<Typography>Upload Video</Typography>}
					labelThumbnail={<Typography>Select Thumbnail Image</Typography>}
					labelVideo={<Typography>Select Process Video</Typography>}
					dialogeElement=''
				/>
			) : null}
			<Container
				sx={{
					p: 0,
					'&.MuiContainer-root': {
						maxWidth: 'xl',
						paddingLeft: '0',
						paddingRight: '0',
					},
				}}>
				<StyledContained>
					{showHeader ? (
						<Stack className='header-navigation'>
							<Stack className='header'>
								<Stack direction='row' alignItems='center'>
									<Button
										className='batch-button'
										variant='text'
										onClick={handleGoBack}
										startIcon={<ArrowLeftRoundedIcon className='arrow-icon' />}>
										Batches
									</Button>
									<Typography
										variant='body1'
										color={theme.palette.neutral['500']}>
										/ {batchDetails?.kilnProcessDetail?.processShortName}{' '}
									</Typography>
								</Stack>
								<Stack className='header-desc'>
									{userDetails?.accountType === userRoles.Admin && (
										<Stack direction='row' spacing={1} alignItems={'center'}>
											<IconButton
												onClick={() => {
													setShowMediaUploadModal((prev) => !prev)
												}}>
												<PlayCircleOutlinedIcon />
											</IconButton>
											<IconButton
												onClick={() => {
													setShowImageUploadModal((prev) => !prev)
												}}>
												<ImageOutlinedIcon />
											</IconButton>
										</Stack>
									)}
									<Stack direction='row' spacing={1} alignItems={'center'}>
										<Box
											component='img'
											src={fireIcon}
											alt='fire-icon'
											height={20}
											width={20}
										/>
										<Typography variant='body1'>
											Kiln: {batchDetails?.kilnProcessDetail?.kilnName}{' '}
										</Typography>
									</Stack>
									<Box
										onMouseEnter={(e) => handleMouseEnterInfoIcon(e)}
										onMouseLeave={handleMouseLeaveInfoIcon}
										display='inline-block'>
										<IconButton sx={{ cursor: 'pointer' }} color='default'>
											<Info />
										</IconButton>
										{showInfoPopup && (
											<ApproveRejectedDialog
												isInfoPopup={true}
												batchHistory={BatchHistory}
												showInfoPopup={showInfoPopup}
												setShowInfoPopup={setShowInfoPopup}
												anchorEl={anchorElForPopUp}
												statusLabelName='Rejected By: '
												statusLabelDate='Rejected '
												approvalName={
													batchDetails?.kilnProcessDetail
														?.statusAccessedByName as string
												}
												approvalDate={
													(batchDetails?.kilnProcessDetail
														?.statusAccessedTime as Date) ?? new Date()
												}
												reason={batchDetails?.kilnProcessDetail?.reason || ''}
											/>
										)}
									</Box>
									{permissionToShowMarkedDeletedMediaBtn ? (
										<CustomChip
											appliedClass={
												networkStatus['rejected'].className as string
											}
											onClick={() => setshowMarkedDeleteModal(true)}
											icon={<Warning color='error' />}
											label={'Deletion Requested'}
										/>
									) : null}
									{permissionToShowNotAssessedMediaBtn ? (
										<IconButton
											sx={{ cursor: 'pointer' }}
											color='error'
											disabled={!!batchDetails?.mixingDetails?.length}
											onClick={() => setShowNotAssessetModal(true)}>
											<Warning />
										</IconButton>
									) : null}
									{networkStatus?.[
										batchDetails?.kilnProcessDetail?.status as string
									] ? (
										<CustomChip
											setShowAcceptOrRejectDialog={setShowAcceptOrRejectDialog}
											icon={batchCustomChips?.icon as ReactElement}
											appliedClass={batchCustomChips?.class as string}
											label={displayStatus}
										/>
									) : null}
									<Stack>
										<IconButton
											onClick={() => {
												handleCloseMenu()
												handleOpenCommentDrawer()
											}}>
											{/* <MoreVert /> */}
											<ChatBubbleOutlineIcon />
										</IconButton>

										<Menu
											id='basic-menu'
											anchorEl={anchorEl}
											open={open}
											onClose={handleCloseMenu}
											sx={{
												'.MuiMenu-paper': {
													borderRadius: 2,
												},
											}}
											MenuListProps={{
												'aria-labelledby': 'basic-button',
											}}></Menu>
									</Stack>
								</Stack>
							</Stack>
						</Stack>
					) : null}
					<Stack className='container' spacing={5}>
						<Stack className='batch-detail-container'>
							<Grid container>
								{batchDetailsList.map(({ label, value }, index: number) => (
									<Grid
										key={index}
										item
										xs={6}
										sm={4}
										md={2}
										className='desc-grid-item'>
										<Stack className='desc' spacing={1}>
											<Box display='flex' alignItems='center'>
												{typeof label === 'string' ? (
													<Typography
														className='title'
														variant='subtitle2'
														onClick={() => {
															if (
																Number(value) !== 0 &&
																label === 'Biomass Qty'
															) {
																handleOpenBiomassDetails()
																handleCloseMenu()
															} else if (
																Number(value) !== 0 &&
																label === 'Biochar Qty'
															) {
																handleCloseMenu()
																handleOpenMeasuringContainerDrawer()
															} else if (
																label === 'Crop Name' &&
																(userDetails?.accountType === userRoles.Admin ||
																	userDetails?.accountType ===
																		userRoles.CsinkManager)
															) {
																navigate('/dashboard/admin/biomass')
															}
														}}
														sx={{
															cursor:
																Number(value) !== 0 ? 'pointer' : 'default',
														}}>
														{label}
													</Typography>
												) : (
													label
												)}
												{(label === 'Biomass Qty' ||
													(label === 'Biochar Qty' &&
														Number((value as string) || 0) !== 0)) &&
													!id && (
														<InfoOutlinedIcon
															fontSize='small'
															onClick={() => {
																if (
																	Number(value) !== 0 &&
																	label === 'Biomass Qty'
																) {
																	handleOpenBiomassDetails()
																	handleCloseMenu()
																} else if (
																	Number(value) !== 0 &&
																	label === 'Biochar Qty'
																) {
																	handleCloseMenu()
																	handleOpenMeasuringContainerDrawer()
																}
															}}
															sx={{
																cursor:
																	Number(value) !== 0 ? 'pointer' : 'default',
															}}
														/>
													)}
											</Box>

											{typeof value === 'string' ? (
												<Typography variant='subtitle1'>{value}</Typography>
											) : (
												value
											)}
										</Stack>
									</Grid>
								))}
							</Grid>
						</Stack>
						<Grid container>
							<Grid item></Grid>
							<Grid item xs={12} alignItems={'flex-start'}>
								<ThemeWrapper>
									<CustomizedTimeline
										batchDetails={batchDetails}
										// media={media}
										handleDeleteMedia={handleDeleteMedia}
										data={batchDetails as IBatchDetails}
										supportingDocStep={supportingDocStep}
										setSupportingDocStep={setSupportingDocStep}
									/>
								</ThemeWrapper>
								<hr
									style={{
										borderTop: `1px solid ${theme.palette.neutral[100]}`,
									}}
								/>
							</Grid>
						</Grid>
					</Stack>
					{showMarkedDeleteModal ? (
						<MediaCarousal
							open={showMarkedDeleteModal && media.length > 0}
							handleDelete={handleDeleteMedia}
							deleteMedia
							onClose={() => {
								setshowMarkedDeleteModal(false)
							}}
							gallery={media}
						/>
					) : null}
					{showNotAssessetModal && (
						<Confirmation
							open={showNotAssessetModal}
							handleClose={() => setShowNotAssessetModal(false)}
							handleNoClick={() => setShowNotAssessetModal(false)}
							handleYesClick={() =>
								handleUpdateProcessStatusToNotAssessetMutate()
							}
							confirmationText={
								'Are you sure you want to mark this batch as "Not Assessed"?'
							}
						/>
					)}
				</StyledContained>
			</Container>
		</>
	)
}

const RenderDrawerComponent: React.FC<TRenderDrawerComponent> = ({
	type,
	data,
	onClose,
}) => {
	switch (type) {
		case 'comment':
			return <BatchComments onClose={onClose} />
		case 'container':
			return (
				<MeasuringContainer
					packedQty={data?.packedQty ?? 0}
					bioCharQty={data?.bioCharQty ?? 0}
					sampling={data?.sampling}
					onClose={onClose}
					containers={data?.containers ?? []}
					networkDetails={data?.networkDetails}
				/>
			)
		case 'biomass':
			return (
				<BiomassDetails
					onClose={onClose}
					biomass={data?.biomass ?? ''}
					networkDetails={data?.networkDetails}
				/>
			)
		default:
			return null
	}
}

const StyledBox = styled(Box)(() => ({
	display: 'flex',
	justifyContent: 'center',
	alignItems: 'center',
	height: '100vh',
	width: '100%',
}))

const StyledContained = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header-navigation': {
		padding: theme.spacing(4, 3, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		alignItems: 'center',
		flexDirection: 'row',

		'.header': {
			width: '100%',
			flexDirection: 'row',
			justifyContent: 'space-between',
			'.header-desc': {
				gap: theme.spacing(4),
				flexDirection: 'row',
				alignItems: 'center',
			},
			'.batch-button': {
				color: theme.palette.neutral['500'],
				...theme.typography.body1,
				'.arrow-icon': {
					color: theme.palette.neutral['500'],
				},
			},
		},
	},
	'.container': {
		padding: theme.spacing(1, 2.2),

		'.batch-detail-container': {
			width: '100%',
			height: '100%',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
			'.desc-grid-item': {
				padding: theme.spacing(2),
				'.desc': {
					'.title': {
						color: theme.palette.neutral['300'],
					},
				},
			},
		},
		'.doc-container': {
			padding: theme.spacing(4),
			maxHeight: theme.spacing(90),
			overflowY: 'auto',
			display: 'grid',
			gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
			'::-webkit-scrollbar': {
				display: 'none',
			},
			'.farmer-image': {
				borderRadius: theme.spacing(1),
				width: '100%',
				maxHeight: 300,
			},
		},
	},
}))
