import { Payload } from '@/components'
import { authAxios, publicAxios, useAuthContext } from '@/contexts'
import {
	ESupportingDocStep,
	IBatchDetails,
	IMarkedDeleteMedia,
} from '@/interfaces'
import {
	BatchStatusTypeEnum,
	MediaActionStatus,
	userRoles,
} from '@/utils/constant'
import {
	QueryFunctionContext,
	useMutation,
	useQuery,
} from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useCallback, useState } from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

interface IDeleteMutationProps {
	apiKey?: string
	payload: {
		uploadIds?: string[]
		deletionStatus?: string
		reason?: string
	}
}

type ActionType =
	| 'start'
	| 'end'
	| 'biochar_addition'
	| 'assess'
	| 'status_change'

interface ActionedBy {
	id: string
	name: string
}

export interface HistoryItem {
	id: string
	type: ActionType
	actioned_by: ActionedBy
	actionedAt: string
	processStatus: BatchStatusTypeEnum
}

export interface getBatchHistoryResponse {
	history: HistoryItem[]
}

// const getBatchDetails = async ({ queryKey }: QueryFunctionContext) => {
// 	const batchId = queryKey[1]
// 	const { data } = await authAxios.get<IBatchDetails>(
// 		`/new/process-details?processId=${batchId}`
// 	)
// 	return data
// }

const getBatchDetails = async ({ queryKey }: QueryFunctionContext) => {
	const batchId = queryKey[1] || queryKey[2]
	const { data } = await publicAxios.get<IBatchDetails>(
		`/public/process-details?processId=${batchId}`
	)
	return data
}

export const useBatchDetails = ({ publicId }: { publicId?: string }) => {
	const { id } = useParams()
	const { userDetails } = useAuthContext()
	const [showNotAssessetModal, setShowNotAssessetModal] = useState(false)

	const batchDetailsQuery = useQuery({
		queryKey: ['batchDetails', id, publicId],
		queryFn: getBatchDetails,
		enabled: !!id || !!publicId,
	})

	const getBatchHistory = useQuery({
		queryKey: ['BatchHistory', id, batchDetailsQuery.data],
		queryFn: async () => {
			const { data } = await authAxios.get<getBatchHistoryResponse>(
				`kiln-process/${id}/history`
			)
			return data.history
		},

		enabled: !!id,
	})

	const MarkedDeletedMediaQuery = useQuery({
		queryKey: [],
		queryFn: async () => {
			const apiUrl = batchDetailsQuery?.data?.kilnProcessDetail?.networkId
				? `/cs-network/${batchDetailsQuery?.data?.kilnProcessDetail?.networkId}/kilns/${batchDetailsQuery?.data?.kilnProcessDetail?.kilnId}/process/${id}/upload/marked-delete`
				: `artisian-pro/${batchDetailsQuery?.data?.kilnProcessDetail?.artisanProId}/site/${batchDetailsQuery?.data?.kilnProcessDetail?.siteId}/kiln/${batchDetailsQuery?.data?.kilnProcessDetail?.kilnId}/process/${id}/upload/marked-delete`
			const { data } = await authAxios.get<IMarkedDeleteMedia>(apiUrl)
			return data
		},
		enabled:
			batchDetailsQuery?.isFetched &&
			!publicId &&
			[userRoles.Admin, userRoles.CsinkManager]?.includes(
				userDetails?.accountType as userRoles
			),
	})

	const deleteMediaMutate = useMutation({
		mutationKey: ['deleteProductionMedia', batchDetailsQuery?.data],
		mutationFn: async ({ apiKey, payload }: IDeleteMutationProps) => {
			const apiUrl = batchDetailsQuery?.data?.kilnProcessDetail?.artisanProId
				? `artisian-pro/${batchDetailsQuery?.data?.kilnProcessDetail?.artisanProId}/site/${batchDetailsQuery?.data?.kilnProcessDetail?.siteId}/kiln/${batchDetailsQuery?.data?.kilnProcessDetail?.kilnId}/process/${id}/upload/${apiKey}`
				: `/cs-network/${
						batchDetailsQuery?.data?.kilnProcessDetail?.networkId ?? ''
				  }/kilns/${
						batchDetailsQuery?.data?.kilnProcessDetail?.kilnId
				  }/process/${id}/upload/${apiKey}`

			const { data } = await authAxios.put(apiUrl, payload)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			batchDetailsQuery.refetch()
			MarkedDeletedMediaQuery.refetch()
		},
	})

	const deleteMixingMediaMutate = useMutation({
		mutationKey: ['deleteMixingMedia', batchDetailsQuery?.data],
		mutationFn: async (payload: { imageIds: string[] }) => {
			const mixingId = batchDetailsQuery?.data?.mixingDetails?.find(
				(mixing) =>
					mixing.images?.some((img) => img.id === payload?.imageIds?.[0]) ||
					mixing.videos?.some((vid) => vid.id === payload?.imageIds?.[0])
			)?.id

			const apiUrl = batchDetailsQuery?.data?.kilnProcessDetail?.artisanProId
				? `/artisian-pro/${batchDetailsQuery?.data?.kilnProcessDetail?.artisanProId}/site/${batchDetailsQuery?.data?.kilnProcessDetail?.siteId}/packaging/${mixingId}/images`
				: `/cs-network/${batchDetailsQuery?.data?.kilnProcessDetail?.networkId}/site/${batchDetailsQuery?.data?.kilnProcessDetail?.kilnId}/packaging-mixing/${mixingId}/images`
			const { data } = await authAxios.delete(apiUrl, { data: payload })
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			batchDetailsQuery.refetch()
		},
	})

	const handleDeleteMedia = useCallback(
		({
			activeId,
			status,
			reason,
			type,
		}: {
			activeId: string
			status: MediaActionStatus
			reason: string
			type?: string
		}) => {
			let apiKey = 'action'
			const payload = { uploadIds: [activeId] }
			if (
				[
					userRoles.BiomassAggregator,
					userRoles.ArtisanPro,
					userRoles.cSinkNetwork,
				].includes(userDetails?.accountType as userRoles)
			) {
				apiKey = 'mark-delete'
				Object.assign(payload, { reason })
			} else {
				Object.assign(payload, { deletionStatus: status })
			}
			if (type === ESupportingDocStep.mixing) {
				deleteMixingMediaMutate.mutate({ imageIds: [activeId] })
				return
			}
			if (
				type === ESupportingDocStep.bioProduction ||
				type === ESupportingDocStep.productionTempMedia
			) {
				deleteMediaMutate.mutate({ apiKey, payload })
			}
		},
		[deleteMediaMutate, deleteMixingMediaMutate, userDetails?.accountType]
	)

	const addMultipleImagesMutate = useMutation({
		mutationKey: ['addMultipleImages'],
		mutationFn: async ({
			batchDetail,
			payload,
		}: {
			batchDetail: any
			payload: Payload
		}) => {
			if (!batchDetail) {
				throw new Error('Batch details are missing.')
			}

			if (
				batchDetail?.kilnProcessDetail?.artisanProId !== null &&
				batchDetail?.kilnProcessDetail?.artisanProId !== undefined
			) {
				// Call for artisianPro
				return await authAxios.post(
					`/artisian-pro/${batchDetail?.kilnProcessDetail?.artisanProId}/site/${batchDetail?.kilnProcessDetail?.siteId}/kiln/${batchDetail?.kilnProcessDetail?.kilnId}/process/${batchDetail?.kilnProcessDetail?.id}/images`,
					payload
				)
			} else if (batchDetail?.kilnProcessDetail?.networkId !== undefined) {
				// Call for cs-network
				return await authAxios.post(
					`/cs-network/${batchDetail?.kilnProcessDetail?.networkId}/kilns/${batchDetail?.kilnProcessDetail?.kilnId}/process/${batchDetail?.kilnProcessDetail?.id}/images`,
					payload
				)
			} else {
				throw new Error('Unable to identify the appropriate endpoint.')
			}
		},
		onSuccess: () => {
			toast.success('Images added successfully.')
			batchDetailsQuery.refetch()
		},
		onError: (error: any) => {
			if (error.response) {
				toast.error(
					`Error ${error.response.status}: ${
						error.response.data?.message || 'Something went wrong.'
					}`
				)
			} else if (error.request) {
				toast.error('No response from server. Please check your network.')
			} else if (error instanceof Error) {
				toast.error(`Unexpected error: ${error.message}`)
			}
		},
	})

	const handleSaveImage = (payload: Payload) => {
		addMultipleImagesMutate.mutate({
			batchDetail: batchDetailsQuery.data,
			payload,
		})
	}

	const addVideoMutate = useMutation({
		mutationKey: ['addVideo'],
		mutationFn: async ({
			batchDetail,
			payload,
		}: {
			batchDetail: any
			payload: Payload
		}) => {
			if (!batchDetail) {
				throw new Error('Batch details are missing.')
			}

			if (
				batchDetail?.kilnProcessDetail?.artisanProId !== null &&
				batchDetail?.kilnProcessDetail?.artisanProId !== undefined
			) {
				// Call for artisianPro
				return await authAxios.post(
					`/artisian-pro/${batchDetail?.kilnProcessDetail?.artisanProId}/site/${batchDetail?.kilnProcessDetail?.siteId}/kiln/${batchDetail?.kilnProcessDetail?.kilnId}/process/${batchDetail?.kilnProcessDetail?.id}/images`,
					payload
				)
			} else if (batchDetail?.kilnProcessDetail?.networkId !== undefined) {
				// Call for cs-network
				return await authAxios.post(
					`/cs-network/${batchDetail?.kilnProcessDetail?.networkId}/kilns/${batchDetail?.kilnProcessDetail?.kilnId}/process/${batchDetail?.kilnProcessDetail?.id}/images`,
					payload
				)
			} else {
				throw new Error('Unable to identify the appropriate endpoint.')
			}
		},
		onSuccess: () => {
			toast.success('Video added successfully.')
			batchDetailsQuery.refetch()
		},
		onError: (error: any) => {
			if (error.response) {
				toast.error(
					`Error ${error.response.status}: ${
						error.response.data?.message || 'Something went wrong.'
					}`
				)
			} else if (error.request) {
				toast.error('No response from server. Please check your network.')
			} else if (error instanceof Error) {
				toast.error(`Unexpected error: ${error.message}`)
			}
		},
	})

	const handleSaveClickVideo = (payload: Payload) => {
		addVideoMutate.mutate({
			batchDetail: batchDetailsQuery.data,
			payload,
		})
	}

	const updateProcessStatusToNotAssessetMutate = useMutation({
		mutationKey: ['updateProcessStatusToNotAssessetMutate'],
		mutationFn: async () => {
			const apiUrl = `/kiln-process/${batchDetailsQuery?.data?.kilnProcessDetail?.id}/status/not-assessed`
			const { data } = await authAxios.put(apiUrl)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			setShowNotAssessetModal(false)
			batchDetailsQuery.refetch()
			toast(data?.message)
		},
	})

	return {
		batchDetails: batchDetailsQuery.data,
		markedMedia: MarkedDeletedMediaQuery.data,
		userDetails,
		handleDeleteMedia,
		batchDetailsQuery,
		handleSaveImage,
		handleSaveClickVideo,
		updateProcessStatusToNotAssessetMutate,
		setShowNotAssessetModal,
		showNotAssessetModal,
		BatchHistory: getBatchHistory.data,
	}
}
