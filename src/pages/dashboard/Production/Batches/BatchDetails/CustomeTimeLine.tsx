import bucket from '@/assets/icons/bucket.svg'
import firepot from '@/assets/icons/firepot.svg'
import hexagon from '@/assets/icons/hexgon.svg'
import leaficon from '@/assets/icons/leaf.svg'
import {
	alpha,
	Box,
	Button,
	IconButton,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import { useCallback, useMemo, useState } from 'react'
import {
	ESupportingDocStep,
	IBatchDetails,
	IMediaWithDeleteParams,
} from '@/interfaces'
import { format } from 'date-fns'
import { proxyImage } from '@/utils/helper'
import { MediaCarousal } from '@/components'
import { defaultIndex, MediaActionStatus } from '@/utils/constant'
import { theme } from '@/lib/theme/theme'
import { PlayCircle } from '@mui/icons-material'
interface IProps {
	data: IBatchDetails
	handleDeleteMedia?: ({
		activeId,
		status,
		reason,
		type
	}: {
		activeId: string
		status: MediaActionStatus
		reason: string
		type?: string
	}) => void
	supportingDocStep: keyof typeof ESupportingDocStep
	setSupportingDocStep: React.Dispatch<
		React.SetStateAction<keyof typeof ESupportingDocStep>
	>
	// media: {
	// 	[key in keyof typeof ESupportingDocStep]: () => IMedia[]
	// }
	batchDetails?: IBatchDetails
}
interface IHandleTabChange {
	isStageComplete: boolean
	stepValue: keyof typeof ESupportingDocStep
}

export const CustomizedTimeline = ({
	data,
	setSupportingDocStep,
	// media,
	handleDeleteMedia,
	batchDetails,
}: IProps) => {
	const handleClick = useCallback(
		({ isStageComplete, stepValue }: IHandleTabChange) => {
			if (isStageComplete) {
				setSupportingDocStep(stepValue)
				// const element = document.getElementById('batchContainer')
				// element?.scrollIntoView({
				// 	behavior: 'smooth',
				// })
			}
		},
		[setSupportingDocStep]
	)

	const media: { [key: string]: IMediaWithDeleteParams[] } = useMemo(() => {
		const collectionImages: IMediaWithDeleteParams[] = []
		const moistureImages: IMediaWithDeleteParams[] = []
		const productionTempMedia: IMediaWithDeleteParams[] = []

		const mixImages: IMediaWithDeleteParams[] = []
		const productionMedia: IMediaWithDeleteParams[] = []
		const distributionImages: IMediaWithDeleteParams[] = []
		// Changes done as per the requirement

		// batchDetails?.kilnProcessBiomass?.forEach((x) => {
		// x.processBiomassRelation?.forEach((i) => {
		// 	moistureImages.push(...(i.biomassAddition.moistureImages ?? []))
		// })

		moistureImages.push(
			...(batchDetails?.kilnProcessBiomass[0]?.processBiomassRelation[0]
				?.biomassAddition.moistureImages ?? [])
		)
		// })
		batchDetails?.kilnProcessTemperature?.forEach((temp) => {
			productionTempMedia.push(
				...(temp.images?.map((i) => ({
					...i,
					type: 'image' as IMediaWithDeleteParams['type'],
				})) ?? [])
			)
		})

		batchDetails?.mixingDetails?.forEach((mix) => {
			// mixImages.push(...(mix.images ?? []))
			mixImages.push(
				...(mix.images?.map((img) => ({
					...img,
					type: 'image' as IMediaWithDeleteParams['type'],
				})) ?? [])
			)
		})

		batchDetails?.mixingDetails?.forEach((mix) => {
			mixImages.push(
				...(mix.videos?.map((vid) => ({
					...vid,
					type: 'video' as IMediaWithDeleteParams['type'],
				})) ?? [])
			)
		})

		batchDetails?.kilnProcessDetail?.stackImages?.forEach((i) => {
			productionMedia.push({
				...i,
				type: 'image' as IMediaWithDeleteParams['type'],
			})
		})

		batchDetails?.kilnProcessDetail?.processImagesAndVideos?.forEach(
			(media) => {
				productionMedia.push({
					...media,
					type: media?.fileType as IMediaWithDeleteParams['type'],
				})
			}
		)

		// batchDetails?.kilnProcessDetail?.processImages?.forEach((img) => {
		// 	productionMedia.push({
		// 		...img,
		// 		type: 'image' as IMediaWithDeleteParams['type'],
		// 	})
		// })
		// batchDetails?.kilnProcessDetail?.processVideos?.forEach((video) => {
		// 	productionMedia.push({
		// 		...video,
		// 		type: 'video' as IMediaWithDeleteParams['type'],
		// 	})
		// })

		batchDetails?.kilnProcessBiomass?.forEach((item) => {
			collectionImages.push(...(item.dropImages ?? []))
		})

		batchDetails?.applications?.forEach((item) => {
			distributionImages.push(
				...(item.images?.map((img) => ({
					...img,
					type: 'image' as IMediaWithDeleteParams['type'],
				})) ?? [])
			)
		})

		batchDetails?.distributedInventory?.forEach((item) => {
			distributionImages.push(...(item.images ?? []))
		})

		return {
			[ESupportingDocStep.feedStockPrepration]: moistureImages,
			[ESupportingDocStep.bioProduction]: productionMedia,
			[ESupportingDocStep.biomassCollection]: collectionImages,
			[ESupportingDocStep.mixing]: mixImages,
			[ESupportingDocStep.application]: distributionImages,
			[ESupportingDocStep.productionTempMedia]: productionTempMedia,
		}
	}, [batchDetails])
	const [showCarousal, setShowCarousal] = useState<string | null>(null)
	const [selectedMedia, setSelectedMedia] = useState<string | null>(null)

	const TimelineData = useMemo(
		() => [
			{
				id: '1',
				icons: leaficon,
				showMedia: false,
				iconStyle: {
					height: 17,
					width: 18,
				},
				text: 'Biomass Collected',
				desc: (
					<Stack rowGap={1}>
						{data?.kilnProcessBiomass?.map((item, index) => (
							<Stack
								key={item.id}
								className={
									index === data?.kilnProcessBiomass?.length - 1
										? ''
										: `border_b`
								}
								pb={1}>
								<Typography className='text-button-grey' variant='overline'>
									{format(item.kilnDropTime, 'dd/MM/yyyy')} (
									{format(item.kilnDropTime, 'p')})
								</Typography>
								{item?.farmer?.name ? (
									<>
										<Typography className='text-button-grey' variant='overline'>
											{item.farmer?.name} ({item.farmer.number})
										</Typography>
										<Typography className='text-button-grey' variant='overline'>
											qty : {item?.totalProcessBiomassQuantity}
										</Typography>
									</>
								) : (
									<>
										<Typography className='text-button-grey' variant='overline'>
											{item?.fpu?.fpuName}
										</Typography>
										<Typography className='text-button-grey' variant='overline'>
											qty:
											{item?.totalProcessBiomassQuantity}
										</Typography>
									</>
								)}
								<Stack className='imagegrid-container'>
									{item.dropImages?.map((image, index) => (
										<IconButton
											key={index}
											sx={{
												padding: 0,
											}}
											onClick={() => {
												setSelectedMedia(image?.id)
												setShowCarousal(ESupportingDocStep.biomassCollection)
											}}>
											<Box
												className='img-btn'
												component='img'
												src={proxyImage(image?.path ?? '', '50:50')}
												alt='image'
											/>
										</IconButton>
									))}
								</Stack>
							</Stack>
						))}
					</Stack>
				),
				buttonText: 'Supporting Doc',
				stepValue: ESupportingDocStep.biomassCollection,
				isStageComplete: !!data?.kilnProcessBiomass?.length,
			},
			...(data?.kilnProcessBiomass?.[0].processBiomassRelation?.[0]
				?.biomassAddition?.moistureArray
				? [
					{
						id: '2',
						icons: leaficon,
						showMedia: true,
						iconStyle: {
							height: 17,
							width: 18,
						},
						text: 'Feedstock Preparation',
						desc: (
							<Stack rowGap={1}>
								{/* As per requirement taking 0th index value may be change in future */}
								{[data?.kilnProcessBiomass?.[0] ?? []]?.map((item, index) => {
									return (
										<Stack
											key={index + item?.id}
											rowGap={1}
										// className={
										// 	index === data?.kilnProcessBiomass?.length - 1
										// 		? ''
										// 		: `border_b`
										// }
										>
											{[item?.processBiomassRelation?.[0] ?? []].map(
												(drop, index) => {
													return index === defaultIndex ? (
														<Stack
															// className={
															// 	index === item?.processBiomassRelation?.length - 1
															// 		? ''
															// 		: `border_b`
															// }
															pb={1}
															key={drop?.processDrop?.id}>
															<Typography
																className='text-button-grey'
																variant='overline'>
																{format(
																	drop?.processDrop?.biomassAddedAt ??
																	new Date(),
																	'P'
																)}{' '}
																(
																{format(
																	drop?.processDrop?.biomassAddedAt ??
																	new Date(),
																	'p'
																)}
																)
															</Typography>
															<Typography
																className='text-button-grey'
																variant='overline'>
																Moisture:{' '}
																{drop?.biomassAddition?.moistureArray?.join(
																	'%, '
																)}
																{drop?.biomassAddition?.moistureArray
																	?.length > 0
																	? ' %'
																	: ''}
															</Typography>
														</Stack>
													) : null
												}
											)}
										</Stack>
									)
								})}
							</Stack>
						),
						buttonText: 'Supporting Doc',
						stepValue: ESupportingDocStep.feedStockPrepration,
						isStageComplete:
							!!data?.kilnProcessBiomass &&
							data?.kilnProcessBiomass?.some(
								(i) => !!i.processBiomassRelation?.length
							),
					},
				]
				: []),
			{
				id: '3',
				showMedia: true,
				icons: firepot,
				iconStyle: {
					height: 24,
					width: 24,
				},
				text: 'Biochar Production',
				desc: (
					<Stack rowGap={1}>
						<Stack>
							<Typography className='text-button-grey' variant='overline'>
								started :{' '}
								{format(
									data?.kilnProcessDetail?.createdAt ?? new Date(),
									'dd/MM/yyyy'
								)}{' '}
								({format(data?.kilnProcessDetail?.createdAt ?? new Date(), 'p')}
								){' '}
							</Typography>
							<Typography className='text-button-grey' variant='overline'>
								Ended :{' '}
								{data?.kilnProcessDetail?.endTime
									? `${format(
										data?.kilnProcessDetail?.endTime,
										'dd/MM/yyyy'
									)} (${format(data?.kilnProcessDetail?.endTime, 'p')})`
									: 'In progress'}
							</Typography>
						</Stack>
						{data?.kilnProcessTemperature?.map((temp, index) => (
							<Stack
								key={temp?.temperature}
								className={
									index === data?.kilnProcessTemperature?.length - 1
										? ''
										: `border_b`
								}>
								<Typography className='text-button-grey' variant='overline'>
									Temperature: {temp.temperature} {temp.temperatureUnit}{' '}
								</Typography>
								<Stack className='imagegrid-container'>
									{temp?.images?.map((image, index) => (
										<IconButton
											key={index}
											sx={{
												padding: 0,
											}}
											onClick={() => {
												setSelectedMedia(image?.id)
												setShowCarousal(ESupportingDocStep.productionTempMedia)
											}}>
											<Box
												className='img-btn'
												component='img'
												src={proxyImage(image?.path ?? '', '50:50')}
												alt='image'
											/>
										</IconButton>
									))}
								</Stack>
							</Stack>
						))}
						<Typography className='text-button-grey' variant='overline'>
							Volume: {data?.kilnProcessDetail?.bioCharQty} litre
							{data?.kilnProcessDetail?.bioCharQty > 1 ? 's' : ''}
						</Typography>
					</Stack>
				),
				buttonText: 'Supporting Doc',
				stepValue: ESupportingDocStep.bioProduction,
				isStageComplete: !!data?.kilnProcessTemperature?.length,
			},
			{
				id: '4',
				icons: bucket,
				showMedia: false,
				iconStyle: {
					height: 24,
					width: 20,
				},
				text: 'Mixing',
				desc: (
					<Stack rowGap={1}>
						{data?.mixingDetails?.map((mix, index) => (
							// <Stack
							// 	key={index}
							// 	className={
							// 		index === data?.mixedPackedInventory?.length - 1
							// 			? ''
							// 			: `border_b`
							// 	}>
							// 	<Typography className='text-button-grey' variant='overline'>
							// 		{format(mix?.packagingCreatedAt, 'dd/MM/yyyy')} (
							// 		{format(mix?.packagingCreatedAt, 'p')})
							// 	</Typography>
							// 	<Typography className='text-button-grey' variant='overline'>
							// 		{mix.packagingName}
							// 	</Typography>
							// 	<Typography className='text-button-grey' variant='overline'>
							// 		{mix.totalActualQty}{' '}
							// 		{mix.packagingType === 'solid' ? 'Kg' : 'liter'} (
							// 		{mix.inventoryList?.length}{' '}
							// 		{mix.inventoryList?.length > 1 ? 'bags' : 'bag'})
							// 	</Typography>
							// </Stack>
							<Stack
								key={index}
								className={
									index === data?.mixingDetails?.length - 1 ? '' : `border_b`
								}>
								<Typography className='text-button-grey' variant='overline'>
									{format(mix?.createdAt, 'dd/MM/yyyy')} (
									{format(mix?.createdAt, 'p')})
								</Typography>
								<Typography className='text-button-grey' variant='overline'>
									{mix.mixType}
								</Typography>
								<Typography className='text-button-grey' variant='overline'>
									{mix.biocharQuantity} {mix.bagCount > 1 ? 'litre' : 'litres'}{' '}
									{mix.bagCount > 0 ? '' : 'Open'}
									{/* {mix.bagCount > 0
										? mix.bagCount > 1
											? ' bags'
											: ' bag'
										: ''}
									) */}
									{mix?.bags?.map((item) => (
										<Stack>
											{item?.name} ({item.inventoryCount}
											{item.inventoryCount > 0
												? item.inventoryCount > 1
													? ' bags'
													: ' bag'
												: ''}
											)
										</Stack>
									))}
								</Typography>
								<Stack className='imagegrid-container'>
									{mix.images?.map((image, index) => (
										<IconButton
											key={index}
											sx={{
												padding: 0,
											}}
											onClick={() => {
												setSelectedMedia(image?.id)
												setShowCarousal(ESupportingDocStep.mixing)
											}}>
											<Box
												component='img'
												src={proxyImage(image?.path ?? '', '50:50')}
												alt='image'
												className='img-btn'
											/>
										</IconButton>
									))}
									{mix.videos?.map((video, index) => (
										<IconButton
											key={index}
											sx={{
												padding: 0,
											}}
											onClick={() => {
												setSelectedMedia(video?.id)
												setShowCarousal(ESupportingDocStep.mixing)
											}}>
											<Box
												component='img'
												src={video?.thumbnailURL ?? ''}
												alt='video'
												className='img-btn'
											/>
										</IconButton>
									))}
								</Stack>
							</Stack>
						))}
					</Stack>
				),
				buttonText: 'Supporting Doc',
				stepValue: ESupportingDocStep.mixing,
				isStageComplete: !!data?.mixingDetails?.length,
			},
			{
				id: '5',
				icons: hexagon,
				showMedia: false,
				iconStyle: {
					height: 24,
					width: 24,
				},
				text: 'Application',
				desc: (
					<Stack rowGap={1}>
						{data?.applications?.map((item, index) => (
							<Stack
								key={item.createdAt}
								className={
									index === data?.distributedInventory?.length - 1
										? ''
										: `border_b`
								}>
								<Typography className='text-button-grey' variant='overline'>
									Qty: {item.biocharQuantity}
								</Typography>
								<Typography className='text-button-grey' variant='overline'>
									{item.fpuName ? item.fpuName : item.farmerName}
								</Typography>
								{item?.isMixingApplication && item?.bagName && (
									<Typography className='text-button-grey' variant='overline'>
										{item.bagName}
										{item.bagCount ? ` (${item.bagCount})` : ''}
									</Typography>
								)}

								<Typography className='text-button-grey' variant='overline'>
									{item.mixTypeName}
								</Typography>
								<Typography className='text-button-grey' variant='overline'>
									{format(item.createdAt, 'dd/MM/yyyy')} (
									{format(item.createdAt, 'p')})
								</Typography>
								<Stack className='imagegrid-container'>
									{item?.images?.map((image, index) => (
										<IconButton
											key={index}
											sx={{
												padding: 0,
											}}
											onClick={() => {
												setSelectedMedia(image?.id)
												setShowCarousal(ESupportingDocStep.application)
											}}>
											<Box
												component='img'
												src={proxyImage(image?.path ?? '', '50:50')}
												alt='image'
												className='img-btn'
											/>
										</IconButton>
									))}
								</Stack>
							</Stack>
						))}

						{data?.distributedInventory?.map((item, index) => (
							<Stack
								key={item.distributedAt}
								className={
									index === data?.distributedInventory?.length - 1
										? ''
										: `border_b`
								}>
								<Typography className='text-button-grey' variant='overline'>
									Qty: {item.totalActualQty}
								</Typography>
								<Typography className='text-button-grey' variant='overline'>
									{item.name} ({item.number})
								</Typography>
								<Typography className='text-button-grey' variant='overline'>
									{item.isOpenDistribution ? item.mixTypeName : ''}
								</Typography>
								<Typography className='text-button-grey' variant='overline'>
									{format(item.distributedAt, 'dd/MM/yyyy')} (
									{format(item.distributedAt, 'p')})
								</Typography>
								<Stack className='imagegrid-container'>
									{item?.images?.map((image, index) => (
										<IconButton
											key={index}
											sx={{
												padding: 0,
											}}
											onClick={() => {
												setSelectedMedia(image?.id)
												setShowCarousal(ESupportingDocStep.application)
											}}>
											<Box
												component='img'
												src={proxyImage(image?.path ?? '', '50:50')}
												alt='image'
												className='img-btn'
											/>
										</IconButton>
									))}
								</Stack>
							</Stack>
						))}
					</Stack>
				),
				buttonText: 'Supporting Doc',
				stepValue: ESupportingDocStep.application,
				isStageComplete:
					!!data?.distributedInventory?.length || !!data?.applications?.length,
			},
		],
		[data]
	)

	return (
		<StyledTimeLine
			direction='row'
			columnGap={1.5}
			rowGap={2}
			// flexWrap='wrap'
			sx={{}}
			width='100%'>
			{TimelineData?.map((item) => (
				<Stack key={item.id} flexDirection='column' minHeight={254} flex={1}>
					<Stack rowGap={2}>
						<Stack
							component={Button}
							href='#batchContainer'
							onClick={(e) => {
								e.preventDefault()
								handleClick({
									isStageComplete: item?.isStageComplete,
									stepValue: item?.stepValue,
								})
							}}
							disableRipple
							direction='row'
							justifyContent='start'
							alignItems='center'
							className={`status_card ${item.isStageComplete ? 'stage_success_bg' : ''
								}`}
							columnGap={1}>
							<Box
								className={`connector-top-image ${item.isStageComplete ? '' : 'icon_filter_grey'
									}`}
								component='img'
								height={item.iconStyle.height}
								width={item.iconStyle.width}
								src={item.icons}
								alt={'leaf-icon'}
							/>
							<Typography
								variant='caption'
								fontWeight={900}
								className={`text-button-grey ${item.isStageComplete ? 'stage_success' : ''
									}`}>
								{item.text}{' '}
							</Typography>
						</Stack>
						<Stack pl={1.5}>{item.desc}</Stack>
					</Stack>
					<Stack
						alignItems='center'
						flexDirection='row'
						flexWrap='wrap'
						paddingLeft={theme.spacing(0.5)}
						maxWidth={200}>
						{media[item.stepValue]?.map(
							(image, index) => {
								return (
									item?.showMedia && (
										<IconButton
											key={index}
											onClick={() => {
												setSelectedMedia(image?.id)
												setShowCarousal(item.stepValue)
											}}>
											<Box
												component='img'
												src={
													image?.type === 'video'
														? image?.thumbnailURL
														: proxyImage(image?.path ?? '', '50:50')
												}
												alt='image'
												className='img-btn'
											/>
											{image?.type === 'video' ? (
												<IconButton
													sx={{ position: 'absolute', top: 10, left: 10 }}>
													<PlayCircle color='primary' />
												</IconButton>
											) : null}
										</IconButton>)
								)
							}
						)}
					</Stack>
				</Stack>
			))}
			{showCarousal ? (
				<MediaCarousal
					open={!!showCarousal}
					handleDelete={handleDeleteMedia}
					typeMedia={showCarousal}
					deleteMedia={
						showCarousal === ESupportingDocStep.bioProduction ||
						showCarousal === ESupportingDocStep.productionTempMedia ||
						showCarousal === ESupportingDocStep.mixing
					}
					onClose={() => {
						setShowCarousal(null)
						setSelectedMedia(null)
					}}
					gallery={media[showCarousal] ?? []}
					activeMedia={selectedMedia ?? ''}
				/>
			) : null}
		</StyledTimeLine>
	)
}

const StyledTimeLine = styled(Stack)(({ theme }) => ({
	overflowX: 'auto',
	'::-webkit-scrollbar': {
		display: 'none',
	},
	'.imagegrid-container': {
		alignItems: 'center',
		flexDirection: 'row',
		flexWrap: 'wrap',
		gap: theme.spacing(2),
		paddingTop: theme.spacing(1),
		maxWidth: theme.spacing(25),
	},

	'.button': {
		...theme.typography.caption,
		'.dummy-box': {
			hight: theme.spacing(2.5),
			width: theme.spacing(2.5),
		},
	},
	'.text-button-grey': {
		color: theme.palette.neutral['300'],
	},

	'.img-btn': {
		height: theme.spacing(6),
		width: theme.spacing(6),
		borderRadius: theme.spacing(0.5),
	},
	'.border_b': {
		borderBottom: '1px solid',
		borderColor: theme.palette.neutral['300'],
		paddingBottom: 6,
	},
	'.status_card': {
		marginTop: theme.spacing(0.5),
		marginLeft: theme.spacing(0.5),
		padding: theme.spacing(1.625, 1.25),
		minWidth: theme.spacing(15),
		// minWidth: theme.spacing(25.25),
		borderRadius: theme.spacing(1.5),
		backgroundColor: theme.palette.neutral[50],
		boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
			theme.palette.common.black,
			0.25
		)}`,
		height: theme.spacing(6.25),
		':hover': {
			backgroundColor: theme.palette.neutral[50],
		},
	},
	'.icon_filter_grey': {
		filter:
			'invert(50%) sepia(0%) saturate(1758%) hue-rotate(244deg) brightness(155%) contrast(43%)',
	},

	'.stage_success': {
		color: theme.palette.success.main,
	},
	'.stage_success_bg': {
		backgroundColor: theme.palette.success.light,
		':hover': {
			backgroundColor: theme.palette.success.light,
		},
	},
}))
