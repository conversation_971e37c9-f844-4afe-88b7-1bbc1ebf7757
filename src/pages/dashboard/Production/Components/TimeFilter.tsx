import { PeriodEnum } from '@/interfaces'
import {
	<PERSON><PERSON>,
	Di<PERSON>r,
	<PERSON>over,
	Stack,
	styled,
	useTheme,
} from '@mui/material'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import moment from 'moment'
import React, { useCallback, useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'

const Buttons = [
	{
		label: 'WTD',
		value: PeriodEnum.wtd,
	},
	{
		label: 'MTD',
		value: PeriodEnum.mtd,
	},
	{
		label: 'YTD',
		value: PeriodEnum.ytd,
	},
	{
		label: 'Custom',
		value: PeriodEnum.custom,
	},
]

export const TimeFilter = () => {
	const theme = useTheme()
	const [searchParams, setSearchParams] = useSearchParams()
	const selectedPeriod = searchParams.get('period') || PeriodEnum.mtd
	const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null)
	const paramsStartDate =
		searchParams.get('startDate') === null
			? null
			: moment(searchParams.get('startDate')).format('DD-MM-YYYY')
	const paramsEndDate =
		searchParams.get('endDate') === null
			? null
			: moment(searchParams.get('endDate')).format('DD-MM-YYYY')

	const open = Boolean(anchorEl)

	const setInitialDateRange = useCallback(
		(period: PeriodEnum) => {
			const currentDate = moment()
			let startDate: string
			let endDate: string

			if (period === PeriodEnum.mtd) {
				startDate = moment(currentDate)
					.subtract(6, 'month')
					.format('DD-MM-YYYY')
				endDate = moment(currentDate).endOf('month').format('DD-MM-YYYY')
			} else if (period === PeriodEnum.ytd) {
				startDate = moment(currentDate).subtract(2, 'year').format('DD-MM-YYYY')
				endDate = moment(currentDate).endOf('year').format('DD-MM-YYYY')
			} else {
				endDate = currentDate.format('DD-MM-YYYY')
				startDate = moment(currentDate).subtract(6, 'days').format('DD-MM-YYYY')
			}

			setSearchParams(
				(prev) => {
					prev.set('startDate', startDate)
					prev.set('endDate', endDate)
					prev.set('period', period)
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const handleSelectButton = useCallback(
		(event: React.MouseEvent<HTMLButtonElement>, value: PeriodEnum) => {
			if (value === PeriodEnum.custom) {
				setAnchorEl(event.currentTarget)
			}
			if (value === selectedPeriod) return

			setInitialDateRange(value)
		},
		[selectedPeriod, setInitialDateRange]
	)

	useEffect(() => {
		if (!searchParams.get('startDate') || !searchParams.get('endDate')) {
			setInitialDateRange(selectedPeriod as PeriodEnum)
		}
	}, [searchParams, selectedPeriod, setInitialDateRange])

	const handleClose = () => {
		setAnchorEl(null)
	}

	const handleChangeParamsValue = useCallback(
		(key: string, value: string) => {
			setSearchParams(
				(prev) => {
					prev.set(key, value)
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	return (
		<StyledStack>
			<Stack className='tab-container'>
				{Buttons.map(({ label, value }) => (
					<Button
						key={value}
						className={`tab-button ${
							selectedPeriod === value ? 'selected-button' : ''
						}`}
						size='small'
						variant='text'
						onClick={(event) => handleSelectButton(event, value)}>
						{label}
					</Button>
				))}
				<Popover
					open={open}
					anchorEl={anchorEl}
					onClose={handleClose}
					anchorOrigin={{
						vertical: 'bottom',
						horizontal: 'left',
					}}>
					<Stack direction='row' columnGap={1} alignItems='center'>
						<LocalizationProvider dateAdapter={AdapterMoment}>
							<DateCalendar
								disableFuture
								value={paramsStartDate ? moment(paramsStartDate) : null}
								onChange={(newValue: any) => {
									handleChangeParamsValue(
										'startDate',
										moment(new Date(newValue)).format('DD-MM-YYYY').toString()
									)
								}}
							/>
						</LocalizationProvider>
						<Divider
							orientation='vertical'
							flexItem
							sx={{ borderColor: theme.palette.neutral[100] }}
						/>
						<LocalizationProvider dateAdapter={AdapterMoment}>
							<DateCalendar
								disableFuture
								value={paramsEndDate ? moment(paramsEndDate) : null}
								onChange={(newValue: any) => {
									handleChangeParamsValue(
										'endDate',
										moment(new Date(newValue)).format('DD-MM-YYYY').toString()
									)
								}}
							/>
						</LocalizationProvider>
					</Stack>
				</Popover>
			</Stack>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	'.tab-container': {
		flexDirection: 'row',
		alignItems: 'center',
		background: theme.palette.neutral['100'],
		padding: theme.spacing(0.25),
		borderRadius: theme.spacing(0.75),
		'.tab-button': {
			background: 'transparent',
			color: theme.palette.common.black,
			textTransform: 'none',
			height: 32,
			width: 88,
			fontWeight: 400,
		},
		'.selected-button': {
			background: theme.palette.common.white,
		},
		'.button-border': {
			height: '60%',
			borderRight: `2px solid ${theme.palette.neutral['100']}`,
			borderRadius: theme.spacing(0.25),
		},
	},
}))
