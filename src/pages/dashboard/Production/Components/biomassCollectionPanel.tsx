import { CustomDataGrid, ImageCarouselDialog } from '@/components'
import { QueryInput } from '@/components/QueryInputs'
import { useAuthContext } from '@/contexts'
import {
	Biomass_Secondary_Unit,
	IBiomassCollectionList,
	IImage,
} from '@/interfaces'
import { fuelTypes, userRoles } from '@/utils/constant'
import { getGoogleMapLink, proxyImage } from '@/utils/helper'
import { EditOutlined, LocationOnOutlined, Search } from '@mui/icons-material'
import { Box, IconButton, Stack, Typography, useTheme } from '@mui/material'
import {
	GridColDef,
	GridRenderCellParams,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { format } from 'date-fns'
import { FC, useMemo, useState } from 'react'

interface IProps {
	biomassCollection?: {
		count: number
		siteBiomassList: IBiomassCollectionList[]
	}
	loadingBiomassCollection: boolean
	openEditBiomassQuantity: (id: string) => void
}

export const BiomassCollectionPanel: FC<IProps> = ({
	biomassCollection,
	loadingBiomassCollection,
	openEditBiomassQuantity,
}) => {
	const theme = useTheme()
	const [imageList, setImageList] = useState<IImage[]>([])
	const [imageIndexShow, setImageIndexShow] = useState<number>(0)
	const { userDetails } = useAuthContext()
	const fuelTypeObject = useMemo(() => {
		return {
			non_motorised: ` / (${fuelTypes.non_motorised})`,
			other: ` / (${fuelTypes.other})`,
			manual: ` / (${fuelTypes.manual})`,
			bullock_cart: ` / (${fuelTypes.bullock_cart})`,
		}
	}, [])

	const biomassCollectionColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'dropTime',
				headerName: 'Delivery Date',
				headerAlign: 'center',
				align: 'center',
				minWidth: 175,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.dropTime
								? format(params?.row?.dropTime, 'dd.MMM.yy HH:mm')
								: '-'}
						</Typography>
					)
				},
			},
			{
				field: 'artisanProName',
				headerName: 'Network/Artisan Pro Name',
				minWidth: 175,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.artisanProName || params.row.networkName}
						</Typography>
					)
				},
			},
			{
				field: 'address',
				headerName: ' Source Location',
				minWidth: 120,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					const lat = params?.row?.farmCoordinate
						? params?.row?.farmCoordinate?.x
						: params?.row?.fpuCoordinate?.x
					const lng = params?.row?.farmCoordinate
						? params?.row?.farmCoordinate?.y
						: params?.row?.fpuCoordinate?.y
					return (
						<Stack flexDirection='row' alignItems='center'>
							<IconButton
								target='_blank'
								href={getGoogleMapLink(`${lat}`, `${lng}`)}>
								<LocationOnOutlined color='primary' />
							</IconButton>
							<Typography
								variant='subtitle1'
								sx={{
									color: theme.palette.neutral['900'],
								}}>
								{params.row?.address}
							</Typography>
						</Stack>
					)
				},
			},
			{
				field: 'site',
				headerName: 'Site/Farmer',
				minWidth: 175,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					const lat = params?.row?.siteCoordinate
						? params?.row?.siteCoordinate?.x
						: params?.row?.kilnCoordinate?.x
					const lng = params?.row?.siteCoordinate
						? params?.row?.siteCoordinate?.y
						: params?.row?.kilnCoordinate?.y
					return (
						<Stack flexDirection='row' alignItems='center'>
							<IconButton
								target='_blank'
								href={getGoogleMapLink(`${lat}`, `${lng}`)}>
								<LocationOnOutlined color='primary' />
							</IconButton>
							<Typography
								variant='subtitle1'
								sx={{
									color: theme.palette.neutral['900'],
								}}>
								{params?.row?.farmerName ??
									params?.row?.fpuName ??
									params?.row?.siteName}
							</Typography>
						</Stack>
					)
				},
			},
			{
				field: 'cropName',
				headerName: 'Biomass Name',
				minWidth: 175,
				flex: 1,
			},

			{
				field: 'biomassQuantity',
				headerName: '',
				minWidth: 180,
				flex: 1,
				renderHeader: () => (
					<Typography
						variant='subtitle2'
						color='primary.dark'
						whiteSpace='break-spaces'
						fontSize={theme.spacing(1.75)}>
						Biomass Qty ({Biomass_Secondary_Unit})
					</Typography>
				),
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								color: theme.palette.neutral['900'],
							}}>
							{params.row.biomassQuantity
								? (params.row.biomassQuantity / 1000).toFixed(2) +
								  ` ${Biomass_Secondary_Unit}`
								: '-'}
						</Typography>
					)
				},
			},

			// {
			// 	field: 'density',
			// 	headerName: '',
			// 	minWidth: 180,
			// 	flex: 1,
			// 	renderHeader: () => (
			// 		<Typography
			// 			variant='subtitle2'
			// 			color='primary.dark'
			// 			whiteSpace='break-spaces'
			// 			fontSize={theme.spacing(1.75)}>
			// 			Biomass Density (tonne/&#13221;)
			// 		</Typography>
			// 	),
			// 	renderCell: (params: GridRenderCellParams) => {
			// 		return (
			// 			<Typography
			// 				variant='subtitle1'
			// 				sx={{
			// 					color: theme.palette.neutral['900'],
			// 				}}>
			// 				{params.row.density ?? '-'}
			// 			</Typography>
			// 		)
			// 	},
			// },
			{
				field: 'vehicleType',
				headerName: 'Transport Info',
				minWidth: 150,
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					return (
						<Typography
							variant='subtitle1'
							sx={{
								textTransform: 'capitalize',
								color: theme.palette.neutral['900'],
							}}>
							{params.row.vehicleType?.replace('_', ' ')}
							{params.row?.vehicleNumber
								? ` / (${params.row.vehicleNumber})`
								: ` ${
										fuelTypeObject[
											params.row?.vehicleFuelType as keyof typeof fuelTypeObject
										] ?? ''
								  }`}
						</Typography>
					)
				},
			},

			{
				field: 'images',
				headerName: 'Images',
				minWidth: 200,
				align: 'center',
				headerAlign: 'center',
				flex: 1,
				renderCell: (params: GridRenderCellParams) => {
					const imageLength = params?.row?.images?.length
					return (
						<Stack direction='row' spacing={0.5} alignItems='center'>
							{params?.row?.images
								?.slice(0, 2)
								?.map((image: IImage, index: number) => (
									<IconButton
										key={index}
										onClick={() => {
											setImageIndexShow(index)
											setImageList(params.row.images)
										}}>
										<Box
											component='img'
											src={proxyImage(image?.fileName ?? '')}
											alt='connection-image'
											sx={{
												height: theme.spacing(4.5),
												width: theme.spacing(4.5),
												borderRadius: theme.spacing(0.5),
											}}
										/>
									</IconButton>
								))}
							{imageLength > 2 && (
								<IconButton
									size='small'
									onClick={() => {
										setImageIndexShow(0)
										setImageList(params.row.images)
									}}
									sx={{
										...theme.typography.body1,
										color: theme.palette.primary.main,
									}}>
									+{imageLength - 2}
								</IconButton>
							)}
						</Stack>
					)
				},
			},
			...(userDetails?.accountType === userRoles.Admin
				? [
						{
							field: 'action',
							headerName: '',
							maxWidth: 100,
							flex: 1,
							renderCell: (params: GridRenderCellParams) => (
								<IconButton
									size='small'
									onClick={() => {
										openEditBiomassQuantity(params.row.id)
									}}>
									<EditOutlined />
								</IconButton>
							),
						},
				  ]
				: []),
		],
		[fuelTypeObject, openEditBiomassQuantity, theme, userDetails?.accountType]
	)

	return (
		<>
			<CustomDataGrid
				showPagination={true}
				rows={biomassCollection?.siteBiomassList || []}
				columns={biomassCollectionColumn}
				rowCount={biomassCollection?.count ?? 0}
				loading={loadingBiomassCollection}
				headerComponent={
					<Stack className='header-filter-search' gap={2}>
						<QueryInput
							className='search-textFiled'
							queryKey='searchName'
							placeholder='Search'
							setPageOnSearch
							InputProps={{
								endAdornment: <Search fontSize='small' />,
							}}
						/>
					</Stack>
				}
			/>
			{imageList?.length ? (
				<ImageCarouselDialog
					open={!!imageList?.length}
					close={() => {
						setImageList([])
					}}
					ImagesList={imageList ?? []}
					imageIndex={imageIndexShow}
					showDownload={false}
				/>
			) : null}
		</>
	)
}
