import { CustomCard, CustomHeader, NoData } from '@/components'
import { Tab<PERSON>ontext, TabList } from '@mui/lab'
import {
	alpha,
	Box,
	capitalize,
	Chip,
	CircularProgress,
	FormControl,
	IconButton,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	styled,
	Tab,
	Typography,
	useTheme,
} from '@mui/material'
import { ApexOptions } from 'apexcharts'
import {
	FC,
	PropsWithChildren,
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react'
import ReactApexChart from 'react-apexcharts'
import { useProduction } from './useProduction'
import { useAuthContext } from '@/contexts'
import { userRoles } from '@/utils/constant'
import { MeasuringUnits, NetworkTabs } from '@/types'
import {
	BioCharDetailWrapper,
	BioCharMixing,
	BioCharProduction,
	TimeFilter,
} from './Components'
import { ArrowBackIosNew, ArrowForwardIos } from '@mui/icons-material'
import { convertKgToTon, roundNumber } from '@/utils/helper'
import { BiomassFilter } from './Biomass/BiomassFilters'
import { PeriodEnum } from '@/interfaces'
import { format, parseISO } from 'date-fns'
import { NewChartCard } from '../Home'

type MonthGroupInfo = {
	monthLabel: string
	leftOffsetPercent: number
	widthPercent: number
	startLabel: string
	endLabel: string
}

type MonthGroupData = {
	categories: string[]
	monthGroupInfo: MonthGroupInfo[]
}

export const Production: FC<PropsWithChildren> = () => {
	const theme = useTheme()
	const { userDetails } = useAuthContext()

	const {
		graphTab,
		setSearchParams,
		paramsNetworkTab,
		handleNetworkTabChange,
		allValues,
		unit,
		handleSelectUnit,
		getQuantityData,
		getGraphData,
		graphData,
		paramsChartType,
		handleChangeChartType,
		handlePrevGraphDate,
		handleNextGraphDate,
		isNextGraphButtonDisabled,
		searchParams,
		paramsPeriod,
		paramsStartDate,
		paramsEndDate,
	} = useProduction()

	const handleGraphTabChange = useCallback(
		(_: unknown, newValue: string) => {
			setSearchParams((searchParams) => {
				searchParams.set('graphTab', newValue)
				return searchParams
			})
		},
		[setSearchParams]
	)

	const creditsData = useMemo(
		() => ({
			registered: getQuantityData?.data?.stockCarbonCredits ?? 0,
			realisedPotential:
				(getQuantityData?.data?.pendingApplicationCarbonCredits || 0) +
				(getQuantityData?.data?.pendingRegistrationCarbonCredits || 0),
			potential: parseFloat(
				(
					((getQuantityData?.data?.pendingApplicationCarbonCredits || 0) +
						(getQuantityData?.data?.pendingRegistrationCarbonCredits || 0) +
						(getQuantityData?.data?.stockCarbonCredits ?? 0)) /
					1000
				).toFixed(4)
			),
			rejected: getQuantityData?.data?.rejectedCarbonCredits || 0,
			compensated: getQuantityData?.data?.compensatedCarbonCredits || 0,
		}),
		[
			getQuantityData?.data?.stockCarbonCredits,
			getQuantityData?.data?.pendingApplicationCarbonCredits,
			getQuantityData?.data?.pendingRegistrationCarbonCredits,
			getQuantityData?.data?.rejectedCarbonCredits,
			getQuantityData?.data?.compensatedCarbonCredits,
		]
	)

	const ActivityGraphTabArray = useMemo(
		() => [
			{
				label: 'Biomass',
				value: 'biomass',
				incrementCount: roundNumber(
					getGraphData?.data?.totalBiomass?.reduce(
						(acc, curr) => acc + curr?.total,
						0
					),
					4
				),
			},
			{
				label: 'Biochar',
				value: 'bioChar',
				incrementCount: roundNumber(
					getGraphData?.data?.processDetail?.reduce((acc, curr) => {
						return (
							acc +
							(unit === MeasuringUnits.ton
								? curr?.totalBiocharInTonnes
								: curr?.total)
						)
					}, 0),
					4
				),
			},
			{
				label: 'Credits',
				value: 'credits',
				incrementCount: roundNumber(
					getGraphData?.data?.processDetail?.reduce(
						(acc, curr) => acc + curr?.totalCarbonCredits,
						0
					),
					4
				),
			},
		],
		[getGraphData?.data?.processDetail, getGraphData?.data?.totalBiomass, unit]
	)

	const selectedTabData = ActivityGraphTabArray.find(
		(item) => item.value === graphTab
	)

	const noDataLabel = selectedTabData
		? `No ${selectedTabData.label} for the selected Dates`
		: 'No data for the selected Dates'

	const options = useMemo(
		() => ({
			series: [
				{
					data:
						graphTab === 'biomass'
							? graphData?.[graphTab]?.data?.map((value) =>
									convertKgToTon(value, 3)
							  )
							: graphTab === 'bioChar' && unit === MeasuringUnits.m3
							? graphData?.[graphTab]?.data?.map((value) =>
									convertKgToTon(value, 3)
							  )
							: graphData?.[graphTab]?.data?.map((value) =>
									roundNumber(value, 4)
							  ),
					color: theme.palette.primary.light,
				},
			],
		}),
		[graphData, graphTab, theme.palette.primary.light, unit]
	)

	const opt: ApexOptions = useMemo(() => {
		const currentPeriod =
			(searchParams.get('period') as PeriodEnum) || PeriodEnum.mtd
		const isCustomPeriod = currentPeriod === PeriodEnum.custom
		const originalLabels: string[] = graphData?.[graphTab]?.label || []

		const parsedDates = originalLabels
			.map((dateStr, index) => {
				try {
					const date = parseISO(dateStr)
					if (isNaN(date.getTime())) return null
					return { index, date, dateStr }
				} catch {
					return null
				}
			})
			.filter(Boolean) as { index: number; date: Date; dateStr: string }[]

		const categories = isCustomPeriod
			? parsedDates.map(({ date }) => format(date, 'dd'))
			: originalLabels

		const monthGroups = new Map<string, { start: string; end: string }>()
		parsedDates.forEach(({ date }) => {
			const key = format(date, "MMM''yy")
			const fullLabel = format(date, 'dd-MMM')
			if (!monthGroups.has(key)) {
				monthGroups.set(key, { start: fullLabel, end: fullLabel })
			} else {
				monthGroups.get(key)!.end = fullLabel
			}
		})

		const annotations: NonNullable<ApexAnnotations>['xaxis'] = Array.from(
			monthGroups.entries()
		).map(([monthLabel, { start, end }]) => {
			return {
				y: 0,
				y2: 40,
				borderWidth: 5,
				x: start,
				x2: end,
				borderColor: '#999',
				fillColor: '#f0f0f0',
				strokeDashArray: 0,
				label: {
					text: monthLabel,
					style: {
						color: '#555',
						fontSize: '12px',
						background: 'transparent',
					},
					position: 'top',
					textAnchor: 'middle',
					orientation: 'horizontal',
					offsetY: 20,
					offsetX: 50,
				},
			}
		})

		const options: ApexOptions = {
			chart: {
				type: paramsChartType as 'bar' | 'line',
				height: 1000,
				stacked: true,
				toolbar: { show: false },
				zoom: { enabled: true },
			},
			responsive: [
				{
					breakpoint: 480,
					options: {
						legend: {
							position: 'bottom',
							offsetX: -10,
							offsetY: 0,
						},
					},
				},
			],

			xaxis: {
				type: 'category',
				categories,
				axisBorder: { show: true },
				axisTicks: { show: false },
				tickPlacement: 'on',
				labels: {
					show: true,
					style: { fontSize: '10px' },
				},
			},
			annotations: {
				xaxis: annotations,
			},
			legend: {
				position: 'bottom',
				horizontalAlign: 'left',
				offsetY: 10,
				markers: { radius: 12 },
			},
			fill: { opacity: 1 },
			grid: {
				show: false,
				padding: { bottom: 15 },
			},
			dataLabels: { enabled: false },
			tooltip: {
				y: {
					formatter: function (value: number) {
						return `${capitalize(graphTab)}: ${value}`
					},
					title: {
						formatter: () => '',
					},
				},
			},
		}

		return options
	}, [graphData, graphTab, paramsChartType, searchParams])

	const chartData = useMemo(
		() => ({
			series: [
				Number((creditsData.registered / 1000)?.toFixed(4)),
				Number((creditsData.realisedPotential / 1000)?.toFixed(4)),
				Number((creditsData.rejected / 1000)?.toFixed(4)),
				Number((creditsData.compensated / 1000)?.toFixed(4)),
			],
			options: {
				legend: { show: false },
				dataLabels: { enabled: false },
				tooltip: {
					enabled: true,
					fillSeriesColor: false,
					marker: { show: false },
					y: {
						formatter: function (val: number) {
							return val + ' tCO₂'
						},
						title: {
							formatter: () => '',
						},
					},
				},
				responsive: [
					{
						breakpoint: theme.breakpoints.values.sm,
						options: {
							chart: {
								width: 140,
							},
						},
					},
					{
						breakpoint: theme.breakpoints.values.lg,
						options: {
							chart: {
								width: 190,
							},
						},
					},
				],
				fill: {
					colors: [
						theme.palette.custom.green[700],
						theme.palette.custom.yellow[500],
						theme.palette.custom.red[500],
						theme.palette.custom.yellow[200],
					],
				},
				states: {
					hover: { filter: { type: 'darken', value: 0.5 } },
					active: { filter: { type: 'none', value: 0 } },
				},
				stroke: { width: 0 },
			},
		}),
		[
			creditsData.realisedPotential,
			creditsData.registered,
			creditsData.rejected,
			creditsData.compensated,
			theme.breakpoints.values.lg,
			theme.breakpoints.values.sm,
			theme.palette.custom.green,
			theme.palette.custom.red,
			theme.palette.custom.yellow,
		]
	)

	const formatIncrementCount = useCallback(
		(label: string, incrementCount: number) => {
			switch (label) {
				case 'Biomass':
					return `${convertKgToTon(incrementCount, 3)} ton`
				case 'Biochar':
					return `${
						unit === MeasuringUnits.ton
							? incrementCount
							: unit === MeasuringUnits.ltr
							? incrementCount
							: convertKgToTon(incrementCount, 3)
					} ${unit}`
				default:
					return incrementCount
			}
		},
		[unit]
	)

	const CustomCardHeader = () => {
		return (
			<Stack className='activity-graph'>
				<Box
					display='flex'
					alignItems='center'
					justifyContent={'space-between'}>
					<TabContext value={graphTab}>
						<TabList
							onChange={handleGraphTabChange}
							TabIndicatorProps={{
								sx: {
									top: 0,
								},
							}}>
							{ActivityGraphTabArray.map(
								({ label, value, incrementCount }, index: number) => (
									<Tab
										key={index}
										label={
											<Stack
												sx={{
													position: 'relative',
													padding:
														graphTab === value
															? theme.spacing(1.7, 4, 0, 0)
															: theme.spacing(0),
												}}>
												<Typography
													textTransform='none'
													variant={graphTab === value ? 'body2' : 'overline'}>
													{label}
												</Typography>
												<Typography
													variant={graphTab === value ? 'h3' : 'body1'}>
													{formatIncrementCount(label, incrementCount)}
												</Typography>
											</Stack>
										}
										value={value}
									/>
								)
							)}
						</TabList>
					</TabContext>
					<Box>
						<IconButton onClick={handlePrevGraphDate}>
							<ArrowBackIosNew color='primary' fontSize='small' />
						</IconButton>
						<IconButton
							disabled={isNextGraphButtonDisabled}
							onClick={handleNextGraphDate}>
							<ArrowForwardIos
								color={`${isNextGraphButtonDisabled ? 'disabled' : 'primary'}`}
								fontSize='small'
							/>
						</IconButton>
					</Box>
				</Box>

				<ChartSection
					isLoading={getGraphData.isFetching}
					isDataAvailable={
						!!(selectedTabData && selectedTabData.incrementCount > 0)
					}
					graphTab={graphTab}
					noDataLabel={noDataLabel}
					opt={opt as ApexOptions}
					series={options.series}
					chartType={paramsChartType === 'line' ? 'line' : 'bar'}
				/>
			</Stack>
		)
	}

	type Props = {
		isLoading: boolean
		isDataAvailable: boolean
		graphTab: string
		noDataLabel: string
		opt: ApexOptions
		series: ApexAxisChartSeries
		chartType: 'bar' | 'line'
	}

	const ChartSection: React.FC<Props> = ({
		isLoading,
		isDataAvailable,
		graphTab,
		noDataLabel,
		opt,
		series,
		chartType,
	}) => {
		const chartRef = useRef<HTMLDivElement>(null)
		const [chartBoxStyle, setChartBoxStyle] = useState<{
			top: number
			left: number
			width: number
		} | null>(null)

		useEffect(() => {
			if (!chartRef.current) return

			const updateChartBoxStyle = () =>
				setTimeout(() => {
					const chart = chartRef.current
					const inner = chart?.querySelector('.apexcharts-inner') as HTMLElement
					const xAxis = chart?.querySelector('.apexcharts-xaxis') as HTMLElement

					if (chart && inner && xAxis) {
						const chartBox = chart.getBoundingClientRect()
						const innerBox = inner.getBoundingClientRect()
						const xAxisBox = xAxis.getBoundingClientRect()

						setChartBoxStyle({
							top: innerBox.top - chartBox.top + innerBox.height + 5,
							left: xAxisBox.left - chartBox.left,
							width: xAxisBox.width,
						})
					}
				}, 500)

			updateChartBoxStyle()

			window.addEventListener('resize', updateChartBoxStyle)

			return () => {
				window.removeEventListener('resize', updateChartBoxStyle)
			}
		}, [chartRef, graphData, paramsPeriod, paramsStartDate, paramsEndDate])

		const [monthGroupData, setMonthGroupData] = useState<MonthGroupData>()

		const generateMonthGroupData = useCallback(
			(graphData: any, graphTab: string): MonthGroupData => {
				const isCustomPeriod = paramsPeriod === PeriodEnum.custom
				const originalLabels: string[] = graphData?.[graphTab]?.label || []

				const parsedDates = originalLabels
					.map((dateStr, index) => {
						try {
							const date = parseISO(dateStr)
							if (isNaN(date.getTime())) return null
							return { index, date, dateStr }
						} catch {
							return null
						}
					})
					.filter(Boolean) as { index: number; date: Date; dateStr: string }[]

				const categories = isCustomPeriod
					? parsedDates.map(({ date }) => format(date, 'dd-MMM'))
					: originalLabels

				const monthGroups = new Map<
					string,
					{ startIndex: number; endIndex: number }
				>()
				parsedDates.forEach(({ index, date }) => {
					const key = format(date, "MMM''yy")
					if (!monthGroups.has(key)) {
						monthGroups.set(key, { startIndex: index, endIndex: index })
					} else {
						monthGroups.get(key)!.endIndex = index
					}
				})

				const totalCategories = categories.length

				const monthGroupInfo: MonthGroupInfo[] = Array.from(
					monthGroups.entries()
				).map(([monthLabel, { startIndex, endIndex }]) => {
					const startLabel = categories[startIndex]
					const endLabel = categories[endIndex]
					const leftOffsetPercent = (startIndex / totalCategories) * 100
					const widthPercent =
						((endIndex - startIndex + 1) / totalCategories) * 100

					return {
						monthLabel,
						leftOffsetPercent,
						widthPercent,
						startLabel,
						endLabel,
					}
				})

				return {
					categories,
					monthGroupInfo,
				}
			},
			[]
		)

		useEffect(() => {
			if (!chartRef.current) return

			const observer = new ResizeObserver(() => {
				const updated = generateMonthGroupData(graphData, graphTab)
				setMonthGroupData(updated)
			})

			observer.observe(chartRef.current)

			return () => observer.disconnect()
		}, [generateMonthGroupData, graphTab])

		if (isLoading) {
			return (
				<Box
					height='85%'
					display='flex'
					alignItems='center'
					justifyContent='center'>
					<CircularProgress />
				</Box>
			)
		}

		if (isDataAvailable) {
			return (
				<Box height='85%' sx={{ position: 'relative' }} ref={chartRef}>
					<ReactApexChart
						options={opt}
						series={series}
						type={chartType}
						height='100%'
					/>

					{chartBoxStyle && paramsPeriod === PeriodEnum.custom && (
						<StyledMonthOverlay
							sx={{
								top: `${chartBoxStyle.top}px`,
								left: `${chartBoxStyle.left}px`,
								width: `${chartBoxStyle.width}px`,
							}}>
							{monthGroupData?.monthGroupInfo.map((group, idx) => {
								const gapPercent = 0.5
								const adjustedWidth = group.widthPercent - gapPercent
								const adjustedLeft = group.leftOffsetPercent + gapPercent / 2

								return (
									<Box
										key={idx}
										className='month-bar'
										sx={{
											left: `${adjustedLeft}%`,
											width: `${adjustedWidth}%`,
										}}>
										<Typography variant='caption' className='month-label'>
											{group.monthLabel}
										</Typography>
									</Box>
								)
							})}
						</StyledMonthOverlay>
					)}
				</Box>
			)
		}

		return (
			<NoData
				isTitle={false}
				imageUrl='/images/homePageDefaultImage.svg'
				title={`${capitalize(graphTab)} Details`}
				noDataLabel={noDataLabel}
			/>
		)
	}

	const StyledMonthOverlay = styled(Box)(({ theme }) => ({
		position: 'absolute',
		height: '10px',
		pointerEvents: 'none',

		'.month-bar': {
			position: 'absolute',
			height: '2px',
			backgroundColor: theme.palette.custom.grey[200],
			border: '1px solid #999',
			zIndex: 10,
		},

		'.month-label': {
			position: 'absolute',
			top: theme.spacing(1),
			left: '50%',
			transform: 'translateX(-50%)',
			fontSize: '12px',
			color: theme.palette.custom.grey[800],
			pointerEvents: 'none',
		},
	}))

	const tabs = [
		{
			label: 'All',
			value: NetworkTabs.all,
			hide: false,
		},
		{
			label: 'C Sink Network',
			value: NetworkTabs.network,
			hide: [userRoles.ArtisanPro, userRoles.artisanProNetworkManager].includes(
				userDetails?.accountType as userRoles
			),
		},
		{
			label: 'Artisan Pro',
			value: NetworkTabs.artisanPro,
			hide: userDetails?.accountType === userRoles.cSinkNetwork,
		},
	]

	const measuringUnitOptions = useMemo(
		() => [
			{
				label: 'm³',
				value: MeasuringUnits.m3,
			},
			{
				label: 'Ltr',
				value: MeasuringUnits.ltr,
			},
			{
				label: 'ton',
				value: MeasuringUnits.ton,
			},
		],
		[]
	)

	const selectedUnit = useMemo(() => {
		if (unit === MeasuringUnits.m3) {
			return 'm³'
		} else if (unit === MeasuringUnits.ltr) {
			return 'ltr'
		} else {
			return 'ton'
		}
	}, [unit])

	const bioCharDetailList = useMemo(
		() => [
			{
				title: 'Biochar Production',
				subTitle: 'Total Biochar Produced',
				totalBioChar: roundNumber(allValues.bioCharProduced?.[unit].total, 4),
				children: (
					<BioCharProduction
						unit={selectedUnit}
						data={{
							approved: Number(
								allValues.bioCharProduced[unit].approved?.toFixed(4)
							),
							rejected: Number(
								allValues.bioCharProduced[unit].rejected?.toFixed(4)
							),
							pending: Number(
								allValues.bioCharProduced[unit].pending?.toFixed(4)
							),
						}}
					/>
				),
				headerEndComponent: (
					<FormControl sx={{ width: 80 }}>
						<InputLabel>Unit</InputLabel>
						<Select
							label='Unit'
							value={unit}
							onChange={(e) => handleSelectUnit(e.target.value)}>
							{measuringUnitOptions.map((option) => (
								<MenuItem key={option.value} value={option.value}>
									{option.label}
								</MenuItem>
							))}
						</Select>
					</FormControl>
				),
			},
			{
				title: 'Biochar Mixing',
				subTitle: 'Total Approved Biochar',
				totalBioChar: roundNumber(
					allValues.bioCharProduced?.[unit]?.approved,
					4
				),
				children: (
					<BioCharMixing
						unit={selectedUnit}
						data={{
							mixed: {
								value: roundNumber(
									allValues.mixedBioChar?.[unit]?.mixed?.total,
									4
								),
								open: {
									open: roundNumber(
										allValues.mixedBioChar?.[unit]?.mixed?.open,
										4
									),
									distribution: roundNumber(
										allValues.mixedBioChar?.[unit]?.mixed?.openDistributed,
										4
									),
								},
								packed: {
									distribution: roundNumber(
										allValues.mixedBioChar?.[unit]?.mixed?.packedDistributed,
										4
									),
									packed: roundNumber(
										allValues.mixedBioChar?.[unit]?.mixed?.packed,
										4
									),
								},
							},
							unmixed: {
								value: roundNumber(
									allValues.mixedBioChar?.[unit]?.unMixed?.total,
									4
								),
								open: {
									open: roundNumber(
										allValues.mixedBioChar?.[unit]?.unMixed?.open,
										4
									),
									distribution: null,
								},
								packed: {
									distribution: roundNumber(
										allValues.mixedBioChar?.[unit]?.unMixed?.packedDistributed,
										4
									),
									packed: roundNumber(
										allValues.mixedBioChar?.[unit]?.unMixed?.packed,
										4
									),
								},
							},
						}}
					/>
				),
				headerEndComponent: null,
			},
		],
		[
			allValues.bioCharProduced,
			allValues.mixedBioChar,
			handleSelectUnit,
			measuringUnitOptions,
			selectedUnit,
			unit,
		]
	)

	const chartTabs = [
		{ label: 'Bar Chart', value: 'bar' },
		{ label: 'Line Chart', value: 'line' },
	]

	const ChartLabelDataCredit = useMemo(() => {
		return {
			id: 'credit',
			label: 'Total Credits',
			unit: 'tCO₂',
			allData: [
				{
					labelName: 'Registered',
					labelQuantity: parseFloat(
						(creditsData.registered / 1000)?.toFixed(4)
					),
					color: theme.palette.success.main,
				},
				{
					labelName: 'Pending',
					labelQuantity: parseFloat(
						(creditsData.realisedPotential / 1000)?.toFixed(4)
					),
					color: theme.palette.warning.main,
				},
				{
					labelName: 'Lost',
					labelQuantity: parseFloat((creditsData.rejected / 1000)?.toFixed(4)),
					color: theme.palette.custom.red[500],
				},
				{
					labelName: 'Compensated',
					labelQuantity: parseFloat(
						(creditsData.compensated / 1000)?.toFixed(4)
					),
					color: theme.palette.custom.yellow[200],
				},
			],
		}
	}, [
		creditsData.compensated,
		creditsData.realisedPotential,
		creditsData.registered,
		creditsData.rejected,
		theme.palette.custom.red,
		theme.palette.custom.yellow,
		theme.palette.success.main,
		theme.palette.warning.main,
	])

	return (
		<StyledContainer>
			<Box className='header'>
				<CustomHeader showBottomBorder={true} heading='Production' />
				<Stack
					direction='row'
					justifyContent={'space-between'}
					alignItems='center'>
					<Stack className='filter-Chips'>
						{tabs.map((tab) =>
							!tab.hide ? (
								<Chip
									key={tab.value}
									variant='filled'
									onClick={() => handleNetworkTabChange(tab.value)}
									color={paramsNetworkTab === tab.value ? 'primary' : 'default'}
									label={tab.label}
								/>
							) : null
						)}
						<BiomassFilter isProductionPage={true} />
					</Stack>

					<TimeFilter />
				</Stack>
			</Box>

			<Stack>
				<Stack className='upper-container'>
					<Stack width='50%' rowGap={4.5}>
						{bioCharDetailList.map((item) => (
							<BioCharDetailWrapper
								key={item.title}
								{...item}
								unit={selectedUnit}
								headerEndComponent={item.headerEndComponent}>
								<Stack>{item.children}</Stack>
							</BioCharDetailWrapper>
						))}
					</Stack>
					<Stack width='50%' rowGap={2}>
						<Stack
							direction='row'
							justifyContent='flex-end'
							alignItems='center'
							columnGap={1}>
							{chartTabs.map((chart) => (
								<Chip
									key={chart.value}
									variant='filled'
									onClick={() => handleChangeChartType(chart.value)}
									color={
										paramsChartType === chart.value ? 'primary' : 'default'
									}
									label={chart.label}
								/>
							))}
						</Stack>
						<CustomCard
							headerComponent={<CustomCardHeader />}
							sx={{ height: '100%' }}
						/>
					</Stack>
				</Stack>

				<Stack className='lower-container'>
					<Stack className='chart-container'>
						<NewChartCard
							key={ChartLabelDataCredit?.id}
							data={ChartLabelDataCredit}
							chartOptions={chartData.options as ApexOptions}
							chartSeries={chartData.series}
						/>
					</Stack>
				</Stack>
			</Stack>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	'.grey-btn': {
		color: theme.palette.neutral['500'],
		borderColor: theme.palette.neutral['500'],
		borderRadius: theme.spacing(1),
		textTransform: 'none',
		...theme.typography.subtitle2,
	},
	'.header': {
		padding: theme.spacing(2, 0),
		'.filter-Chips': {
			padding: theme.spacing(1, 2, 0, 2),
			flexDirection: 'row',
			gap: theme.spacing(3),
		},
	},

	'.upper-container': {
		flexDirection: 'row',
		gap: theme.spacing(3.75),
		padding: theme.spacing(3, 3, 0),
		'.activity-graph': {
			margin: theme.spacing(-2),
			padding: theme.spacing(0, 2),
			height: '100%',
			display: 'flex',
			justifyContent: 'space-between',
		},
	},

	'.lower-container': {
		height: '50vh',
		padding: theme.spacing(3, 3, 0),
		'.border-card': {
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.25)} 0 ${alpha(
				theme.palette.common.black,
				0.25
			)}`,
		},
		'.chart-container': {
			flexDirection: 'row',
			width: '40%',
			gap: theme.spacing(2),
			flexWrap: 'wrap',
			'.card': {
				padding: theme.spacing(3),
				width: '100%',
				'.card-container': {
					height: '100%',
					alignItems: 'center',
					width: '100%',
					'.chart': {
						position: 'relative',
						width: '100%',
						height: '100%',
						justifyContent: 'center',
						marginLeft: theme.spacing(-3),
						'.chart-data': {
							position: 'absolute',
							height: 48,
							width: 48,
						},
					},
				},
			},
		},
	},
	'.container': {
		gap: theme.spacing(2),
	},

	'.circle-icon': {
		height: 12,
		width: 12,
	},
	'.auto-complete': {
		width: 200,
		'.MuiOutlinedInput-root': {
			height: 38,
			'.MuiInputBase-input': {
				paddingTop: '2.5px',
				fontSize: theme.typography.subtitle1,
			},
		},
	},
}))
