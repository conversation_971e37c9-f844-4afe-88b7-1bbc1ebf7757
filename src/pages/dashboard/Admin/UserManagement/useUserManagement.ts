import { authAxios, useAuthContext } from '@/contexts'
import { IEntitytabsCount, IUsersResponse, IUserTabsCount } from '@/interfaces'
import { defaultLimit, defaultPage, userRoles } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'

export enum tabEnum {
	all = 'all',
	network_admins = 'network_admins',
	operators = 'operators',
	ba_admins = 'ba_admins',
	farmers = 'farmers',
	admin = 'admin',
	circonomy_employee = 'circonomy_employee',
	csink_managers = 'c_sink_manager',
	compliance_manager='compliance_manager'
}

const userType = {
	[tabEnum.all]: '',
	[tabEnum.admin]: 'admin',
	[tabEnum.circonomy_employee]: 'circonomyEmployee',
	[tabEnum.ba_admins]: 'biomassAggregator',
	[tabEnum.farmers]: 'farmer',
	[tabEnum.network_admins]: 'networkAdmin',
	[tabEnum.operators]: 'operator',
	[tabEnum.csink_managers]: 'cSinkManager',
	[tabEnum.compliance_manager]:'complianceManager'
}

export const useUserManagement = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const { userDetails } = useAuthContext()
	const search = searchParams.get('search') || ''
	const paramsTab = searchParams.get('tab') || tabEnum.all
	const paramsLimit = searchParams.get('limit') || defaultLimit.toString()
	const paramsPage = searchParams.get('page') || defaultPage.toString()
	const paramsBAId = searchParams.get('baId') || ''
	const paramsNetworkId = searchParams.get('networkId') || ''
	const paramsDataViewType = searchParams.get('DataViewType') || 'list'

	const AllBaQuery = useQuery({
		queryKey: ['allBA'],
		queryFn: () => {
			return authAxios.get<{
				baDetails: { id: string; name: string; shortCode: string }[]
			}>(`/drop-down/biomass-aggregators`)
		},
		select: ({ data }) =>
			data?.baDetails?.map((item) => ({
				label: `${item.name} (${item.shortCode})`,
				value: item.id,
			})),
		enabled: [userRoles.Admin, userRoles.CsinkManager].includes(
			userDetails?.accountType as userRoles
		),
	})

	const AllNetworkQuery = useQuery({
		queryKey: ['allNetwork', paramsBAId, userDetails?.biomassAggregatorId],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				biomassAggregatorId:
					paramsBAId || (userDetails?.biomassAggregatorId ?? ''),
				limit: '1000',
				page: '0',
			})
			return authAxios.get<{
				networks: {
					id: string
					name: string
					shortName: string
				}[]
			}>(`/new/biomass-aggregator/networks?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.networks?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled:
			!!paramsBAId ||
			(userDetails?.accountType === userRoles.BiomassAggregator &&
				!!userDetails?.biomassAggregatorId),
	})

	const fetchUsers = useQuery({
		queryKey: [
			'users',
			search,
			paramsPage,
			paramsLimit,
			paramsTab,
			paramsNetworkId,
			paramsBAId,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				biomassAggregatorId: paramsBAId,
				networkId: paramsNetworkId,
				limit: paramsLimit,
				page: paramsPage,
				search,
				userType: userType[paramsTab as tabEnum],
			})
			const apiUrl = paramsTab === tabEnum.admin ? `/new/admins` : '/new/users'
			return authAxios.get<IUsersResponse>(
				`${apiUrl}?${queryParams.toString()}`
			)
		},
		enabled: true,
	})
	const handleChangeDataViewType = useCallback(
		(view: string): void => {
			setSearchParams(
				(prev) => {
					if (view === 'grid') {
						prev.set('DataViewType', view)
					} else {
						prev.delete('DataViewType')
					}
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const entityTabsCountQuery = useQuery({
		queryKey: ['entityTabsCountQuery'],
		queryFn: async () => {
			const { data } = await authAxios.get<IEntitytabsCount>(
				`/get-entities-count`
			)
			return data
		},
	})
	const usersCountQuery = useQuery({
		queryKey: ['usersCountQuery'],
		queryFn: async () => {
			const { data } = await authAxios.get<IUserTabsCount>(`/new/users/count`)
			return data
		},
	})

	return {
		setSearchParams,
		paramsTab,
		userList: fetchUsers?.data?.data?.users ?? [],
		totalUserCount: fetchUsers?.data?.data?.count ?? 0,
		baList: AllBaQuery?.data ?? [],
		networkList: AllNetworkQuery?.data ?? [],
		searchParams,
		isLoading: fetchUsers?.isLoading || fetchUsers?.isFetching,
		paramsDataViewType,
		handleChangeDataViewType,
		entityTabsCount: entityTabsCountQuery?.data,
		userTabCounnt: usersCountQuery?.data,
	}
}
