import {
	ActionInformationDrawer,
	AddEntity,
	AdminDetails,
	CustomDataGrid,
	CustomHeader,
	QueryInput,
} from '@/components'
import { Add, AddRounded, Search } from '@mui/icons-material'
import {
	Avatar,
	Box,
	Button,
	IconButton,
	Stack,
	styled,
	Tab,
	Tooltip,
	Typography,
} from '@mui/material'
import { useEntityManagement } from './useEntityManagement'
import { TabContext, TabList, TabPanel } from '@mui/lab'
import { useCallback, useMemo, useState } from 'react'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { ILocation } from '@/interfaces'
import { GoogleMapsDraw } from '@/components/GoogleMap'
import { AddKmlButton } from '@/components/AddKmlButton/AddKmlButton'
import { AssignBiomassToCsManager } from '@/components/AssignBiomassToCsManager/AssignBiomassToCsManager'
import {
	CropforCsinkManager,
	EntityEnum,
	IManagerApplicationType,
	IManagerMixingType,
	ManagerDetails,
} from '@/interfaces/Entity'
import { AddBiomassReference } from '@/components/AddBiomassReference'
import { AssignedBiomassList } from '@/components/AssignBiomassToCsManager'
import { CustomTable } from '@/components/CustomTable'
import { BiomassReferenceColumn } from './BiomassReferenceColumn'
import { ArtisanProList } from '@/components/ArtisanProsList'
import { theme } from '@/lib/theme/theme'
import { MultipleAvatarWrapper } from '@/components/MultipleAvatarWrapper'
import { Confirmation } from '@/components/Confirmation'
import { useSearchParams } from 'react-router-dom'
// import { ActionButtonForBA } from './ActionButtonForBA'
import { ActionButtonGeneric } from './ActionButtonGeneric'
import { getSerialNumber } from '@/utils/helper'

export const EntityManagement = () => {
	const {
		AllBaQuery,
		AllCSinkManagerQuery,
		AllCSinkNetwork,
		AllAPN,
		AllAPs,
		tabs,
		tabParams,
		tabEnum,
		limit,
		handleTabChange,
		isActionInfoDrawer,
		handleCloseDrawer,
		farmCoordinates,
		setFarmCoordinates,
		handelOpenDrawer,
		selectedId,
		navigate,
		showMap,
		setShowMap,
		setSelectedId,
		setSearchParams,
		assignBiomass,
		setAssignBiomass,
		handleSaveKml,
		showBiomassReferenceDialog,
		setshowBiomassReferenceDialog,
		showAssignedBiomassDrawer,
		setshowAssignedBiomassDrawer,
		handleAPNRowClick,
		setShowArtisaProListDrawer,
		showArtisaProListDrawer,
		handleArtisanProRowClick,
		handleRowClick,
		handleExcelDownload,
		handleCsinkNetworkRowClick,
		showAdminsDrawer,
		setShowAdminsDrawer,
		handleSuspendEntity,
		fetchBiomassTypeList,
	} = useEntityManagement()
	const [type, setType] = useState('')
	const [suspendId, setSuspendId] = useState<string | null>(null)
	// const [showSuspendDialog, setShowSuspendDialog] = useState(false)
	const [searchParams] = useSearchParams()
	const tabName = searchParams.get('tab')
	const HeaderEndButtons = () => (
		<Stack direction='row' spacing={2}>
			<Button
				onClick={handelOpenDrawer}
				variant='contained'
				size='small'
				startIcon={<AddRounded />}>
				Add Entity
			</Button>
		</Stack>
	)

	const entityNames = {
		[tabEnum.ba]: 'Biomass Aggregator',
		[tabEnum.cSinkManager]: 'CSink Manager',
		[tabEnum.cSinkNetwork]: 'CSink Network',
		[tabEnum.apn]: 'Artisan Pro Network',
		[tabEnum.artisanpros]: 'Artisan Pro',
	}

	const handleViewKMLFile = useCallback(
		(
			farmCoordinates: ILocation[],
			center: { x?: string; y?: string },
			networkId: string
		) => {
			const formattedCoordinates = farmCoordinates.map((coordinate) => ({
				lat: coordinate.x,
				lng: coordinate.y,
			}))
			const searchParams = new URLSearchParams(window.location.search)
			searchParams.set('lat', center.x ?? '')
			searchParams.set('long', center.y ?? '')
			searchParams.set('networkId', networkId)
			navigate(`?${searchParams.toString()}`, { replace: true })
			setFarmCoordinates(formattedCoordinates)
			setShowMap(true)
		},
		[navigate, setFarmCoordinates, setShowMap]
	)

	const biomassAggregatorColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				flex: 0.5,
				minWidth: 80,
			},
			{
				field: 'name',
				headerName: 'Name',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'csinkManagerName',
				headerName: 'CSM Name',
				minWidth: 120,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.row?.csinkManagerName ?? '-'}
					</Typography>
				),
			},
			{
				field: 'shortName',
				headerName: 'BA-Short Name',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'managerDetails',
				headerName: 'Managers',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Tooltip
						onClick={(e) => {
							e.stopPropagation()
							e.preventDefault()
							setShowAdminsDrawer(params?.row?.managerDetails)
						}}
						title={params?.row?.managerDetails
							?.map((i: ManagerDetails) => i?.managerName)
							?.join(', ')}>
						<Box>
							<MultipleAvatarWrapper
								images={
									params?.row?.managerDetails?.map(
										(operator: ManagerDetails) => operator?.profileImageUrl
									) || []
								}
								length={params?.row?.managerDetails?.length || 0}
							/>
						</Box>
					</Tooltip>
				),
			},
			{
				field: 'locationName',
				headerName: 'Location',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'suspended',
				headerName: 'Status',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography color={params.value ? 'error.main' : 'success.main'}>
						{params.value ? 'Suspended' : 'Active'}
					</Typography>
				),
			},
			{
				field: 'action',
				headerName: 'Action',
				flex: 1,
				renderCell: (params) => (
					<ActionButtonGeneric
						handleExcelDownload={handleExcelDownload}
						row={params?.row}
						isBa={true}
						onEdit={() => {
							handelOpenDrawer()
							setSelectedId(params?.row?.id)
							setType('ba')
						}}
						onSuspend={(id: string) => {
							setSuspendId(id)
							// setShowSuspendDialog(true)
						}}
						onActivate={(id: string) => {
							handleSuspendEntity(id, false)
						}}
						setshowBiomassReferenceDialog={setshowBiomassReferenceDialog}
					/>
				),
			},
		],
		[
			handelOpenDrawer,
			handleExcelDownload,
			handleSuspendEntity,
			setSelectedId,
			setShowAdminsDrawer,
			setshowBiomassReferenceDialog,
		]
	)

	const cSinkManagerColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				flex: 0.5,
				minWidth: 80,
				renderCell: (params) => {
					return (
						<Typography variant='subtitle1'>
							{getSerialNumber(params, Number(limit))}
						</Typography>
					)
				},
			},
			{
				field: 'name',
				headerName: 'Name',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'shortName',
				headerName: 'Short Name',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},

			{
				field: 'managerDetails',
				headerName: 'Managers',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Tooltip
						onClick={(e) => {
							e.stopPropagation()
							e.preventDefault()
							setShowAdminsDrawer(params?.row?.managerDetails)
						}}
						title={params?.row?.managerDetails
							?.map((i: ManagerDetails) => i?.managerName)
							?.join(', ')}>
						<Box>
							<MultipleAvatarWrapper
								images={
									params?.row?.managerDetails?.map(
										(operator: ManagerDetails) => operator?.profileImageUrl
									) || []
								}
								length={params?.row?.managerDetails?.length || 0}
							/>
						</Box>
					</Tooltip>
				),
			},
			{
				field: 'locationName',
				headerName: 'Location',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'suspended',
				headerName: 'Status',
				flex: 1,
				minWidth: 80,
				renderCell: (params) => (
					<Typography color={params.value ? 'error.main' : 'success.main'}>
						{params.value ? 'Suspended' : 'Active'}
					</Typography>
				),
			},
			{
				field: 'assignedBiomass',
				headerName: 'Assigned Biomass',
				flex: 1,
				minWidth: 180,
				renderCell: (params) => {
					const cropNames = params?.row?.crops
						?.map((crop: CropforCsinkManager) => crop.cropName)
						?.join(', ')
					return (
						<Tooltip
							componentsProps={{
								tooltip: {},
							}}
							title={cropNames}
							onClick={() =>
								setshowAssignedBiomassDrawer({
									biomass: params?.row?.crops ?? [],
									id: params?.row?.id,
								})
							}
							placement='bottom'>
							<Typography>
								{cropNames?.length > 40
									? `${cropNames?.slice(0, 40)}...`
									: `${cropNames}`}
							</Typography>
						</Tooltip>
					)
				},
			},

			{
				field: 'assignBiomass',
				headerName: 'Assign Biomass',
				flex: 1,
				minWidth: 80,
				renderCell: (params) => (
					<IconButton
						onClick={() => {
							setAssignBiomass({
								id: params?.row?.id,
								biomass: params?.row?.crops ?? [],
							})
						}}>
						<Add color='primary' />
					</IconButton>
				),
			},
			{
				field: 'mixingType',
				headerName: 'Mixing Type',
				flex: 1,
				minWidth: 220,
				renderCell: (params) => {
					const show: boolean =
						params?.row?.mixingTypes?.length > 0 ? true : false
					return show ? (
						<Button
							color='inherit'
							onClick={() => {
								setType(EntityEnum.mixingType)
								setSelectedId(params?.row?.id)
								handelOpenDrawer()
							}}>
							<Stack rowGap={0.5} justifyContent={'center'} height={'100%'}>
								{params?.row?.mixingTypes
									?.slice(0, 2)
									?.map((item: IManagerMixingType, idx: number) => (
										<Typography key={idx} variant='body1'>
											{item?.name}
										</Typography>
									))}
							</Stack>
						</Button>
					) : (
						<IconButton
							size='small'
							onClick={() => {
								setType(EntityEnum.mixingType)
								setSelectedId(params?.row?.id)
								handelOpenDrawer()
							}}>
							<Add color='primary' />
						</IconButton>
					)
				},
			},
			{
				field: 'application',
				headerName: 'Application Type',
				flex: 1,
				minWidth: 220,
				renderCell: (params) => {
					const show: boolean =
						params?.row?.applicationTypes?.length > 0 ? true : false
					return show ? (
						<Button
							color='inherit'
							onClick={() => {
								setType(EntityEnum.applicationsType)
								setSelectedId(params?.row?.id)
								handelOpenDrawer()
							}}>
							<Stack rowGap={0.5} justifyContent={'center'} height={'100%'}>
								{params?.row?.applicationTypes
									?.slice(0, 2)
									?.map((item: IManagerApplicationType, idx: number) => (
										<Typography key={idx} variant='body1'>
											{item?.type}
										</Typography>
									))}
							</Stack>
						</Button>
					) : (
						<IconButton
							size='small'
							onClick={() => {
								setType(EntityEnum.applicationsType)
								setSelectedId(params?.row?.id)
								handelOpenDrawer()
							}}>
							<Add color='primary' />
						</IconButton>
					)
				},
			},
			{
				field: 'biomassReference',
				headerName: 'Biomass Reference',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => {
					return (
						<BiomassReferenceColumn
							biomassReferenceValue={params?.row?.biomassReferenceValue || []}
							onClick={() =>
								setshowBiomassReferenceDialog({
									id: params?.row?.id,
									type: EntityEnum.cSinkManager,
								})
							}
						/>
					)
				},
			},
			{
				field: 'action',
				headerName: 'Action',
				flex: 1,
				minWidth: 80,
				renderCell: (params) => (
					<ActionButtonGeneric
						row={params.row}
						onEdit={(id) => {
							handelOpenDrawer()
							setSelectedId(id)
							setType('cSinkManager')
						}}
						onSuspend={(id) => {
							setSuspendId(id)
						}}
						onActivate={(id) => {
							handleSuspendEntity(id, false)
						}}
					/>
				),
			},
		],
		[
			limit,
			setShowAdminsDrawer,
			setshowAssignedBiomassDrawer,
			setAssignBiomass,
			setSelectedId,
			handelOpenDrawer,
			setshowBiomassReferenceDialog,
			handleSuspendEntity,
		]
	)

	const aPNColumn: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Network Name',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'biomassAggregatorName',
				headerName: 'BA Name',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'managers',
				headerName: 'Managers',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Tooltip
						onClick={(e) => {
							e.stopPropagation()
							e.preventDefault()
							setShowAdminsDrawer(params?.row?.managers)
						}}
						title={params?.row?.managers
							?.map((i: ManagerDetails) => i?.managerName)
							?.join(', ')}>
						<Box>
							<MultipleAvatarWrapper
								images={
									params?.row?.managers?.map(
										(operator: ManagerDetails) => operator?.profileImageUrl
									) || []
								}
								length={params?.row?.managers?.length || 0}
							/>
						</Box>
					</Tooltip>
				),
			},

			{
				field: 'address',
				headerName: 'Location',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'artisanProCount',
				headerName: 'Artisan pro Count',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => <Typography>{params?.value}</Typography>,
			},
			//here
			{
				field: 'suspended',
				headerName: 'Status',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography color={params.value ? 'error.main' : 'success.main'}>
						{params.value ? 'Suspended' : 'Active'}
					</Typography>
				),
			},
			{
				field: 'action',
				headerName: 'Action',
				minWidth: 80,
				renderCell: (params) => (
					<ActionButtonGeneric
						row={params.row}
						onEdit={(id) => {
							handelOpenDrawer()
							setSelectedId(id)
							setType('apn')
						}}
						onSuspend={(id) => {
							setSuspendId(id)
						}}
						onActivate={(id) => {
							handleSuspendEntity(id, false)
						}}
					/>
				),
			},
		],
		[handelOpenDrawer, handleSuspendEntity, setSelectedId, setShowAdminsDrawer]
	)

	const artisianPros: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Artisan Pro',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'artisianProNetworkName',
				headerName: 'Artisian Pro Network',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'managers',
				headerName: 'Managers',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Tooltip
						onClick={(e) => {
							e.stopPropagation()
							e.preventDefault()
							setShowAdminsDrawer(params?.row?.managers)
						}}
						title={params?.row?.managers
							?.map((i: ManagerDetails) => i?.managerName)
							?.join(', ')}>
						<Box>
							<MultipleAvatarWrapper
								images={
									params?.row?.managers?.map(
										(operator: ManagerDetails) => operator?.profileImageUrl
									) || []
								}
								length={params?.row?.managers?.length || 0}
							/>
						</Box>
					</Tooltip>
				),
			},

			{
				field: 'address',
				headerName: 'Location',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},

			{
				field: 'farmArea',
				headerName: 'KML File',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Stack>
						<AddKmlButton
							data={{
								name: params?.row?.name,
								landmark: params?.row?.address,
								coordinate: '(0,0)',
								farmArea: params?.row?.kmlCoordinates,
								id: params?.row?.id,
							}}
							handleViewKMLFile={handleViewKMLFile}
							setSearchParams={setSearchParams}
							setShowMap={(bool: boolean) => setShowMap(bool)}
						/>
					</Stack>
				),
			},
			{
				field: 'biomassReference',
				headerName: 'Biomass Reference',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => {
					return (
						<BiomassReferenceColumn
							biomassReferenceValue={params?.row?.biomassReferenceValue || []}
							onClick={() =>
								setshowBiomassReferenceDialog({
									id: params?.row?.id,
									type: EntityEnum.aps,
								})
							}
						/>
					)
				},
			},
			{
				field: 'suspended',
				headerName: 'Status',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography color={params.value ? 'error.main' : 'success.main'}>
						{params.value ? 'Suspended' : 'Active'}
					</Typography>
				),
			},
			{
				field: 'action',
				headerName: 'Action',
				minWidth: 80,
				renderCell: (params) => (
					<ActionButtonGeneric
						row={params.row}
						onEdit={(id) => {
							handelOpenDrawer()
							setSelectedId(id)
							setType('aps')
						}}
						onSuspend={(id) => {
							setSuspendId(id)
						}}
						onActivate={(id) => {
							handleSuspendEntity(id, false)
						}}
					/>
				),
			},
		],
		[
			handelOpenDrawer,
			handleSuspendEntity,
			handleViewKMLFile,
			setSearchParams,
			setSelectedId,
			setShowAdminsDrawer,
			setShowMap,
			setshowBiomassReferenceDialog,
		]
	)

	const cSinkNetworks: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Network Name',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'biomassAggregatorName',
				headerName: 'BA Name',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'managerDetails',
				headerName: 'Managers',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Tooltip
						onClick={(e) => {
							e.stopPropagation()
							e.preventDefault()
							setShowAdminsDrawer(params?.row?.managerDetails)
						}}
						title={params?.row?.managerDetails
							?.map((i: ManagerDetails) => i?.managerName)
							?.join(', ')}>
						<Box>
							<MultipleAvatarWrapper
								images={
									params?.row?.managerDetails?.map(
										(operator: ManagerDetails) => operator?.profileImageUrl
									) || []
								}
								length={params?.row?.managerDetails?.length || 0}
							/>
						</Box>
					</Tooltip>
				),
			},

			{
				field: 'locationName',
				headerName: 'Location',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'methaneCompensationStrategy',
				headerName: 'Methane Strategy',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'farmArea',
				headerName: 'KML File',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<AddKmlButton
						data={{
							name: params?.row?.name,
							landmark: params?.row?.locationName,
							coordinate: '(0,0)',
							farmArea: params?.row?.kmlCoordinates,
							id: params?.row?.id,
						}}
						handleViewKMLFile={handleViewKMLFile}
						setSearchParams={setSearchParams}
						setShowMap={(bool: boolean) => setShowMap(bool)}
					/>
				),
			},
			{
				field: 'biomassReference',
				headerName: 'Biomass Reference',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => {
					return (
						<BiomassReferenceColumn
							biomassReferenceValue={params?.row?.biomassReferenceValue || []}
							onClick={() =>
								setshowBiomassReferenceDialog({
									id: params?.row?.id,
									type: EntityEnum.cSinkNetwork,
								})
							}
						/>
					)
				},
			},
			//here
			{
				field: 'suspended',
				headerName: 'Status',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography color={params.value ? 'error.main' : 'success.main'}>
						{params.value ? 'Suspended' : 'Active'}
					</Typography>
				),
			},
			{
				field: 'action',
				headerName: 'Action',
				minWidth: 80,
				renderCell: (params) => (
					<ActionButtonGeneric
						row={params.row}
						onEdit={(id) => {
							handelOpenDrawer()
							setSelectedId(id)
							setType('cSinkNetwork')
						}}
						onSuspend={(id) => {
							setSuspendId(id)
						}}
						onActivate={(id) => {
							handleSuspendEntity(id, false)
						}}
					/>
				),
			},
		],
		[
			handelOpenDrawer,
			handleSuspendEntity,
			handleViewKMLFile,
			setSearchParams,
			setSelectedId,
			setShowAdminsDrawer,
			setShowMap,
			setshowBiomassReferenceDialog,
		]
	)

	return (
		<>
			<ActionInformationDrawer
				open={isActionInfoDrawer}
				onClose={handleCloseDrawer}
				anchor='right'
				component={
					<AddEntity
						handleCloseDrawer={handleCloseDrawer}
						fetchBiomassTypeList={fetchBiomassTypeList}
						editMode={!!selectedId}
						baId={selectedId}
						cSinkId={selectedId}
						apnId={selectedId}
						apsId={selectedId}
						cSinkNetworkId={selectedId}
						type={type}
					/>
				}
			/>
			<ActionInformationDrawer
				open={!!showAdminsDrawer}
				onClose={() => setShowAdminsDrawer(null)}
				anchor='right'
				component={
					<AdminDetails
						close={() => setShowAdminsDrawer(null)}
						managerDetails={showAdminsDrawer}
					/>
				}
			/>

			<ActionInformationDrawer
				open={!!showBiomassReferenceDialog}
				onClose={() => setshowBiomassReferenceDialog(null)}
				anchor='right'
				component={
					<AddBiomassReference
						id={showBiomassReferenceDialog?.id ?? ''}
						networkType={showBiomassReferenceDialog?.type as EntityEnum}
						onClose={() => setshowBiomassReferenceDialog(null)}
					/>
				}
			/>
			<ActionInformationDrawer
				open={!!showAssignedBiomassDrawer}
				onClose={() => setshowAssignedBiomassDrawer(null)}
				anchor='right'
				component={
					<AssignedBiomassList
						csinkManagerId={showAssignedBiomassDrawer?.id ?? ''}
						BiomassList={showAssignedBiomassDrawer?.biomass || []}
						handleClose={() => setshowAssignedBiomassDrawer(null)}
					/>
				}
			/>
			<ActionInformationDrawer
				open={!!showArtisaProListDrawer}
				onClose={() => setShowArtisaProListDrawer(null)}
				anchor='right'
				component={
					<ArtisanProList
						name={showArtisaProListDrawer?.name || ''}
						handleClose={() => setShowArtisaProListDrawer(null)}
						id={showArtisaProListDrawer?.id || ''}
					/>
				}
			/>
			{suspendId ? (
				<Confirmation
					open={!!suspendId}
					handleClose={() => setSuspendId(null)}
					handleNoClick={() => setSuspendId(null)}
					handleYesClick={() => {
						handleSuspendEntity(suspendId, true)
						setSuspendId(null)
					}}
					confirmationText={`Are you sure you want to mark this ${
						entityNames[tabName as keyof typeof entityNames] || 'Csink Manager'
					} as suspended, 
						as this will not allow the user of this
					${
						entityNames[tabName as keyof typeof entityNames] || 'Csink Manager'
					}  to login in the App or Admin Panel`}
				/>
			) : null}
			<StyledContainer>
				<Box className='header'>
					<CustomHeader
						showBottomBorder={true}
						heading='Entity Management'
						showButton={false}
						endComponent={
							<>
								<QueryInput
									className='search-textFiled'
									queryKey='search'
									placeholder='Search'
									setPageOnSearch
									InputProps={{
										startAdornment: <Search fontSize='small' />,
									}}
								/>
								<HeaderEndButtons />
							</>
						}
					/>
				</Box>

				<TabContext value={tabParams}>
					<TabList className='tabList' onChange={handleTabChange}>
						{tabs.map(({ label, value, show, count }, index) =>
							show ? (
								<Tab
									key={index}
									label={label}
									value={value}
									sx={{
										position: 'relative',
										minWidth: theme.spacing(12),
									}}
									icon={
										<Avatar
											className='tab-count'
											sx={{
												backgroundColor:
													tabParams === value ? theme.palette.primary.main : '',
											}}>
											{count}
										</Avatar>
									}
									iconPosition='top'
								/>
							) : null
						)}
					</TabList>
					<TabPanel value={tabEnum.cSinkManager} className='tab-panel'>
						<CustomDataGrid
							showPagination
							columns={cSinkManagerColumn}
							loading={AllCSinkManagerQuery?.isLoading}
							rows={AllCSinkManagerQuery?.data?.csinkManagers || []}
							rowCount={AllCSinkManagerQuery?.data?.count}
						/>
					</TabPanel>
					<TabPanel value={tabEnum.ba} className='tab-panel'>
						<CustomTable
							showPagination
							columns={biomassAggregatorColumn}
							isLoading={AllBaQuery?.isLoading}
							handleRowClick={handleRowClick}
							rows={AllBaQuery?.data?.biomassAggregators || []}
							count={AllBaQuery?.data?.count || 0}
							isComponent
							component='ArtisanAndCsinkList'
						/>
					</TabPanel>
					<TabPanel value={tabEnum.apn} className='tab-panel'>
						<CustomDataGrid
							showPagination
							columns={aPNColumn}
							loading={AllAPN?.isLoading}
							onRowClick={handleAPNRowClick}
							rows={AllAPN?.data?.artisanProNetworks || []}
							rowCount={AllAPN?.data?.count}
						/>
					</TabPanel>
					<TabPanel value={tabEnum.cSinkNetwork} className='tab-panel'>
						<CustomDataGrid
							showPagination
							columns={cSinkNetworks}
							onRowClick={handleCsinkNetworkRowClick}
							loading={AllCSinkNetwork?.isLoading}
							rows={AllCSinkNetwork?.data?.network || []}
							rowCount={AllCSinkNetwork?.data?.count}
						/>
					</TabPanel>
					<TabPanel value={tabEnum.artisanpros} className='tab-panel'>
						<CustomDataGrid
							showPagination
							columns={artisianPros}
							loading={AllAPs?.isLoading}
							onRowClick={handleArtisanProRowClick}
							rows={AllAPs?.data?.artisanPros || []}
							rowCount={AllAPs?.data?.count}
						/>
					</TabPanel>
				</TabContext>
				{showMap ? (
					<GoogleMapsDraw
						open={showMap}
						handleModalClose={() => {
							setSearchParams((params) => {
								params.delete('lat')
								params.delete('long')
								params.delete('networkId')

								return params
							})
							setShowMap(false)
							setFarmCoordinates([])
						}}
						handleSave={handleSaveKml}
						initialPolygons={farmCoordinates}
					/>
				) : null}
			</StyledContainer>
			{assignBiomass ? (
				<AssignBiomassToCsManager
					id={assignBiomass.id}
					onClose={() => setAssignBiomass(null)}
					open={!!assignBiomass}
					assignedBiomass={assignBiomass.biomass}
				/>
			) : null}
		</>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.tabList': {
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		padding: theme.spacing(0, 3),
		'.MuiTab-root': {
			paddingRight: theme.spacing(2),
		},
		'.tab-count': {
			position: 'absolute',
			right: 0,
			padding: theme.spacing(1.5, 1, 1, 1),
			top: 5,
			height: theme.spacing(2.5),
			fontSize: theme.spacing(1.3),
			width: theme.spacing(2.6),
		},
	},
	'.search-textFiled': {
		width: 300,
		'.MuiInputBase-root': {
			borderRadius: theme.spacing(1.25),
			height: theme.spacing(4.75),
		},
	},
}))
