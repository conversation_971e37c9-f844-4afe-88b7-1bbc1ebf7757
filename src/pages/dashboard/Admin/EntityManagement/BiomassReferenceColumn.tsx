import { BiomassReference } from '@/interfaces'
import { Add } from '@mui/icons-material'
import { IconButton, Stack, Typography } from '@mui/material'

interface IProps {
	onClick: () => void
	biomassReferenceValue: BiomassReference[]
}
export const BiomassReferenceColumn = ({
	onClick,
	biomassReferenceValue,
}: IProps) => {
	const length = biomassReferenceValue?.length
	const biomassName = biomassReferenceValue?.[0]?.biomassName
	const quantity = biomassReferenceValue?.[0]?.biomassQuantity || 0
	return (
		<Stack
			onClick={(e) => {
				e.stopPropagation()
				onClick()
			}}
			alignItems='center'>
			{length ? (
				<Typography>
					{biomassName} : {quantity}kgs {length > 1 ? '..' : ''}
				</Typography>
			) : (
				<IconButton
					sx={{
						width: 'fit-content',
					}}>
					<Add color='primary' />
				</IconButton>
			)}
		</Stack>
	)
}
