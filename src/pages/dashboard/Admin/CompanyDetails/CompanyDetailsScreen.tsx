import { Breadcrumbs, Stack, Typography } from '@mui/material'
import { CompanyDetailsWithStatus } from './components'
import { ContractDetails } from './components/ContractDetails'
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import { useCompanyDetails } from './useCompanydetails'
import { useNavigate } from 'react-router-dom'
const BreadcrumbHeader = () => {
	const navigate = useNavigate()
	return (
		<Stack direction='row' alignItems='center' gap={1} sx={{ px: 2, py: 1 }}>
			<ChevronLeftIcon
				fontSize='small'
				sx={{ color: 'grey.500' }}
				onClick={() => navigate(-1)}
			/>
			<Breadcrumbs separator='/' aria-label='breadcrumb'>
				<Typography variant='body2' color='text.disabled'>
					CRM
				</Typography>
				<Typography variant='body1' fontWeight={500}>
					Account Management
				</Typography>
			</Breadcrumbs>
		</Stack>
	)
}

export const CompanyDetailsScreen = () => {
	const {
		isApproved,
		assessCompanyRequestMutation,
		handleDownload,
		getCompanyDetails,
		handleFileChange,
		isshowSignedDoc,
		handleUpdateStatus,
		isShowContractDetails,
	} = useCompanyDetails()

	return (
		<Stack sx={{ width: '100%', padding: 2 }} gap={2}>
			<BreadcrumbHeader />
			<CompanyDetailsWithStatus
				assessCompanyRequestMutation={assessCompanyRequestMutation}
				handleDownload={handleDownload}
				getCompanyDetails={getCompanyDetails}
				isApproved={isApproved}
			/>
			{isShowContractDetails && (
				<ContractDetails
					getCompanyDetails={getCompanyDetails}
					handleDownload={handleDownload}
					handleFileChange={handleFileChange}
					isshowSignedDoc={isshowSignedDoc}
					handleUpdateStatus={handleUpdateStatus}
				/>
			)}
		</Stack>
	)
}
