import { authAxios, useAuthContext } from '@/contexts'
import {
	IAllConstants,
	ICarbonConstantsResponse,
	ICSinkManagerResponse,
	UpdatedAddConstant,
} from '@/interfaces'
import { addConstantTabs, userRoles } from '@/utils/constant'
import { yupResolver } from '@hookform/resolvers/yup'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { addConstantSchema } from './schema'

const ProductionConstantsFallbacks = {
	artisanProMaximumMoisture: 20,
	artisanProMaximumVideoLengthInSeconds: 15,
	artisanProMinimumFiringImages: 3,
	artisanProMinimumFiringVideos: 3,
	artisanProMinimumTemperature: 650,
	artisanProMinimumTemperatureImages: 1,
	creditsToRegister: 0,
	csinkNetworkMaximumMoisture: 20,
	csinkNetworkMaximumVideoLengthInSeconds: 15,
	csinkNetworkMinimumFiringImages: 3,
	csinkNetworkMinimumFiringVideos: 3,
	csinkNetworkMinimumTemperature: 650,
	csinkNetworkMinimumTemperatureImages: 1,

	// csinkNetworkMoistureRequired: true,
	// csinkNetworkTemperatureRequired: true,
	// csinkNetworkFiringVideosIncluded: true,
	// artisanProFiringVideosIncluded: true,
	// csinkNetworkSeparateChimneyImages: false,
	// artisanProSeparateChimneyImages: false,
	// csinkNetworkSeparateQuenchingImage: false,
	// artisanProSeparateQuenchingImage: false,
}

export const useAddConstants = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsCsinkManagerId = searchParams.get('csinkManagerId') ?? ''
	const { userDetails } = useAuthContext()
	const CsinkManagerName = userDetails?.csinkManagerName ?? ''
	const paramsTab =
		searchParams.get('tab') ?? addConstantTabs.productionConstants

	const isCsinkManagerLogin = useMemo(() => {
		return userDetails?.accountType === userRoles.CsinkManager
	}, [userDetails?.accountType])

	const fetchCsinkManagerDetails = useQuery({
		queryKey: ['fetchCsinkManagerDetails', paramsCsinkManagerId],
		queryFn: async () => {
			const { data } = await authAxios.get<{
				csinkManagerCurrentConstant: UpdatedAddConstant
				csinkManagerPreviousConstant: UpdatedAddConstant[]
			}>(
				`csink-manager/${
					isCsinkManagerLogin
						? userDetails?.csinkManagerId
						: paramsCsinkManagerId
				}/constants`
			)
			return data
		},
		enabled: Boolean(paramsCsinkManagerId),
	})
	const globalConstantQuery = useQuery({
		queryKey: ['globalConstantQuery', paramsCsinkManagerId],
		queryFn: async () => {
			const { data } = await authAxios.get<ICarbonConstantsResponse>(
				`/constant`
			)
			return data
		},
		enabled:
			userDetails?.accountType === userRoles.Admin &&
			paramsTab === addConstantTabs.carbonCreditConstants,
	})

	// Can implement in future

	// const getFormDefaultValue = (
	// 	key: keyof UpdatedAddConstant,
	// 	defaultValue: Nullable<number | undefined>
	// ) => {
	// 	return isCsinkManagerLogin
	// 		? fetchCsinkManagerDetails?.data?.[key] ?? defaultValue
	// 		: defaultValue
	// }

	const handleDefaultValues = useCallback(() => {
		switch (paramsTab) {
			case addConstantTabs.productionConstants:
				return {
					csinkManagerId: '',
					csinkNetworkMoistureRequired: null,
					csinkNetworkMaximumMoisture: null,
					artisanProMaximumMoisture: null,

					// Temperature
					csinkNetworkTemperatureRequired: null,
					csinkNetworkMinimumTemperatureImages: null,
					csinkNetworkMinimumTemperature: null,
					artisanProMinimumTemperatureImages: null,
					artisanProMinimumTemperature: null,

					// Quenching Image
					csinkNetworkSeparateQuenchingImage: null,
					artisanProSeparateQuenchingImage: null,

					// Firing Video, Chimney Image, and others
					csinkNetworkFiringVideosRequired: null,
					csinkNetworkSeparateChimneyImage: null,
					artisanProSeparateChimneyImage: null,
					artisanProMaximumVideoLengthInSeconds: null,
					csinkNetworkMaximumVideoLengthInSeconds: null,

					artisanProPreQuenchingImageRequired: null,
					csinkNetworkPreQuenchingImageRequired: null,

					csinkNetworkMinimumFiringVideos: null,
					csinkNetworkMinimumFiringImages: null,
					artisanProMinimumFiringImages: null,
					artisanProMinimumFiringVideos: null,

					// Credits
					creditsToRegister: null,

					//email boolean
					isBatchRejectionEmailEnabled: null,
					isBatchCommentEmailEnabled: null,
				}
			case addConstantTabs.carbonCreditConstants:
				return {
					ccConstantNumerator: null,
					ccConstantDenominator: null,
					pacFraction: null,
					spcFraction: null,
				}
		}
	}, [paramsTab])
	const {
		control,
		handleSubmit,
		setValue,
		formState,
		reset,
		register,
		watch,
		clearErrors,
	} = useForm({
		defaultValues: handleDefaultValues(),
		mode: 'all',
		resolver: yupResolver<any>(addConstantSchema(paramsTab as addConstantTabs)),
	})

	const fetchCsinkManagers = useQuery({
		queryKey: ['fetchCsinkManagers'],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				limit: '500',
				page: '0',
			})
			const { data } = await authAxios.get<ICSinkManagerResponse>(
				`/csink-manager?${queryParams.toString()}`
			)
			return data
		},
		select: (data) => {
			return data?.csinkManagers?.map((item) => ({
				value: item.id,
				label: item.name,
			}))
		},
		enabled: userDetails?.accountType === userRoles.Admin,
	})
	const handleUpdateProductionConstantsMutation = useMutation({
		mutationKey: ['updateProductionConstants'],
		mutationFn: async (formData: IAllConstants) => {
			const api = `csink-manager/${formData?.csinkManagerId}/constants`

			const { data } = await authAxios.put(api, formData)
			return data
		},
		onError(error) {
			toast(`Something went wrong`)
			console.error('Error updating the constants:', error)
		},
		onSuccess: (data) => {
			fetchCsinkManagerDetails.refetch()
			toast(data?.message || `Constants Added Successfully !!`)
		},
	})
	const handleUpdateGlobalConstantsMutation = useMutation({
		mutationKey: ['updateGlobalConstants'],
		mutationFn: async (formData: IAllConstants) => {
			const api = `/constant`

			const { data } = await authAxios.post(api, formData)
			return data
		},
		onError(error) {
			toast(`Something went wrong`)
			console.error('Error updating the constants:', error)
		},
		onSuccess: (data) => {
			globalConstantQuery.refetch()
			toast(data?.message || `Constants Added Successfully !!`)
		},
	})

	const onSubmit = async (formData: IAllConstants) => {
		paramsTab === addConstantTabs.productionConstants
			? handleUpdateProductionConstantsMutation.mutate(formData)
			: handleUpdateGlobalConstantsMutation.mutate(formData)
	}
	const handleTabChange = useCallback(
		(_: unknown, newValue: addConstantTabs) => {
			reset()
			setSearchParams(
				(prev) => ({
					...prev,
					tab: newValue,
				}),
				{ replace: true }
			)
		},
		[reset, setSearchParams]
	)

	useEffect(() => {
		if (userDetails?.accountType === userRoles.CsinkManager) {
			setSearchParams(
				(urlParams) => {
					urlParams.set('csinkManagerId', userDetails?.csinkManagerId)
					return urlParams
				},
				{ replace: true }
			)
			setValue('csinkManagerId', userDetails?.csinkManagerId)
		}
	}, [
		setSearchParams,
		setValue,
		userDetails?.accountType,
		userDetails?.csinkManagerId,
	])

	// Called due to late value set in the form.
	useEffect(() => {
		if (paramsTab !== addConstantTabs.productionConstants) return
		if (!fetchCsinkManagerDetails?.data) return

		const constants =
			fetchCsinkManagerDetails?.data?.csinkManagerCurrentConstant

		setValue(
			'artisanProMaximumMoisture',
			constants?.artisanProMaximumMoisture ??
				ProductionConstantsFallbacks['artisanProMaximumMoisture'] ??
				null
		)
		setValue(
			'artisanProMinimumTemperature',
			constants?.artisanProMinimumTemperature ??
				ProductionConstantsFallbacks['artisanProMinimumTemperature'] ??
				null
		)

		setValue(
			'artisanProMinimumFiringVideos',
			constants?.artisanProMinimumFiringVideos ??
				ProductionConstantsFallbacks['artisanProMinimumFiringVideos'] ??
				null
		)

		setValue(
			'csinkNetworkMinimumFiringVideos',
			constants?.csinkNetworkMinimumFiringVideos ??
				ProductionConstantsFallbacks['csinkNetworkMinimumFiringVideos'] ??
				null
		)

		setValue(
			'artisanProMinimumTemperatureImages',
			constants?.artisanProMinimumTemperatureImages ??
				ProductionConstantsFallbacks['artisanProMinimumTemperatureImages'] ??
				null
		)

		setValue(
			'artisanProSeparateQuenchingImage',
			constants?.artisanProSeparateQuenchingImage ?? null
		)
		setValue(
			'artisanProSeparateChimneyImage',
			constants?.artisanProSeparateChimneyImage ?? null
		)
		setValue(
			'artisanProMaximumVideoLengthInSeconds',
			constants?.artisanProMaximumVideoLengthInSeconds ??
				ProductionConstantsFallbacks['artisanProMaximumVideoLengthInSeconds'] ??
				null
		)

		setValue(
			'csinkNetworkMaximumMoisture',
			constants?.csinkNetworkMaximumMoisture ??
				ProductionConstantsFallbacks['csinkNetworkMaximumMoisture'] ??
				null
		)
		setValue(
			'csinkNetworkMoistureRequired',
			constants?.csinkNetworkMoistureRequired ?? null
		)
		setValue(
			'csinkNetworkTemperatureRequired',
			constants?.csinkNetworkTemperatureRequired ?? null
		)
		setValue(
			'csinkNetworkMinimumTemperature',
			constants?.csinkNetworkMinimumTemperature ??
				ProductionConstantsFallbacks['csinkNetworkMinimumTemperature'] ??
				null
		)
		setValue(
			'csinkNetworkMinimumTemperatureImages',
			constants?.csinkNetworkMinimumTemperatureImages ??
				ProductionConstantsFallbacks['csinkNetworkMinimumTemperatureImages'] ??
				null
		)

		setValue(
			'csinkNetworkSeparateQuenchingImage',
			constants?.csinkNetworkSeparateQuenchingImage ?? null
		)
		setValue(
			'csinkNetworkSeparateChimneyImage',
			constants?.csinkNetworkSeparateChimneyImage ?? null
		)
		setValue(
			'csinkNetworkMinimumFiringVideos',
			constants?.csinkNetworkMinimumFiringVideos ??
				ProductionConstantsFallbacks['csinkNetworkMinimumFiringVideos'] ??
				null
		)
		setValue(
			'csinkNetworkMaximumVideoLengthInSeconds',
			constants?.csinkNetworkMaximumVideoLengthInSeconds ??
				ProductionConstantsFallbacks[
					'csinkNetworkMaximumVideoLengthInSeconds'
				] ??
				null
		)
		setValue(
			'creditsToRegister',
			constants?.creditsToRegister ??
				ProductionConstantsFallbacks['creditsToRegister'] ??
				null
		)

		setValue(
			'csinkNetworkMinimumFiringImages',
			constants?.csinkNetworkMinimumFiringImages ??
				ProductionConstantsFallbacks['csinkNetworkMinimumFiringImages'] ??
				null
		)
		setValue(
			'artisanProMinimumFiringImages',
			constants?.artisanProMinimumFiringImages ??
				ProductionConstantsFallbacks['artisanProMinimumFiringImages'] ??
				null
		)
		setValue(
			'csinkNetworkFiringVideosRequired',
			constants?.csinkNetworkFiringVideosRequired ?? null
		)
		setValue(
			'artisanProPreQuenchingImageRequired',
			constants?.artisanProPreQuenchingImageRequired ?? null
		)
		setValue(
			'csinkNetworkPreQuenchingImageRequired',
			constants?.csinkNetworkPreQuenchingImageRequired ?? null
		)
		//email booleans
		setValue(
			'isBatchRejectionEmailEnabled',
			constants?.isBatchRejectionEmailEnabled ?? null
		)
		setValue(
			'isBatchCommentEmailEnabled',
			constants?.isBatchCommentEmailEnabled ?? null
		)
	}, [
		fetchCsinkManagerDetails?.data,
		isCsinkManagerLogin,
		paramsCsinkManagerId,
		setValue,
		paramsTab,
	])
	useEffect(() => {
		if (
			userDetails?.accountType === userRoles.CsinkManager &&
			!paramsCsinkManagerId
		)
			return
		setValue('csinkManagerId', paramsCsinkManagerId)
	}, [userDetails?.accountType, paramsCsinkManagerId])

	useEffect(() => {
		if (paramsTab !== addConstantTabs.carbonCreditConstants) return

		setValue(
			'ccConstantNumerator',
			globalConstantQuery?.data?.currentConstant?.ccConstantNumerator
		)
		setValue(
			'ccConstantDenominator',
			globalConstantQuery?.data?.currentConstant?.ccConstantDenominator
		)
		setValue(
			'pacFraction',
			globalConstantQuery?.data?.currentConstant?.pacFraction
		)

		setValue(
			'spcFraction',
			globalConstantQuery?.data?.currentConstant?.spcFraction
		)
	}, [globalConstantQuery?.data?.currentConstant, paramsTab, setValue])

	return {
		fetchCsinkManagers,
		control,
		handleSubmit,
		setValue,
		formState,
		register,
		onSubmit,
		reset,
		isCsinkManagerLogin,
		paramsCsinkManagerId,
		watch,
		CsinkManagerName,
		clearErrors,
		fetchCsinkManagerDetails,
		handleTabChange,
		paramsTab,
		setSearchParams,
		userDetails,
		globalConstantQuery,
	}
}
