import { IPropsConstantLeftSection } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import {
	Autocomplete,
	Button,
	Stack,
	TextField,
} from '@mui/material'
import { ProductionConstantsForm } from './ProductionConstantsForm'

export const AddProductionConstant = ({
	handleSubmit,
	formState: { errors, isDirty },
	register,
	onSubmit,
	fetchCsinkManagers,
	setValue,
	isCsinkManagerLogin,
	paramsCsinkManagerId,
	CsinkManagerName,
	watch,
	reset,
	clearErrors,
	setSearchParams,
}: IPropsConstantLeftSection) => {
	return (
		<Stack
			className='container'
			component='form'
			onSubmit={handleSubmit(onSubmit)}
			gap={theme.spacing(2.5)}>
			<Autocomplete
				{...register('csinkManagerId')}
				value={
					isCsinkManagerLogin
						? { value: paramsCsinkManagerId, label: CsinkManagerName }
						: fetchCsinkManagers?.data?.find(
								(manager) => manager.value === watch('csinkManagerId')
						  ) || null
				}
				fullWidth
				onAbort={() =>
					reset(
						(formValues) => ({
							...formValues,
						}),
						{ keepDirty: false }
					)
				}
				onChange={(_, newValue) => {
					if (newValue) {
						setValue(`csinkManagerId`, newValue?.value ?? '')
						setSearchParams(
							(urlParams) => {
								urlParams.set('csinkManagerId', newValue?.value ?? '')
								return urlParams
							},
							{ replace: true }
						)
						clearErrors('csinkManagerId')
					} else {
						setSearchParams(
							(urlParams) => {
								urlParams.delete('csinkManagerId')
								return urlParams
							},
							{ replace: true }
						)
						setValue(`csinkManagerId`, '')
					}
					reset(
						(formValues) => ({
							...formValues,
						}),
						{ keepDirty: false }
					)
				}}
				readOnly={isCsinkManagerLogin}
				options={fetchCsinkManagers?.data || []}
				renderInput={(params) => (
					<TextField
						{...params}
						label='CsinkManagers'
						fullWidth
						placeholder='Select You Csink Managers'
						error={!!errors?.csinkManagerId}
						helperText={errors?.csinkManagerId?.message ?? ''}
					/>
				)}
			/>
			<Stack gap={theme.spacing(3)}>
				<ProductionConstantsForm
					register={register}
					watch={watch}
					errors={errors}
					setValue={setValue}
				/>
			</Stack>
			<Stack className='buttonContainer'>
				<Button variant='contained' type='submit' disabled={!isDirty}>
					Update Constants
				</Button>
			</Stack>
		</Stack>
	)
}
