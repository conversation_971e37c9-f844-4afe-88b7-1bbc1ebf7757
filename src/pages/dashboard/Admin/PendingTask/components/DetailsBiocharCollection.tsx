import {
	GetPendingTaskResponse,
	StatusType,
	TBiocharActionData,
	TBiocharProductionForm,
} from '@/interfaces'
import { TModal } from '@/types'
import {
	Dialog,
	DialogContent,
	DialogTitle,
	Stack,
	TextField,
	Button,
	FormHelperText,
	DialogActions,
	Typography,
	Grid,
	Box,
	Autocomplete,
	InputAdornment,
	CircularProgress,
	styled,
} from '@mui/material'
import { UseQueryResult } from '@tanstack/react-query'
import React, { useMemo } from 'react'
import { Controller } from 'react-hook-form'
import { ActionTypes, AppType } from '@/utils/constant'
import { useBiocharCollectionDetails } from './useBiocharCollectionDetails'
import { GoogleMapsWithNonDraggableMarker } from '@/components/GoogleMap'

type DetailsBiomassCollectionDetailsProps = TModal & {
	data: TBiocharActionData
	actionId: string
	getPendingTasks: UseQueryResult<GetPendingTaskResponse, Error>
	actionType: ActionTypes
	appType: AppType
}

export const DetailsBiocharCollection: React.FC<
	DetailsBiomassCollectionDetailsProps
> = ({
	open,
	onClose,
	data,
	actionId,
	getPendingTasks,
	actionType,
	appType,
}) => {
	const {
		control,
		handleSubmit,
		onSubmit,
		newData,
		setValue,
		fields,
		fetchKilns,
		measuringField,
		fetchContainers,
		watch,
		updateLocationFields,
		mapCenter,
		getButtonLabel,
	} = useBiocharCollectionDetails({
		data,
		actionId,
		getPendingTasks,
		actionType,
		onClose,
		appType,
	})

	const fieldsToShow = useMemo(() => {
		switch (actionType) {
			case ActionTypes.BIOMASS_COLLECTION:
				return [
					'biomassTypeId',
					'siteId',
					'farmerId',
					'vehicleId',
					'fuelType',
					'biomassQuantityUnit',
					'biomassQuantity',
					'fpuId',
				]
			case ActionTypes.BIOMASS_COLLECTION_CSINK:
				return [
					'biomassTypeId',
					'vehicleId',
					// 'fuelType',
					'biomassQuantityUnit',
					'biomassQuantity',
					'kilnId',
					'farmerId',
					'biomassTransportationVehicleType',
				]
			case ActionTypes.BATCH_CREATION:
				return [
					'biomassTypeId',
					'siteId',
					'biomassQuantityUnit',
					'biomassQuantity',
					'moistureContentValue',
				]
			case ActionTypes.BATCH_CREATION_CSINK:
				return [
					'biomassTypeId',
					'kilnId',
					'biomassQuantityUnit',
					'biomassQuantity',
					'moistureContentValue',
				]
			case ActionTypes.ADD_TEMPERATURE:
				return [
					'biomassTypeId',
					'kilnId',
					'siteId',
					'biomassQuantity',
					'moistureContentValue',
					'temperature',
				]
			case ActionTypes.ADD_BIOCHAR:
				return [
					'biomassTypeId',
					'siteId',
					'biomassQuantity',
					'biocharQuantity',
					'moistureContentValue',
				]
			case ActionTypes.ADD_BIOCHAR_CSINK:
				return [
					'biomassTypeId',
					'kilnId',
					'biomassQuantity',
					'biocharQuantity',
					'moistureContentValue',
				]
			case ActionTypes.ADD_VEHICLE:
				return ['name', 'number', 'fuelType', 'categoryId', 'localId', 'siteId']

			case ActionTypes.ADD_FPU:
				return [
					'localId',
					'name',
					'siteId',
					'artisanProId',
					'address',
					'latitude',
					'longitude',
				]

			default:
				return []
		}
	}, [actionType])

	const statusBody = (status: string) => {
		switch (status) {
			case 'error':
				return {
					label: 'Error',
					color: 'error',
					name: 'Retry-sync',
				}
			case 'created':
				return {
					label: 'Created',
					color: 'green',
					name: 'Approve',
				}
			case 'processed':
				return {
					label: 'Processed',
					color: 'blue',
				}
			default:
				return {
					label: '',
					color: 'black',
					name: 'Save',
				}
		}
	}

	return (
		<Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
			<DialogTitle variant='h6' textAlign='center'>
				<Stack
					flexDirection='row'
					justifyContent={'center'}
					alignItems={'center'}
					p={1}>
					<Typography variant='body2'>
						{actionType?.split('_').join(' ')}
					</Typography>
					<Stack
						flexDirection={'row'}
						gap={1}
						sx={{
							position: 'absolute',
							right: 20,
							top: 20,
						}}>
						<Typography variant='body1'>Status: </Typography>
						<Typography color={statusBody(data?.statusType)?.color}>
							{statusBody(data?.statusType)?.label}
						</Typography>
					</Stack>
				</Stack>
			</DialogTitle>
			<DialogContent>
				{fetchKilns?.isFetching ? (
					<Stack
						minHeight={200}
						alignItems={'center'}
						justifyContent={'center'}>
						<CircularProgress />
					</Stack>
				) : (
					<form onSubmit={handleSubmit(onSubmit)}>
						<Stack pt={2} rowGap={3}>
							<Grid container columnSpacing={2} rowSpacing={4}>
								{newData
									.filter(([key]) => fieldsToShow?.includes(key))
									.map(([key, value, unit]) => {
										const isAutocomplete = [
											'artisanProId',
											'farmerId',
											'vehicleId',
											'biomassTypeId',
											'fpuId',
											'fuelType',
											'categoryId',
										].includes(key)
										const isReadOnlyField = [
											'localId',
											'kilnId',
											'siteId',
										].includes(key)
										const nonChangableField = ['siteId', 'kilnId'].includes(key)

										// TODO : CHECK for Errors
										return isAutocomplete ? (
											<Grid item xs={6} key={key}>
												<Controller
													// name={`${key}.value` as keyof TBiocharProductionForm}
													name={key}
													control={control}
													render={({ field, fieldState }) => {
														return (
															<Stack spacing={1}>
																<Autocomplete
																	{...field}
																	sx={{
																		textTransform: 'capitalize',
																	}}
																	fullWidth
																	readOnly={nonChangableField}
																	disabled={
																		getButtonLabel() === StatusType.PROCESSED
																	}
																	onChange={(_, newValue) => {
																		setValue(
																			`${key}.value` as keyof TBiocharProductionForm,
																			newValue?.value ?? '',
																			{
																				shouldDirty: true,
																			}
																		)
																		setValue(
																			`${key}.label` as keyof TBiocharProductionForm,
																			newValue?.label ?? '',
																			{
																				shouldDirty: true,
																				shouldValidate: true,
																			}
																		)
																	}}
																	options={value?.listName || []}
																	isOptionEqualToValue={(option, value) =>
																		option.value === value.value
																	}
																	renderOption={(props, option) => {
																		return (
																			<li {...props}>
																				<Stack>
																					<Typography
																						variant='body1'
																						sx={{
																							textTransform: 'capitalize',
																						}}>
																						{option?.label}
																					</Typography>
																					{option.value ? (
																						<Typography variant='subtitle1'>
																							({option?.label})
																						</Typography>
																					) : null}
																				</Stack>
																			</li>
																		)
																	}}
																	renderInput={(params) => (
																		<TextField
																			{...params}
																			label={key}
																			fullWidth
																		/>
																	)}
																/>
																{fieldState.error && (
																	<FormHelperText error>
																		{fieldState.error.message}
																	</FormHelperText>
																)}
															</Stack>
														)
													}}
												/>
											</Grid>
										) : (
											<Grid item xs={6} key={key}>
												{isReadOnlyField ? (
													<TextField
														key={key}
														label={key}
														value={value}
														disabled={getButtonLabel() === StatusType.PROCESSED}
														sx={{ textTransform: 'capitalize' }}
														inputProps={{ readOnly: true }}
														fullWidth
													/>
												) : (
													<Controller
														name={key}
														control={control}
														render={({ field, fieldState }) => (
															<Stack spacing={1}>
																<TextField
																	disabled={
																		getButtonLabel() === StatusType.PROCESSED
																	}
																	{...field}
																	label={key}
																	sx={{
																		textTransform: 'capitalize',
																	}}
																	fullWidth
																	error={!!fieldState.error}
																	{...(unit
																		? {
																				InputProps: {
																					endAdornment: (
																						<InputAdornment position='end'>
																							<Typography>{unit}</Typography>
																						</InputAdornment>
																					),
																				},
																		  }
																		: {})}
																/>
																{fieldState.error && (
																	<FormHelperText error>
																		{fieldState.error.message}
																	</FormHelperText>
																)}
															</Stack>
														)}
													/>
												)}
											</Grid>
										)
									})}
							</Grid>
							{actionType === ActionTypes.ADD_FPU && (
								<GoogleMapsWithNonDraggableMarker
									center={mapCenter}
									dragEnabled={getButtonLabel() !== StatusType.PROCESSED}
									setMapCenter={updateLocationFields}
									mapContainerStyle={{
										width: '100%',
										height: 400,
										position: 'relative',
									}}
								/>
							)}
							{watch('moistureContentValue') &&
							watch('moistureContentValue')?.length > 0 ? (
								<Stack gap={2}>
									<Typography variant='body2' gutterBottom>
										Moisture Content:
									</Typography>
									<Stack
										flexDirection={'row'}
										justifyContent='flex-start'
										gap={2}>
										{fields.map((_, index) => (
											<Controller
												key={index}
												name={`moistureContentValue.${index}`}
												control={control}
												render={({ field, fieldState }) => (
													<Stack
														spacing={1}
														width={{ xs: 64, sm: 84 }}
														flexDirection={'column'}>
														<StyledTextField
															{...field}
															value={field.value || ''}
															type='number'
															onChange={(e) => {
																setValue(
																	`moistureContentValue.${index}`,
																	Number(e.target.value),
																	{
																		shouldDirty: true,
																		shouldValidate: true,
																	}
																)
															}}
															disabled={
																getButtonLabel() === StatusType.PROCESSED
															}
															InputProps={{
																endAdornment: <Typography>%</Typography>,
															}}
															inputProps={{
																style: { textAlign: 'center' },
															}}
														/>
														{fieldState.error && (
															<FormHelperText error>
																{fieldState.error.message}
															</FormHelperText>
														)}
													</Stack>
												)}
											/>
										))}
									</Stack>
								</Stack>
							) : null}
							{watch('measuringContainer') &&
							watch('measuringContainer')?.length > 0 ? (
								<Stack gap={2}>
									<Typography variant='body2' gutterBottom>
										Measuring Container:
									</Typography>
									<Stack gap={4} px={2}>
										{measuringField.map((item, index) => (
											<Grid container spacing={2}>
												<Grid item xs={6} spacing={1} key={index}>
													<Controller
														name={`measuringContainer.${index}.measuringContainerId`}
														control={control}
														render={({ field, fieldState }) => (
															<Stack spacing={1}>
																<Autocomplete
																	{...field}
																	disabled={
																		getButtonLabel() === StatusType.PROCESSED
																	}
																	value={
																		fetchContainers?.data?.find(
																			(option) => option.value === field.value
																		) ?? null
																	}
																	fullWidth
																	onChange={(_, newValue) => {
																		setValue(
																			`measuringContainer.${index}.measuringContainerId` as keyof TBiocharProductionForm,
																			newValue?.value ?? ''
																		)
																		setValue(
																			`measuringContainer?.${index}?.measuringContainerName` as keyof TBiocharProductionForm,
																			newValue?.value ?? ''
																		)
																	}}
																	options={fetchContainers?.data || []}
																	renderInput={(params) => (
																		<TextField
																			{...params}
																			label={'Measuring Container'}
																			fullWidth
																		/>
																	)}
																/>
																{fieldState.error && (
																	<FormHelperText error>
																		{fieldState.error.message}
																	</FormHelperText>
																)}
															</Stack>
														)}
													/>
													<Typography variant='subtitle2' mt={1}>
														{item?.isFullyFilled
															? 'Fully Filled'
															: 'Partial Filled'}
													</Typography>
												</Grid>
												<Grid item xs={6}>
													{item?.isFullyFilled ? (
														<Controller
															name={`measuringContainer.${index}.count`}
															control={control}
															render={({ field, fieldState }) => (
																<Stack spacing={1}>
																	<TextField
																		{...field}
																		disabled={
																			getButtonLabel() === StatusType.PROCESSED
																		}
																		label='Count'
																		fullWidth
																		type='number'
																		error={!!fieldState.error}
																	/>
																	{fieldState.error && (
																		<FormHelperText error>
																			{fieldState.error.message}
																		</FormHelperText>
																	)}
																</Stack>
															)}
														/>
													) : (
														<Controller
															name={`measuringContainer.${index}.height`}
															control={control}
															render={({ field, fieldState }) => (
																<Stack spacing={1}>
																	<TextField
																		{...field}
																		disabled={
																			getButtonLabel() === StatusType.PROCESSED
																		}
																		label='Height'
																		fullWidth
																		type='number'
																		error={!!fieldState.error}
																		{...(item?.isHeightInMM
																			? {
																					InputProps: {
																						endAdornment: (
																							<InputAdornment position='end'>
																								<Typography>
																									{item?.isHeightInMM
																										? 'mm'
																										: ''}
																								</Typography>
																							</InputAdornment>
																						),
																					},
																			  }
																			: {})}
																	/>
																	{fieldState.error && (
																		<FormHelperText error>
																			{fieldState.error.message}
																		</FormHelperText>
																	)}
																</Stack>
															)}
														/>
													)}
												</Grid>
											</Grid>
										))}
									</Stack>
								</Stack>
							) : null}
							{data?.error ? (
								<Stack pl={2}>
									<Typography variant='body2'>Errors: </Typography>
									<Box component='ul' sx={{ listStyleType: 'disc', pl: 2 }}>
										{Array.isArray(data?.error) ? (
											data.error.map((errorMessage: string) => (
												<Box component='li' key={errorMessage}>
													{errorMessage}
												</Box>
											))
										) : (
											<Box component='li'>{data.error}</Box>
										)}
									</Box>
								</Stack>
							) : null}
							{getButtonLabel() !== StatusType.PROCESSED && (
								<DialogActions sx={{ justifyContent: 'space-between' }}>
									<Button
										variant='outlined'
										color='primary'
										fullWidth
										onClick={onClose}>
										Cancel
									</Button>
									<Button
										type='submit'
										fullWidth
										variant='contained'
										color='primary'>
										{statusBody(getButtonLabel())?.name}
									</Button>
								</DialogActions>
							)}
						</Stack>
					</form>
				)}
			</DialogContent>
		</Dialog>
	)
}

const StyledTextField = styled(TextField)(() => ({
	'& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button': {
		display: 'none',
	},
	'& input[type=number]': {
		MozAppearance: 'textfield',
	},
}))
