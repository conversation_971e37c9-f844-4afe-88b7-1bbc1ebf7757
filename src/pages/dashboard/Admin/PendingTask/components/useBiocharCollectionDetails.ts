import { useSearchParams } from 'react-router-dom'
import { authAxios } from '@/contexts'
import {
	GetFarmerListResponse,
	GetPendingTaskResponse,
	IBiomassData,
	IContainerDetails,
	ISiteList,
	KilnListResponse,
	StatusType,
	TArtisanProsResponse,
	TBiocharActionData,
	TBiocharProductionForm,
	TBiocharProductionResponse,
	Vehicle,
} from '@/interfaces'
import { ICrop, Nullable } from '@/types'
import { useMutation, useQuery, UseQueryResult } from '@tanstack/react-query'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useFieldArray, useForm, useWatch } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { toast } from 'react-toastify'
import { actionSchema } from '../schema'
import { ActionTypes, AppType, fuelTypes } from '@/utils/constant'
import { AxiosError } from 'axios'

type ViewPendingTaskDetailsHookProps = {
	data: TBiocharActionData
	actionId: string
	getPendingTasks: UseQueryResult<GetPendingTaskResponse, Error>
	actionType: ActionTypes
	onClose: () => void
	appType: AppType
}

export const useBiocharCollectionDetails = ({
	data,
	actionId,
	getPendingTasks,
	actionType,
	onClose,
	appType,
}: ViewPendingTaskDetailsHookProps) => {
	const [selectedArtisanProId, setSelectedArtisanProId] = useState({
		value: data?.artisanProId,
		label: '',
	})
	const fetchArtisanPros = useQuery({
		queryKey: ['fetchArtisanPros'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})

			return authAxios.get<TArtisanProsResponse>(
				`/new/artisan-pros?${queryParams.toString()}`
			)
		},
		select: ({ data }) => {
			return (data?.artisanPros ?? [])?.map((artisanPro) => ({
				value: artisanPro.id,
				label: artisanPro.name,
			}))
		},
		enabled: Boolean(data?.artisanProId),
	})

	const fetchFarmerId = useQuery({
		queryKey: ['fetchFarmerId'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '10000000',
				page: '0',
			})

			return authAxios.get<GetFarmerListResponse>(
				`/artisian-pro/${data?.artisanProId}/farmers?${queryParams.toString()}`
			)
		},
		select: ({ data }) => {
			return (data?.farmers ?? [])?.map((farmer) => ({
				value: farmer.id,
				label: farmer.name,
			}))
		},
		enabled: Boolean(data?.artisanProId),
	})

	const fetchSites = useQuery({
		queryKey: ['fetchSites', selectedArtisanProId?.value],
		queryFn: async () => {
			const resp = await authAxios.get<{
				count: number
				siteList: ISiteList[]
			}>(`/artisian-pro/${selectedArtisanProId.value}/site?limit=10000&page=0`)
			return resp.data
		},
		select: (data) => {
			return (data?.siteList ?? [])?.map((site) => ({
				value: site.id,
				label: site.name,
			}))
		},
		enabled: Boolean(selectedArtisanProId?.value),
	})

	const fetchFpus = useQuery({
		queryKey: ['fetchFpus'],
		queryFn: async () => {
			const resp = await authAxios.get<IBiomassData>(
				`/artisian-pro/${data?.artisanProId}/site/${data?.siteId}/fpu?limit=1000&page=0`
			)
			return resp.data
		},
		select: (data) => {
			return (data?.fpu ?? [])?.map((fpuItem) => ({
				value: fpuItem.id,
				label: fpuItem.name,
			}))
		},
		enabled: Boolean(data?.artisanProId),
	})

	const fetchKilns = useQuery({
		queryKey: ['fetchKilns'],
		queryFn: async () => {
			const URI =
				appType === AppType.ArtisanProApp
					? `/artisian-pro/${data?.artisanProId}/site/${data?.siteId}/kiln?limit=10&page=0&sendAllKilns=false`
					: `/cs-network/${data?.csinkNetworkId}/kilns?limit=1000&page=0`
			const resp = await authAxios.get<KilnListResponse>(URI)
			return resp.data
		},
		select: (data) => {
			return (data?.kilns ?? [])?.map((kiln) => ({
				value: kiln.id,
				label: kiln.name,
			}))
		},
		enabled: Boolean(
			data?.csinkNetworkId || data?.artisanProId || data?.siteId
		),
	})

	const fetchBiomassType = useQuery({
		queryKey: ['getBiomassTypes'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '10000000',
				page: '0',
			})

			return authAxios.get<{ count: number; crops: ICrop[] }>(
				`/crops?${queryParams.toString()}`
			)
		},
		select: ({ data }) => {
			return (data?.crops ?? [])?.map((crop) => ({
				value: crop.id,
				label: crop.name,
			}))
		},
	})

	const fetchContainers = useQuery({
		queryKey: ['fetchContainers'],
		queryFn: async () => {
			const URI =
				appType === AppType.ArtisanProApp
					? `/artisian-pro/${data?.artisanProId}/site/${data?.siteId}/measuring-container?limit=1000&page=0`
					: `/cs-network/${data?.csinkNetworkId}/container?limit=1000&page=0`
			const resp = await authAxios.get<{
				containers: IContainerDetails[]
				count: number
				limit: number
				page: number
			}>(URI)
			return resp.data
		},
		select: (data) => {
			return (data?.containers ?? [])?.map((container) => ({
				value: container.id,
				label: container.name,
			}))
		},
		enabled: Boolean(
			data?.artisanProId || data?.siteId || data?.csinkNetworkId
		),
	})
	const fetchVehcile = useQuery({
		queryKey: ['fetchVehcile'],
		queryFn: async () => {
			const resp = await authAxios.get<{
				count: number
				vehicles: Vehicle[]
			}>(
				`/artisian-pro/${data?.artisanProId}/site/${data?.siteId}/vehicle?limit=10000&page=0`
			)
			return resp.data
		},
		select: (data) => {
			return (data?.vehicles ?? [])?.map((vehicle) => ({
				value: vehicle.id,
				label: vehicle.name,
			}))
		},
		enabled: Boolean(data?.artisanProId),
	})
	const fetchCategories = useQuery({
		queryKey: ['fetchCategories'],
		queryFn: async () => {
			const resp = await authAxios.get<{ ID: string; Name: string }[]>(
				`/vehicle-category`
			)
			return resp.data
		},
		select: (data) => {
			return (data || []).map((category) => ({
				value: category.ID,
				label: category.Name,
			}))
		},
		enabled: true,
	})

	const labels = useMemo(() => {
		const nestedData = data?.data
		const kilnIdKey = nestedData?.kilnId || nestedData?._kilnId
		const biomassTypeId = nestedData?.biomassTypeId
		return {
			categoryLabel:
				fetchCategories?.data?.find(
					(item) => item.value === nestedData?.categoryId
				)?.label ||
				nestedData?.categoryId ||
				'',
			siteLabel:
				fetchSites?.data?.find((item) => item.value === nestedData?.siteId)
					?.label || nestedData?.siteId,
			fpuLabel:
				fetchSites?.data?.find((item) => item.value === nestedData?.fpuId)
					?.label || nestedData?.fpuId,
			artisanProId:
				fetchArtisanPros?.data?.find(
					(item) => item.value === nestedData?.artisanProId
				)?.label || nestedData?.artisanProId,
			farmerLabel:
				fetchFarmerId?.data?.find((item) => item.value === nestedData?.farmerId)
					?.label || nestedData?.farmerId,
			kilnIdLabel:
				fetchKilns?.data?.find((item) => item.value === kilnIdKey)?.label ||
				kilnIdKey,
			biomassTypeLabel:
				fetchBiomassType?.data?.find((i) => i?.value === biomassTypeId)
					?.label || nestedData?.biomassTypeId,
			temperatureUnit: nestedData?.temperatureUnit,
			// TODO: use in future
			biomassQuantityUnit: nestedData?.biomassQuantityUnit,
		}
	}, [
		data?.data,
		fetchCategories?.data,
		fetchSites?.data,
		fetchArtisanPros?.data,
		fetchFarmerId?.data,
		fetchKilns?.data,
		fetchBiomassType?.data,
	])
	const fuelData = Object.values(fuelTypes).map((type) => ({
		value: type,
		label: type
			.replace(/_/g, ' ')
			.replace(/\b\w/g, (char) => char.toUpperCase()),
	}))

	const handleDefaultValues = useCallback(
		({
			actionType,
			data,
		}: {
			actionType: ActionTypes
			data: Nullable<TBiocharProductionResponse>
		}) => {
			// todo: update in future
			const commonValues = {
				...(data?.ImageIds
					? {
							imageIds: data?.ImageIds || '',
					  }
					: {}),
				// imageIds: data?.ImageIds || [],
				...(data?.siteId
					? {
							siteId: data?.siteId || '',
					  }
					: {}),
				...(data?.kilnId
					? {
							kilnId: data?.kilnId || '',
					  }
					: {}),
			}

			const moistureContentValue = data?.moistureContentValue
				? {
						moistureContentValue: Array.isArray(data?.moistureContentValue)
							? data?.moistureContentValue
							: [data?.moistureContentValue],
				  }
				: {}

			switch (actionType) {
				case ActionTypes.BIOMASS_COLLECTION:
					return {
						biomassTypeId: {
							value: data?._biomassType?._value || null,
							label: data?._biomassType?._label || '',
						},
						biomassQuantityUnit: data?.biomassQuantityUnit || '',
						biomassQuantity: data?.biomassQuantity || '',
						...(data?._isFarmer
							? {
									farmerId: {
										value: data?.farmerId || '',
										label: labels.farmerLabel,
									},
							  }
							: {
									fpuId: {
										value: data?.fpuId || '',
										label: labels.fpuLabel,
									},
							  }),
						vehicleId: {
							value: data?._vehicle?._value || null,
							label: data?._vehicle?._label || '',
						},
						fuelType: data?._vehicle?._fuelType || '',
						...commonValues,
					}
				case ActionTypes.BIOMASS_COLLECTION_CSINK:
					return {
						farmerId: {
							value: data?.farmerId || '',
							label: labels.farmerLabel,
						},
						biomassTypeId: {
							value: data?.biomassTypeId || null,
							label: labels.biomassTypeLabel,
						},
						biomassQuantityUnit: data?.biomassQuantityUnit || '',
						biomassQuantity: data?.biomassQuantity || '',
						vehicleId: {
							value: data?._vehicle?._value || null,
							label: data?._vehicle?._label || '',
						},
						biomassTransportationVehicleType:
							data?.biomassTransportationVehicleType || '',
						...commonValues,
					}
				case ActionTypes.BATCH_CREATION:
					return {
						biomassTypeId: {
							value: data?.biomassTypeId || null,
							label: labels.biomassTypeLabel,
						},
						biomassQuantityUnit: data?.biomassQuantityUnit || '',
						biomassQuantity: data?.biomassQuantity || '',
						...moistureContentValue,
						...commonValues,
					}
				case ActionTypes.BATCH_CREATION_CSINK:
					return {
						biomassTypeId: {
							value: data?.biomassTypeId || null,
							label: labels.biomassTypeLabel,
						},
						biomassQuantityUnit: data?.biomassQuantityUnit || '',
						biomassQuantity: data?.biomassQuantity || '',
						...moistureContentValue,
						...commonValues,
					}
				case ActionTypes.ADD_TEMPERATURE:
					return {
						biomassTypeId: {
							value: data?.biomassTypeId || null,
							label: labels.biomassTypeLabel,
						},

						biomassQuantity: data?.biomassQuantity || '',
						temperature: data?.temperature || 0,
						temperatureUnit: data?.temperatureUnit || '',
						...moistureContentValue,
						...commonValues,
					}

				case ActionTypes.ADD_BIOCHAR:
					return {
						biomassTypeId: {
							value: data?.biomassTypeId || null,
							label: labels.biomassTypeLabel,
						},
						biomassQuantity: data?.biomassQuantity || '',
						measuringContainer: data?.measuringContainer || [],
						...moistureContentValue,
						...commonValues,
					}
				case ActionTypes.ADD_BIOCHAR_CSINK:
					return {
						biomassTypeId: {
							value: data?.biomassTypeId || null,
							label: labels.biomassTypeLabel,
						},

						biomassQuantity: data?.biomassQuantity || '',
						measuringContainer: data?.measuringContainer || [],
						...moistureContentValue,
						...commonValues,
					}

				case ActionTypes.ADD_FPU:
					return {
						localId: data?.localId || '',
						name: data?.name || '',
						artisanProId: {
							value: data?.artisanProId || '',
							label: labels.artisanProId,
						},
						address: data?.address || '',
						latitude: data?.latitude || 0,
						longitude: data?.longitude || 0,
						...commonValues,
					}
				case ActionTypes.ADD_VEHICLE:
					return {
						categoryId: {
							value: data?.categoryId || '',
							label: labels.categoryLabel,
						},
						localId: data?.localId,
						imageIds: [],
						name: data?.name,
						number: data?.number || 0,
						fuelType: {
							value: data?.fuelType,
							label:
								fuelData.find((option) => option.value === data?.fuelType)
									?.label || 'Unknown Fuel Type',
						},
						...commonValues,
					}
				default:
					return {
						...commonValues,
					}
			}
		},
		[
			fuelData,
			labels.artisanProId,
			labels.biomassTypeLabel,
			labels.categoryLabel,
			labels.farmerLabel,
			labels.fpuLabel,
		]
	)

	const [searchParams] = useSearchParams()
	const [mapCenter, setMapCenter] = useState({
		lat: data?.data?.latitude ? Number(data.data.latitude) : 0,
		lng: data?.data?.longitude ? Number(data.data.longitude) : 0,
	})

	const { control, handleSubmit, setValue, formState, register, watch, reset } =
		useForm<TBiocharProductionForm>({
			defaultValues: handleDefaultValues({ actionType, data: data?.data }),
			mode: 'all',
			resolver: yupResolver<any>(actionSchema(actionType)),
		})

	useEffect(() => {
		//For First Time updating the fields
		if (!fetchSites?.isPending) {
			reset(handleDefaultValues({ actionType, data: data?.data }))
		}
	}, [fetchSites?.isPending])

	const selectedArtisanProChange = watch('artisanProId')
	useEffect(() => {
		if (!selectedArtisanProChange) return
		setSelectedArtisanProId(selectedArtisanProChange)
	}, [selectedArtisanProChange])

	const latitude = useWatch({ name: 'latitude', control }) || 0
	const longitude = useWatch({ name: 'longitude', control }) || 0

	useEffect(() => {
		setMapCenter({ lat: Number(latitude), lng: Number(longitude) })
	}, [latitude, longitude])

	const updateLocationFields = (lat: number, lng: number) => {
		setValue('latitude', lat, { shouldDirty: true, shouldValidate: true })
		setValue('longitude', lng, { shouldDirty: true, shouldValidate: true })
	}

	const { fields } = useFieldArray({
		control,
		name: 'moistureContentValue',
	})
	const { fields: measuringField } = useFieldArray({
		control,
		name: 'measuringContainer',
	})

	// Not to Take these keys
	const filteredData = useMemo(
		() =>
			Object.entries(data?.data as TBiocharProductionResponse)?.filter(
				([key]) =>
					![
						'imageIds',
						'videoIds',
						'imageIDs',
						'moistureContentValue',
					].includes(key)
			),
		[data]
	)

	const newData = useMemo(
		() =>
			filteredData?.flatMap(([key, value]) => {
				switch (key) {
					case 'fuelType':
						return [
							[
								key,
								{
									value: value,
									label: value,
									listName: fuelData,
								},
							],
						]

					case 'categoryId':
						return [
							[
								key,
								{
									value: value,
									label: value,
									listName: fetchCategories?.data,
								},
							],
						]

					case 'biomassTypeId':
						return [
							[
								key,
								{
									value: value,
									label: value,
									listName: fetchBiomassType?.data,
								},
							],
						]
					case 'artisanProId':
						return [
							[
								key,
								{
									value: value,
									label: value,
									listName: fetchArtisanPros?.data,
								},
							],
						]
					case 'siteId':
						return [[key, labels?.siteLabel]]
					case 'kilnId':
						return [[key, labels?.kilnIdLabel]]
					case 'farmerId':
						return [
							[
								key,
								{
									value: value,
									label: value,
									listName: fetchFarmerId?.data,
								},
							],
						]
					case 'fpuId':
						return [
							[
								key,
								{
									value: value,
									label: value,
									listName: fetchFpus?.data,
								},
							],
						]
					case 'vehicleId':
						return [
							[
								key,
								{
									value: value,
									label:
										fetchVehcile?.data?.find((type) => type.value === value)
											?.label || value,
									listName: fetchVehcile?.data,
								},
							],
						]
					case '_vehicle':
						return [
							[key, value],
							['fuelType', value?._fuelType],
						]
					case 'temperature':
						return [[key, value, labels?.temperatureUnit]]
					default:
						return [[key, value]]
				}
			}),
		[
			fetchArtisanPros?.data,
			fetchBiomassType?.data,
			fetchCategories?.data,
			fetchFarmerId?.data,
			fetchFpus?.data,
			fetchVehcile?.data,
			filteredData,
			fuelData,
			labels?.kilnIdLabel,
			labels?.siteLabel,
			labels?.temperatureUnit,
		]
	)

	const performBiocharAction = async ({
		formData,
		actionId,
		statusType,
		isDirty,
		siteId,
	}: {
		formData: TBiocharProductionForm
		actionId: string
		statusType?: string
		isDirty: boolean
		siteId?: string
	}) => {
		const payload = {
			...formData,
			siteId,
			artisanProId: formData?.artisanProId?.value,
			...(formData?.moistureContentValue?.length === 0
				? { moistureContentValue: undefined }
				: {}),
			...(formData?.measuringContainer?.length === 0
				? { measuringContainer: undefined }
				: {}),
		}

		let apiUrl = ''
		let apiData = {}

		if (statusType === 'error' && !isDirty) {
			apiUrl = `/new/dmrv-lite/action/${actionId}/retry-sync`
		} else if (statusType === 'created' && !isDirty) {
			apiUrl = `/new/dmrv-lite/action/${actionId}/sync`
		} else {
			apiUrl = `/new/dmrv-lite/action/${actionId}/`
			apiData = { data: JSON.stringify(payload), isApproved: false }
		}

		const { data: response } = await authAxios.put(apiUrl, apiData)
		return response
	}

	const mutation = useMutation({
		mutationFn: performBiocharAction,
		onSuccess: (response) => {
			onClose()
			toast(response?.message || 'Action successfully completed')
			getPendingTasks.refetch()
		},
		onError: (error: AxiosError<{ error: string }>) => {
			onClose()
			getPendingTasks.refetch()
			toast(error.response?.data?.error || 'Something went wrong')
		},
	})

	const onSubmit = (formData: TBiocharProductionForm) => {
		mutation.mutate({
			formData,
			actionId,
			statusType: data?.statusType,
			isDirty: formState.isDirty,
			siteId: data?.siteId,
		})
	}

	const getButtonLabel = () => {
		if (data?.statusType === StatusType.PROCESSED) return StatusType.PROCESSED
		if (data?.statusType === StatusType.ERROR && !formState.isDirty)
			return StatusType.ERROR
		if (data?.statusType === StatusType.CREATED && !formState.isDirty)
			return StatusType.CREATED
		return StatusType.PENDING
	}

	return {
		control,
		handleSubmit,
		setValue,
		formState,
		onSubmit,
		newData,
		searchParams,
		register,
		watch,
		fields,
		fetchSites,
		fetchKilns,
		fetchFarmerId,
		fetchBiomassType,
		fetchVehcile,
		measuringField,
		fetchContainers,
		mapCenter,
		setMapCenter,
		updateLocationFields,
		getButtonLabel,
	}
}
