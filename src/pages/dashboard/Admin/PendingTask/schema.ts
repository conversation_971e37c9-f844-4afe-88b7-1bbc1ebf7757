import { ActionTypes } from '@/utils/constant'
import * as yup from 'yup'

export const actionSchema = (actionType: string) => {
	const artisanMixingSchema = yup.object().shape({
		biocharQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your BiocharQuantity'),
		description: yup.string().optional(),
		imageIds: yup.array(yup.string().required()).required(),
		otherMixQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your OtherMixQuantity'),
		otherMixQuantityUnit: yup.string().required(),
		packagingTypeId: yup.string().required(),
		siteId: yup.string().optional(),
		waterQuantityAdded: yup
			.number()
			.required()
			.typeError('Please Enter Your WaterQuantity'),
		biocharOnlyBags: yup
			.array()
			.of(
				yup.object().shape({
					bagId: yup.string().required('Please select your bag'),
					bagName: yup.string().required('Please select your bag').optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your bag count'),
				})
			)
			.required()
			.optional(),
		videoIds: yup.array(yup.string().required()).required(),
		cropId: yup.string().optional(),
	})

	const mixingCsink = yup.object().shape({
		biocharQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your BiocharQuantity'),
		description: yup.string().optional(),
		imageIds: yup.array(yup.string().required()).required(),
		otherMixQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your OtherMixQuantity'),
		otherMixQuantityUnit: yup.string().required(),
		packagingTypeId: yup.string().required(),
		kilnId: yup.string().optional(),
		waterQuantityAdded: yup
			.number()
			.required()
			.typeError('Please Enter Your WaterQuantity'),
		biocharOnlyBags: yup
			.array()
			.of(
				yup.object().shape({
					bagId: yup.string().required('Please select your bag'),
					bagName: yup.string().required('Please select your bag').optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your bag count'),
				})
			)
			.required()
			.optional(),
		videoIds: yup.array(yup.string().required()).required(),
		cropId: yup.string().optional(),
	})

	const packagingArtisan = yup.object().shape({
		bags: yup
			.array()
			.of(
				yup.object().shape({
					bagId: yup.string().required('Please select your bag'),
					bagName: yup.string().required('Please select your bag').optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your bag count'),
				})
			)
			.required(),
		videoIds: yup.array(yup.string().required()).required(),
		packagingQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your PackagingQuantity'),
		cropId: yup.string().optional(),

		description: yup.string().optional(),
		imageIds: yup.array(yup.string().required()).required(),
		packagingTypeId: yup.string().required(),
		siteId: yup.string().optional(),
	})
	const packagingCsink = yup.object().shape({
		bags: yup
			.array()
			.of(
				yup.object().shape({
					bagId: yup.string().required('Please select your bag'),
					bagName: yup.string().required('Please select your bag').optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your bag count'),
				})
			)
			.required(),
		videoIds: yup.array(yup.string().required()).required(),
		packagingQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your PackagingQuantity'),
		cropId: yup.string().optional(),
		description: yup.string().optional(),
		imageIds: yup.array(yup.string().required()).required(),
		packagingTypeId: yup.string().required(),
		kilnId: yup.string().optional(),
	})
	const distributionOtherArtisan = yup.object().shape({
		buyerId: yup.string().optional(),
		description: yup.string().optional(),
		fuelValue: yup.number().required().typeError('Please Enter Your fuelValue'),
		imageIds: yup.array(yup.string().required()).required(),
		siteId: yup.string().optional(),
		openMixPackages: yup
			.array(
				yup
					.object()
					.shape({
						openMixQty: yup
							.number()
							.required()
							.typeError('Please Enter openMixQty'),
						packagingTypeId: yup.string().required().optional(),
					})
					.required()
			)
			.required(),
		packagingBags: yup
			.array()
			.of(
				yup.object().shape({
					bagId: yup.string().required('Please select your bag'),
					bagName: yup.string().required('Please select your bag').optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your bag count'),
				})
			)
			.required()
			.optional(),
		vehicleId: yup.string().optional(),
		VehicleType: yup.string().optional(),
		videoIds: yup.array(yup.string().required()).required(),
	})
	const distributionFarmerArtisan = yup.object().shape({
		description: yup.string().optional(),
		fuelValue: yup.number().required().typeError('Please Enter Your fuelValue'),
		imageIds: yup.array(yup.string().required()).required(),
		siteId: yup.string().optional(),
		openMixQty: yup.number().required().typeError('Please Enter openMixQty'),
		packagingTypeId: yup.string().required().optional(),
		distributionBags: yup
			.array()
			.of(
				yup.object().shape({
					bagId: yup.string().required('Please select your bag'),
					bagName: yup.string().required('Please select your bag').optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your bag count'),
				})
			)
			.required()
			.optional(),
		vehicleId: yup.string().optional(),
		VehicleType: yup.string().optional(),
		videoIds: yup.array(yup.string().required()).required(),
	})
	const distributionFarmerCsink = yup.object().shape({
		description: yup.string().optional(),
		fuelValue: yup.number().required().typeError('Please Enter Your fuelValue'),
		imageIds: yup.array(yup.string().required()).required(),
		kilnId: yup.string().optional(),
		openMixPackages: yup
			.array(
				yup
					.object()
					.shape({
						openMixQty: yup
							.number()
							.required()
							.typeError('Please Enter openMixQty'),
						packagingTypeId: yup.string().required().optional(),
					})
					.required()
			)
			.required(),
		distributionBags: yup
			.array()
			.of(
				yup.object().shape({
					bagId: yup.string().required('Please select your bag'),
					bagName: yup.string().required('Please select your bag').optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your bag count'),
				})
			)
			.required()
			.optional(),
		vehicleId: yup.string().optional(),
		VehicleType: yup.string().optional(),
		videoIds: yup.array(yup.string().required()).required(),
		farmerIds: yup.array(yup.string().required()).required(),
	})
	const biomassCollection = yup.object().shape({
		biomassQuantityUnit: yup.string().required(),
		biomassQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your biomassQuantity'),
		vehicleId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your vehicle')
			.optional(),
		biomassTypeId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your biomassType')
			.optional(),
		fuelType: yup.string().required('Please select your fuelType').optional(),
		imageIds: yup.array(yup.string().required()).required(),
		siteId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your site')
			.optional(),
		farmerId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your farmer')
			.optional(),
		fpuId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('please select your fpu')
			.optional(),
	})
	const biomassCollectionCsink = yup.object().shape({
		biomassTypeId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your biomassType')
			.optional(),
		biomassQuantityUnit: yup.string().required(),
		biomassQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your biomassQuantity'),
		biomassTransportationVehicleType: yup
			.string()
			.required('Please select your biomassTransportationVehicleType')
			.optional(),
		imageIds: yup.array(yup.string().required()).required(),
		kilnId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your kiln')
			.optional(),
		farmerId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your farmer')
			.optional(),
	})

	const batchCreation = yup.object().shape({
		biomassTypeId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your biomassType')
			.optional(),
		// biomassQuantityUnit: yup.string().required(),
		biomassQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your biomassQuantity'),
		imageIds: yup.array(yup.string().required()).required(),
		siteId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your site')
			.optional(),
		moistureContentValue: yup.array().notRequired(),
		// moistureContentValue: yup
		// 	.array()
		// 	.of(
		// 		yup
		// 			.number()
		// 			.required('Value is required')
		// 			.typeError('Must be a number')
		// 			.min(0.01, 'Must be greater than 0')
		// 			.max(9.99, 'Max Limit reach')
		// 	)
		// 	.required('Invalid Input'),
	})
	const batchCreationCsink = yup.object().shape({
		biomassTypeId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your biomassType')
			.optional(),
		// biomassQuantityUnit: yup.string().required(),
		biomassQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your biomassQuantity'),
		imageIds: yup.array(yup.string().required()).required(),
		siteId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your site')
			.optional(),
		kilnId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your kiln')
			.optional(),
		// moistureContentValue: yup
		// 	.array()
		// 	.of(
		// 		yup
		// 			.number()
		// 			.required('Value is required')
		// 			.typeError('Must be a number')
		// 			.min(0.01, 'Must be greater than 0')
		// 			.max(9.99, 'Max Limit reach')
		// 	)
		// 	.required('Invalid Input'),
		moistureContentValue: yup.array().notRequired(),
	})

	const addTemperature = yup.object().shape({
		biomassTypeId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your biomassType')
			.optional(),
		// biomassQuantityUnit: yup.string().required(),
		biomassQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your biomassQuantity'),
		imageIds: yup.array(yup.string().required()).required(),
		kilnId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your kiln')
			.optional(),
		// moistureContentValue: yup
		// 	.array()
		// 	.of(
		// 		yup
		// 			.number()
		// 			.required('Value is required')
		// 			.typeError('Must be a number')
		// 			.min(0.01, 'Must be greater than 0')
		// 			.max(9.99, 'Max Limit reach')
		// 	)
		// 	.required('Invalid Input'),
		moistureContentValue: yup.array().notRequired(),
		temperatureUnit: yup.string().required().optional(),
		temperature: yup
			.number()
			.required()
			.typeError('Please Enter Your temperature'),
	})

	const addBiochar = yup.object().shape({
		biomassTypeId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your biomassType')
			.optional(),
		biomassQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your biomassQuantity'),
		imageIds: yup.array(yup.string().required()).required(),
		siteId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your site')
			.optional(),
		// moistureContentValue: yup
		// 	.array()
		// 	.of(
		// 		yup
		// 			.number()
		// 			.required('Value is required')
		// 			.typeError('Must be a number')
		// 			.min(0.01, 'Must be greater than 0')
		// 			.max(9.99, 'Max Limit reach')
		// 	)
		// 	.required('Invalid Input'),
		moistureContentValue: yup.array().notRequired(),
		measuringContainer: yup
			.array()
			.of(
				yup.object().shape({
					measuringContainerId: yup
						.string()
						.required('Please select your measuringContainer'),
					measuringContainerName: yup
						.string()
						.required('Please select your measuringContainer')
						.optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your Contianer count'),
					isFullyFilled: yup.boolean().required(),
					height: yup
						.number()
						.required()
						.typeError('Please enter your container height'),
				})
			)
			.required(),
	})

	const addBiocharCsink = yup.object().shape({
		biomassTypeId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your biomassType')
			.optional(),
		biomassQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your biomassQuantity'),
		imageIds: yup.array(yup.string().required()).required(),
		kilnId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your kiln')
			.optional(),
		// moistureContentValue: yup
		// 	.array()
		// 	.of(
		// 		yup
		// 			.number()
		// 			.required('Value is required')
		// 			.typeError('Must be a number')
		// 			.min(0.01, 'Must be greater than 0')
		// 			.max(9.99, 'Max Limit reach')
		// 	)
		// 	.required('Invalid Input'),
		moistureContentValue: yup.array().notRequired(),

		measuringContainer: yup
			.array()
			.of(
				yup.object().shape({
					measuringContainerId: yup
						.string()
						.required('Please select your measuringContainer'),
					measuringContainerName: yup
						.string()
						.required('Please select your measuringContainer')
						.optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your Container count'),
					isFullyFilled: yup.boolean().required(),
					height: yup
						.number()
						.required()
						.typeError('Please enter your container height'),
				})
			)
			.required()
			.optional(),
	})

	const addFpu = yup.object().shape({
		name: yup.string().required(),
		latitude: yup.number().required(),
		longitude: yup.number().required(),
		artisanProId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your artisanProId')
			.optional(),
		siteId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your Site')
			.optional(),
	})

	const addVehicle = yup.object().shape({
		imageIds: yup.array(yup.string().required()).required(),
		siteId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your site')
			.optional(),
		categoryId: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your site')
			.optional(),

		localId: yup.string().required('Please enter localId'),
		name: yup.string().required('Please enter name'),
		number: yup.string().required('Please enter number'),
		fuelType: yup
			.object()
			.shape({
				value: yup.string().optional(),
				label: yup.string().optional(),
			})
			.required('Please select your site')
			.optional(),
	})
	const addSiteApplicationArtisan = yup.object().shape({
		siteId: yup.string().required('Please select your site'),
		fpuId: yup.string().required('Please select your Fpu'),
		mixTypeId: yup.string().required('Please select your mixTypeId'),
		mixQuantity: yup.number().required('Please select your mixQuantity'),
	})

	const defaultSchema = yup.object().shape({
		biocharQuantity: yup
			.number()
			.required()
			.typeError('Please Enter Your BiocharQuantity'),
		description: yup.string().optional(),
		imageIds: yup.array(yup.string().required()).required(),
		siteId: yup.string().optional(),
		biocharOnlyBags: yup
			.array()
			.of(
				yup.object().shape({
					bagId: yup.string().required('Please select your bag'),
					bagName: yup.string().required('Please select your bag').optional(),
					count: yup
						.number()
						.required()
						.typeError('Please Enter Your bag count'),
				})
			)
			.required()
			.optional(),
		videoIds: yup.array(yup.string().required()).required(),
		cropId: yup.string().optional(),
	})

	switch (actionType) {
		case ActionTypes.ADD_PACKAGING_ARTISAN:
			return packagingArtisan
		case ActionTypes.ADD_MIXING_ARTISAN:
			return artisanMixingSchema
		case ActionTypes.ADD_DISTRIBUTION_OTHER_ARTISAN:
			return distributionOtherArtisan
		case ActionTypes.ADD_DISTRIBUTION_FARMER_ARTISAN:
			return distributionFarmerArtisan
		case ActionTypes.ADD_PACKAGING_CSINK:
			return packagingCsink
		case ActionTypes.ADD_MIXING_CSINK:
			return mixingCsink
		case ActionTypes.ADD_DISTRIBUTION_OTHER_CSINK:
			return defaultSchema
		case ActionTypes.ADD_DISTRIBUTION_FARMER_CSINK:
			return distributionFarmerCsink
		case ActionTypes.BIOMASS_COLLECTION:
			return biomassCollection
		case ActionTypes.BIOMASS_COLLECTION_CSINK:
			return biomassCollectionCsink
		case ActionTypes.BATCH_CREATION:
			return batchCreation
		case ActionTypes.BATCH_CREATION_CSINK:
			return batchCreationCsink
		case ActionTypes.ADD_TEMPERATURE:
			return addTemperature
		case ActionTypes.ADD_BIOCHAR:
			return addBiochar
		case ActionTypes.ADD_BIOCHAR_CSINK:
			return addBiocharCsink
		case ActionTypes.ADD_FPU:
			return addFpu
		case ActionTypes.ADD_VEHICLE:
			return addVehicle
		case ActionTypes.ADD_SITE_APPLICATION_ARTISAN:
			return addSiteApplicationArtisan
		default:
			return defaultSchema
	}
}
