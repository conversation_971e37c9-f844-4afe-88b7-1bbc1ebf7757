import { authAxios } from '@/contexts'
import { IProjectsResponse } from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import {
	QueryFunctionContext,
	useMutation,
	useQuery,
} from '@tanstack/react-query'
import { SyntheticEvent, useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

const getAllProjects = async ({ queryKey }: QueryFunctionContext) => {
	const [search, paramsLimit, paramsPage] = queryKey.slice(1)
	const { data } = await authAxios.get<IProjectsResponse>(
		`/projects?limit=${paramsLimit}&page=${paramsPage}&search=${search}&all=false`
	)
	return data
}

export const useProjects = () => {
	const [searchParams] = useSearchParams()
	const navigate = useNavigate()
	const search = searchParams.get('search') ?? ''

	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsPage = searchParams.get('page') || defaultPage
	const projectListQuery = useQuery({
		queryKey: ['allProjects', search, paramsLimit, paramsPage],
		queryFn: getAllProjects,
		enabled: true,
	})

	const handleProjectRowClick = (params: any) => {
		navigate({
			pathname: `${params.row?.id}/details`,
		})
	}

	const handleAddProject = () => {
		navigate({
			pathname: 'create',
		})
	}

	const handleEditProject = useCallback(
		(e: SyntheticEvent, id: string) => {
			e.stopPropagation()
			navigate({
				pathname: `${id}/edit`,
			})
		},
		[navigate]
	)

	const publishProject = useMutation({
		mutationKey: ['publishProject'],
		mutationFn: async (id: string) =>
			await authAxios.put(`/projects/${id}/publish`),
		onSuccess: () => {
			toast('Project published successfully')
			projectListQuery.refetch()
		},
		onError: (err: any) => {
			toast(err?.response?.data?.messageToUser)
		},
	})

	const unPublishProject = useMutation({
		mutationKey: ['unPublishProject'],
		mutationFn: async (id: string) =>
			await authAxios.put(`/projects/${id}/unpublish`),
		onSuccess: () => {
			toast('Project unPublished successfully')
			projectListQuery.refetch()
		},
		onError: (err: any) => {
			toast(err?.response?.data?.messageToUser)
		},
	})

	const handlePublishUnPublishProject = useCallback(
		(id: string, status: string) =>
			status === 'draft'
				? publishProject.mutate(id)
				: unPublishProject.mutate(id),
		[publishProject, unPublishProject]
	)

	return {
		handleProjectRowClick,
		projectList: projectListQuery.data,
		handleAddProject,
		handleEditProject,
		handlePublishUnPublishProject,
		isLoading: projectListQuery?.isLoading,
	}
}
