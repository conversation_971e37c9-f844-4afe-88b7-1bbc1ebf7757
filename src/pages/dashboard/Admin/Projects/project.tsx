import archivedIcon from '@/assets/icons/archievd.svg'
import AddRoundedIcon from '@mui/icons-material/AddRounded'
import DoneIcon from '@mui/icons-material/Done'
import {
	Box,
	Button,
	Chip,
	FormControl,
	IconButton,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import {
	GridColDef,
	GridRenderCellParams,
	GridSearchIcon,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { useProjects } from './useProjects'
import { CustomTable } from '@/components/CustomTable'
import { CustomHeader, QueryInput } from '@/components'
import { Edit } from '@mui/icons-material'

const activeStatusList = ['active', 'active available', 'active sold out']

export const Projects = () => {
	const theme = useTheme()
	const {
		projectList,
		handleProjectRowClick,
		handleAddProject,
		handleEditProject,
		isLoading,
	} = useProjects()
	const DataColumns: GridColDef<GridValidRowModel>[] = [
		{
			field: 'name',
			headerName: 'Name',
			minWidth: 200,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							fontWeight: 700,
							color: theme.palette.neutral['900'],
						}}>
						{params.row?.name ?? ''}
					</Typography>
				)
			},
		},
		{
			field: 'creditsIssued',
			headerName: 'Credits',
			minWidth: 120,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{params.row?.creditsIssued ?? ''}
					</Typography>
				)
			},
		},
		// {
		// 	field: 'time',
		// 	headerName: 'Time',
		// 	minWidth: 120,
		// 	flex: 1,
		// 	headerAlign: 'center',
		// 	align: 'center',

		// 	renderCell: (params: GridRenderCellParams) => {
		// 		return (
		// 			<Typography
		// 				variant='subtitle1'
		// 				sx={{
		// 					color: theme.palette.neutral['900'],
		// 				}}>
		// 				{params.row.time ?? ''}
		// 			</Typography>
		// 		)
		// 	},
		// },
		{
			field: 'location',
			headerName: 'Location',
			minWidth: 280,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{params.row?.address ?? ''}
					</Typography>
				)
			},
		},

		{
			field: 'price_per_credit',
			headerName: 'Price per credit',
			minWidth: 120,
			flex: 1,

			renderCell: (params: GridRenderCellParams) => {
				return (
					<Typography
						variant='subtitle1'
						sx={{
							color: theme.palette.neutral['900'],
						}}>
						{params.row?.rate ?? ''}
					</Typography>
				)
			},
		},
		{
			field: 'status',
			headerName: 'Status',
			minWidth: 120,
			flex: 1,

			renderCell: (params: GridRenderCellParams) =>
				params.row?.projectStatus ? (
					<Chip
						label={params.row?.projectStatus ?? ''}
						onClick={() => console.log(params.row?.projectStatus)}
						icon={
							activeStatusList.includes(params.row?.projectStatus ?? '') ? (
								<DoneIcon color='success' fontSize='small' />
							) : (
								<Box
									component='img'
									src={archivedIcon}
									height={10}
									width={18}
								/>
							)
						}
						size='small'
						variant='filled'
						sx={{
							fontSize: '12px',
							fontWeight: 600,
							background: activeStatusList.includes(
								params.row?.projectStatus ?? ''
							)
								? theme.palette.success.light
								: theme.palette.neutral['200'],
							color: activeStatusList.includes(params.row?.projectStatus ?? '')
								? theme.palette.success.main
								: theme.palette.neutral['500'],
						}}
					/>
				) : null,
		},
		{
			field: 'state',
			headerName: 'Publish/Draft',
			flex: 1,
			minWidth: 150,
			renderCell: (params) => (
				<Typography className='first_letter_capitalize'>
					{params?.row?.state}
				</Typography>
			),
		},
		{
			field: 'action',
			headerName: '',
			flex: 1,
			minWidth: 200,
			renderCell: (params) => (
				<Stack flexDirection='row' gap={2}>
					<IconButton onClick={(e) => handleEditProject(e, params?.row?.id)}>
						<Edit />
					</IconButton>
				</Stack>
			),
		},
	]

	const HeaderEndButtons = () => (
		<Stack direction='row' spacing={2}>
			<Button
				onClick={handleAddProject}
				variant='contained'
				size='small'
				startIcon={<AddRoundedIcon />}>
				Add Project
			</Button>
		</Stack>
	)

	return (
		<StyledContainer>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading='Projects'
					showButton={false}
					endComponent={<HeaderEndButtons />}
				/>
			</Box>
			<Stack className='container'>
				<Typography variant='h5'>All Projects</Typography>
				<CustomTable
					showPagination={true}
					rows={projectList?.projects ?? []}
					handleRowClick={handleProjectRowClick}
					columns={DataColumns}
					count={projectList?.count ?? 0}
					headerComponent={<GridHeaderComponents />}
					isLoading={isLoading}
				/>
			</Stack>
		</StyledContainer>
	)
}

const GridHeaderComponents = () => {
	return (
		<Stack className='grid-header-component' columnGap={2}>
			<QueryInput
				queryKey='search'
				label='Search'
				name='search'
				placeholder='Search'
				className='search-textFiled'
				setPageOnSearch
				InputProps={{
					startAdornment: <GridSearchIcon />,
				}}
			/>
			<FormControl className='form-controller'>
				<InputLabel id='tagLabel'>Tag</InputLabel>
				<Select labelId='tagLabel' label='Tag'>
					<MenuItem value='farmer'>Farmer</MenuItem>
				</Select>
			</FormControl>

			<FormControl className='form-controller'>
				<InputLabel id='statusLabel'>Status</InputLabel>
				<Select labelId='statusLabel' label='Status'>
					<MenuItem value={'Active'}>Active</MenuItem>
					<MenuItem value={'archived'}>Archived</MenuItem>
				</Select>
			</FormControl>
		</Stack>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(4, 3),
		gap: theme.spacing(2),
		'.grid-header-component': {
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				minWidth: 334,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(18),
				width: '100%',
				'.MuiOutlinedInput-notchedOutline': {
					borderRadius: theme.spacing(1.25),
				},
			},
		},
	},
}))
