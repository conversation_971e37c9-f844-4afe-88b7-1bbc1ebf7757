import { authAxios } from '@/contexts'
import { User } from '@/interfaces'
import { Close } from '@mui/icons-material'
import {
	Typography,
	Select,
	MenuItem,
	Button,
	Stack,
	InputLabel,
	FormControl,
	styled,
	IconButton,
	CircularProgress,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { CertificateFormData } from './certificateSchema'
import { toast } from 'react-toastify'

export default function GenerateCertificateDrawer({
	onClose,
	userData,
}: {
	onClose: () => void
	userData: User
}) {
	const queryClient = useQueryClient()

	const fetchCertificateData = async (): Promise<CertificateFormData> => {
		const response = await authAxios.get(
			`/certificate/request/v2/${userData?.csinkManagerId}`
		)
		return response.data
	}

	const { data: certificateData } = useQuery({
		queryKey: ['CertificateData', userData?.csinkManagerId],
		queryFn: () => fetchCertificateData(),
		enabled: !!userData?.csinkManagerId,
	})

	const [dropdownValue, setDropdownValue] = useState<number | null>(null)

	const { mutate: onGenerate, isPending } = useMutation({
		mutationFn: async () => {
			if (dropdownValue == null) {
				toast('No Signing Authority selected.')
				return
			}
			const selectedAuthority =
				certificateData?.signingAuthorityDetails?.[dropdownValue]
			if (!selectedAuthority) {
				throw new Error('No matching signing authority found')
			}
			const certificatePayload = {
				cSinkManagerId: certificateData?.cSinkManagerId,
				userId: userData.id,
				signingAuthorityName: selectedAuthority?.signingAuthorityName?.trim(),
				signingAuthorityPosition: selectedAuthority?.signingAuthorityPosition,
				signingAuthoritySignatureId:
					selectedAuthority.signingAuthoritySignature,
				accountType: userData.accountType,
			}
			const response = await authAxios.post(
				`/certificate/generate/v2`,
				certificatePayload,
				{
					responseType: 'blob',
				}
			)
			return response
		},
		onSuccess: (response) => {
			if (!response?.data) return
			const url = window.URL.createObjectURL(new Blob([response?.data]))
			const link = document.createElement('a')
			link.href = url
			link.setAttribute('download', `Certificate.pdf`)
			document.body.appendChild(link)
			link.click()
			toast('File Downloaded')
			queryClient.refetchQueries({ queryKey: ['users'] })
			onClose()
		},
		onError: () => {
			onClose()
			toast('Error generating certificate.')
		},
	})

	return (
		<StyleContainer>
			<Stack className='header'>
				<Typography variant='h5'>Add Certification</Typography>
				<IconButton onClick={onClose}>
					<Close />
				</IconButton>
			</Stack>
			<Stack className='container'>
				<CustomizedStack spacing={3} mt={2}>
					<FormControl fullWidth>
						<InputLabel id='dropdown-label'>
							Select the Signing Authority
						</InputLabel>
						<Select
							labelId='dropdown-label'
							value={dropdownValue}
							label='Select the Signing Authority'
							className='inputs'
							renderValue={(selected) =>
								certificateData?.signingAuthorityDetails?.[selected!]
									?.signingAuthorityName || ''
							}
							onChange={(e) => setDropdownValue(Number(e.target.value))}>
							{certificateData?.signingAuthorityDetails?.map((item, index) => (
								<MenuItem key={item.signingAuthorityName} value={index}>
									<Stack direction='column'>
										<Typography variant='body1'>
											{item.signingAuthorityName}
										</Typography>
										<Typography variant='body2'>
											{item.signingAuthorityPosition}
										</Typography>
									</Stack>
								</MenuItem>
							))}
						</Select>
					</FormControl>
				</CustomizedStack>

				<Stack direction='row' spacing={2} justifyContent='center' mt={5}>
					{isPending ? (
						<Button variant='contained' disabled>
							<CircularProgress size={20} />
						</Button>
					) : (
						<Button onClick={() => onGenerate()} variant='contained'>
							Generate
						</Button>
					)}

					<Button onClick={onClose} variant='outlined'>
						Cancel
					</Button>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

export const CustomizedStack = styled(Stack)(() => ({
	'.inputs': {
		height: 56,
	},
}))

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(1),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(3),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
	},
}))
