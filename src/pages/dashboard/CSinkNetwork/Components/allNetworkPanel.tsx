import { CustomDataGrid } from '@/components'
import { QueryAutoComplete, QueryInput } from '@/components/QueryInputs'
import { useAuthContext } from '@/contexts'
import { Biomass_Secondary_Unit, ICsink } from '@/interfaces'
import { Search } from '@mui/icons-material'
import { Stack, styled, Tooltip, Typography, useTheme } from '@mui/material'
import {
	GridColDef,
	GridEventListener,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { FC, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'

interface IProps {
	cSinkNetwork?: ICsink
	isLoading: boolean
	allBaList?: {
		label: string
		value: string
	}[]
}

const filterFieldForUser = ['biomass_aggregator', 'c_sink_manager', 'admin']

export const AllNetworkPanel: FC<IProps> = ({
	cSinkNetwork,
	isLoading,
	allBaList,
}) => {
	const navigate = useNavigate()
	const { userDetails } = useAuthContext()

	const filterInitialValue = useMemo(() => {
		if (filterFieldForUser.includes(userDetails?.accountType ?? '')) {
			return userDetails?.biomassAggregatorId
		}
		return ''
	}, [userDetails])
	const theme = useTheme()

	const column: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'name',
				headerName: 'Network Name',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'biomassAggregatorName',
				headerName: 'BA',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'farmersCount',
				headerName: 'Farmer Count',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'locationName',
				headerName: 'Location',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'cropBiomass',
				headerName: 'Biomass quantity',
				minWidth: 200,
				flex: 1,
				renderCell: (params) => {
					const value = params?.row?.cropBiomass
						? params?.row?.cropBiomass
								.map(
									({
										cropName,
										biomassCollectedInTonnes,
									}: {
										cropName: string
										biomassCollectedInTonnes: number
									}) => {
										return `${cropName}: ${biomassCollectedInTonnes} ${Biomass_Secondary_Unit}`
									}
								)
								.join(',\n')
						: '-'
					return params?.row?.cropBiomass ? (
						<Tooltip
							title={
								<Typography
									variant='subtitle1'
									style={{
										whiteSpace: 'pre-wrap',
									}}>
									{value}
								</Typography>
							}
							placement='top'>
							<Typography
								variant='subtitle1'
								style={{
									display: 'inline-block',
									wordWrap: 'break-word',
									whiteSpace: 'pre-wrap',
									textOverflow:
										(params?.row?.comments?.length ?? 0) > 200
											? 'ellipsis'
											: 'clip',
								}}>
								{value}
							</Typography>
						</Tooltip>
					) : (
						'-'
					)
				},
			},
			{
				field: 'cropBiochar',
				headerName: 'Biochar Qty',
				minWidth: 200,
				flex: 1,
				renderCell: (params) => {
					const value = params?.row?.cropBiochar
						? params?.row?.cropBiochar
								.map(
									({
										cropName,
										biocharProducedInTonne,
									}: {
										cropName: string
										biocharProducedInTonne: number
									}) => {
										return `${cropName}: ${biocharProducedInTonne} ${Biomass_Secondary_Unit}`
									}
								)
								.join(',\n')
						: '-'
					return params?.row?.cropBiochar ? (
						<Tooltip
							title={
								<Typography
									variant='subtitle1'
									style={{
										whiteSpace: 'pre-wrap',
									}}>
									{value}
								</Typography>
							}
							placement='top'>
							<Typography
								variant='subtitle1'
								style={{
									display: 'inline-block',
									wordWrap: 'break-word',
									whiteSpace: 'pre-wrap',
									textOverflow:
										(params?.row?.comments?.length ?? 0) > 200
											? 'ellipsis'
											: 'clip',
								}}>
								{value}
							</Typography>
						</Tooltip>
					) : (
						'-'
					)
				},
			},
			{
				field: 'cropCarbonCredits',
				headerName: 'Carbon Credits',
				minWidth: 200,
				flex: 1,
				renderCell: (params) => {
					const value = params?.row?.cropCarbonCredits
						? params?.row?.cropCarbonCredits.map(
								({
									cropName,
									carbonCreditsInTonnes,
								}: {
									cropName: string
									carbonCreditsInTonnes: number
								}) => (
									<div key={cropName}>
										{cropName}: {carbonCreditsInTonnes} tCO<sub>2</sub>
									</div>
								)
						  )
						: '-'

					return params?.row?.cropCarbonCredits ? (
						<Tooltip
							title={
								<Typography
									variant='subtitle1'
									style={{
										whiteSpace: 'pre-wrap',
									}}>
									{value}
								</Typography>
							}
							placement='top'>
							<Typography
								variant='subtitle1'
								style={{
									display: 'inline-block',
									wordWrap: 'break-word',
									whiteSpace: 'pre-wrap',
									textOverflow:
										(params?.row?.comments?.length ?? 0) > 200
											? 'ellipsis'
											: 'clip',
								}}>
								{value}
							</Typography>
						</Tooltip>
					) : (
						'-'
					)
				},
			},
			{
				field: 'isSuspended',
				headerName: 'Status',
				minWidth: 200,
				flex: 1,
				renderCell: (params) => {
					return (
						<Typography
							variant='subtitle1'
							style={{
								color: params.row.suspended
									? theme.palette.error.main
									: theme.palette.custom.green[700],
							}}>
							{params.row.suspended ? 'Suspended' : 'Active'}
						</Typography>
					)
				},
			},
		],
		[theme.palette.custom.green, theme.palette.error.main]
	)

	const handleRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(`${params?.row?.id}/details`)
		},
		[navigate]
	)

	return (
		<StyledContainer>
			<CustomDataGrid
				showPagination={true}
				rows={cSinkNetwork?.network || []}
				columns={column}
				rowCount={cSinkNetwork?.count ?? 0}
				headerComponent={
					<Stack className='header-filter-search' gap={2}>
						<QueryInput
							className='search-textFiled'
							queryKey='search'
							placeholder='Search'
							setPageOnSearch
							InputProps={{
								startAdornment: <Search fontSize='small' />,
							}}
						/>
						<QueryAutoComplete
							options={allBaList}
							queryKey={'baId'}
							label={'BA'}
							isDisable={filterFieldForUser.includes(
								userDetails?.accountType ?? ''
							)}
							initialValue={filterInitialValue ?? ''}
						/>
					</Stack>
				}
				loading={isLoading}
				onRowClick={handleRowClick}
			/>
		</StyledContainer>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	padding: theme.spacing(2, 0),
	'.grid-header-component': {
		flexDirection: 'row',
		alignItems: 'center',
		'.search-textFiled': {
			minWidth: 334,
			width: '100%',
			'.MuiInputBase-root': {
				height: theme.spacing(4.5),
				borderRadius: theme.spacing(1.25),
			},
		},
		'.form-controller': {
			margin: theme.spacing(0.125),
			minWidth: theme.spacing(18),
			width: '100%',
			'.MuiOutlinedInput-notchedOutline': {
				borderRadius: theme.spacing(1.25),
			},
		},
	},
}))
