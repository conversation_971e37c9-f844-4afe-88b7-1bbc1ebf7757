import { authAxios } from '@/contexts'
import {
	Farm,
	GetFarmerListResponse,
	ILocation,
	INetwork,
	KilnListResponse,
} from '@/interfaces'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { GridEventListener } from '@mui/x-data-grid'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useCallback, useMemo, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'
export const useCSinkNetworkDetails = () => {
	const queryClient = useQueryClient()
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsLimit = searchParams.get('limit') || defaultLimit
	const paramsPage = searchParams.get('page') || defaultPage
	const paramsTab = searchParams.get('tab') || 'kiln'
	const searchText = searchParams.get('search') || ''
	const paramsFarmerDataViewType =
		searchParams.get('farmerDataViewType') || 'list'
	const { cSinkNetworkId } = useParams()
	const [farmCoordinates, setFarmCoordinates] = useState<
		google.maps.LatLng[] | google.maps.LatLngLiteral[]
	>([])
	const [showMap, setShowMap] = useState(false)
	const navigate = useNavigate()
	const [showFarmsDrawer, setShowFarmDrawer] = useState<string | null>(null)

	const getCSinkNetworkDetails = useCallback(async () => {
		const { data } = await authAxios.get<INetwork>(
			`/cs-network/${cSinkNetworkId}`
		)
		return data
	}, [cSinkNetworkId])

	const getKilnDetails = useCallback(async () => {
		const { data } = await authAxios.get<KilnListResponse>(
			`/cs-network/${cSinkNetworkId}/kilns?sendAllKilns=false&limit=${paramsLimit}&page=${paramsPage}&search=${searchText}`
		)
		return data
	}, [cSinkNetworkId, paramsLimit, paramsPage, searchText])

	const getFarmerList = useCallback(async () => {
		const { data } = await authAxios.get<GetFarmerListResponse>(
			`/cs-network/${cSinkNetworkId}/farmers?limit=${paramsLimit}&page=${paramsPage}&search=${searchText}`
		)
		return data
	}, [cSinkNetworkId, paramsLimit, paramsPage, searchText])

	const getFarmList = useCallback(async () => {
		const { data } = await authAxios.get<{ farms: Farm[] }>(
			`/new/csink-network/${cSinkNetworkId}/farms`
		)
		return data
	}, [cSinkNetworkId])

	const handleRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(
				`/dashboard/production/batches?baId=${
					params?.row?.baID ?? ''
				}&networkId=${cSinkNetworkId}&kilnId=${params?.row?.id}`
			)
		},
		[cSinkNetworkId, navigate]
	)

	const cSinkNetworkDetailsQuery = useQuery({
		queryKey: ['cSinkNetworkDetails', cSinkNetworkId],
		queryFn: getCSinkNetworkDetails,
		enabled: !!cSinkNetworkId,
	})

	const KilnDetailsQuery = useQuery({
		queryKey: [
			'kilnDetails',
			cSinkNetworkId,
			searchText,
			paramsLimit,
			paramsPage,
		],
		queryFn: getKilnDetails,
		enabled: !!cSinkNetworkId && paramsTab === 'kiln',
	})

	const farmerListQuery = useQuery({
		queryKey: [
			'farmerList',
			cSinkNetworkId,
			searchText,
			paramsLimit,
			paramsPage,
		],
		queryFn: getFarmerList,
		enabled:
			!!cSinkNetworkId &&
			paramsTab === 'farmers' &&
			paramsFarmerDataViewType === 'list',
	})

	const farmListQuery = useQuery({
		queryKey: ['farmList', cSinkNetworkId],
		queryFn: getFarmList,
		select: (data) => {
			return data.farms?.map((farm) => ({
				position: {
					lat: farm?.location?.x || 0,
					lng: farm?.location?.y || 0,
				},
				name: farm?.landmark || '',
				id: farm?.id,
				desc: farm?.fieldSize
					? `(${farm?.fieldSize} ${farm?.fieldSizeUnit})`
					: '',
			}))
		},
		enabled:
			!!cSinkNetworkId &&
			paramsTab === 'farmers' &&
			paramsFarmerDataViewType === 'map',
	})

	// Add KML query for Kiln

	// const handleSaveKmlMutation = useMutation({
	// 	mutationKey: ['SaveKml'],
	// 	mutationFn: async (mapData: ILocation[]) => {
	// 		const networkId = searchParams.get('networkId')
	// 		if (!networkId) {
	// 			return
	// 		}
	// 		const payload = {
	// 			kmlCoordinates: mapData,
	// 		}
	// 		const api = `/cs-network/${networkId}/kilns/${networkId}/kml-coordinates`
	// 		await authAxios.put(api, payload)
	// 	},
	// 	onSuccess: () => {
	// 		KilnDetailsQuery.refetch()
	// 		queryClient.refetchQueries({ queryKey: ['getFarmsQuery'] })
	// 		toast('KML added')
	// 	},
	// 	onError: (error: AxiosError) => {
	// 		toast((error?.response?.data as { userToMessage: string })?.userToMessage)
	// 	},
	// })

	const handleSaveKmlMutation = useMutation({
		mutationKey: ['SaveKml'],
		mutationFn: async (mapData: ILocation[]) => {
			const farmId = searchParams.get('farmId')
			if (!farmId) {
				return
			}
			const payload = {
				id: farmId,
				area: mapData,
			}
			await authAxios.post(
				`/cs-network/${cSinkNetworkId}/farms/edit-area`,
				payload
			)
		},
		onSuccess: () => {
			queryClient.refetchQueries({ queryKey: ['farmerList'] })
			toast('Farm KML added')
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { userToMessage: string })?.userToMessage)
		},
	})
	const handleSaveKml = useCallback(
		async (mapData: ILocation[]) => {
			await handleSaveKmlMutation.mutateAsync(mapData)
		},
		[handleSaveKmlMutation]
	)
	const handleChangeFarmerDataViewType = useCallback(
		(view: string): void => {
			setSearchParams(
				(prev) => {
					if (view === 'list') {
						prev.delete('farmerDataViewType')
					} else {
						prev.set('farmerDataViewType', view)
					}
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const handleFarmerRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			setShowFarmDrawer(params?.row?.id)
		},
		[setShowFarmDrawer, farmerListQuery?.data]
	)
	const farmerInfoForSideDrawer = useMemo(() => {
		if (!showFarmsDrawer) return null
		const farmer =
			farmerListQuery?.data?.farmers?.find((i) => i?.id === showFarmsDrawer) ||
			null
		return farmer
	}, [showFarmsDrawer, farmerListQuery?.data])

	const handleDownloadExcelFarmersMuatation = useMutation({
		mutationKey: ['downloadFarmers'],
		mutationFn: async () => {
			const response = await authAxios.get(
				`cs-network/${cSinkNetworkId}/farmers/excel`,
				{
					responseType: 'blob',
				}
			)

			return response
		},
		onSuccess: (response) => {
			const url = window.URL.createObjectURL(new Blob([response.data]))
			const link = document.createElement('a')
			link.href = url
			link.setAttribute('download', `Farmers.xlsx`)
			document.body.appendChild(link)
			link.click()
			toast('File Downloaded')
		},
		onError: async (err: AxiosError) => {
			if (err.response?.data instanceof Blob) {
				try {
					const text = await err.response.data.text()
					const json = JSON.parse(text)
					toast(json.messageToUser || 'Something went wrong')
				} catch (e) {
					toast('Unexpected error format')
				}
			} else {
				const message = (err.response?.data as { messageToUser: string })
					?.messageToUser
				toast(message || 'Something went wrong')
			}
		},
	})

	return {
		cSinkNetworkDetails: cSinkNetworkDetailsQuery.data,
		kilnDetails: KilnDetailsQuery.data,
		setSearchParams,
		isLoading: KilnDetailsQuery.isLoading,
		farmerListQuery,
		searchParams,
		paramsTab,
		handleRowClick,
		handleSaveKml,
		handleFarmerRowClick,
		showFarmsDrawer,
		setShowFarmDrawer,
		paramsFarmerDataViewType,
		handleChangeFarmerDataViewType,
		farmListQuery,
		showMap,
		setShowMap,
		farmCoordinates,
		farmerInfoForSideDrawer,
		setFarmCoordinates,
		handleDownloadExcelFarmersMuatation,
	}
}
