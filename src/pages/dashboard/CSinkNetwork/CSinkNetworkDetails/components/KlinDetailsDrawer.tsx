import { ActionInformationDrawer, AddKiln } from '@/components'
import { IKiln } from '@/interfaces'
import { Close, Edit } from '@mui/icons-material'
import { Typography, styled, Stack, IconButton } from '@mui/material'
import { useMemo, useState } from 'react'

export const KlinDetailsDrawer = ({
	setHideModal,
	kilnData,
}: {
	setHideModal: () => void
	kilnData?: IKiln
}) => {
	const [editKilnDrawer, setEditKilnDrawer] = useState(false)

	const handleClose = () => {
		setEditKilnDrawer(false)
	}

	const kilnDetails = useMemo(() => {
		switch (kilnData?.kilnShape) {
			case 'cylindrical':
				return (
					<>
						<Stack className='container'>
							<Typography variant='subtitle1'>
								Diameter: {(kilnData?.diameter ?? 0) / 1000} m
							</Typography>
							<Typography variant='subtitle1'>
								Height: {(kilnData?.depth ?? 0) / 1000} m
							</Typography>
						</Stack>
					</>
				)
			case 'rectangular_frustum':
				return (
					<>
						<Stack className='container containerGapped'>
							<Typography variant='subtitle1'>
								Long Base: {(kilnData?.longBase ?? 0) / 1000} m
							</Typography>
							<Typography variant='subtitle1'>
								Short Base: {(kilnData?.shortBase ?? 0) / 1000} m
							</Typography>
						</Stack>
						<Stack className='container'>
							<Typography variant='subtitle1'>
								Height: {(kilnData?.depth ?? 0) / 1000}m
							</Typography>
						</Stack>
					</>
				)

			default:
				return (
					<>
						<Stack className='container containerGapped' spacing={0}>
							<Typography variant='subtitle1'>
								Upper Surface: {(kilnData?.upperSurfaceDiameter ?? 0) / 1000} m
							</Typography>
							<Typography variant='subtitle1'>
								Lower Surface: {(kilnData?.lowerSurfaceDiameter ?? 0) / 1000} m
							</Typography>
						</Stack>
						<Stack className='container'>
							<Typography variant='subtitle1'>
								Height: {(kilnData?.depth ?? 0) / 1000} m
							</Typography>
						</Stack>
					</>
				)
		}
	}, [kilnData])


	return (
		<>
			<ActionInformationDrawer
				open={editKilnDrawer}
				onClose={handleClose}
				anchor='right'
				component={
					<AddKiln
						editMode={true}
						kilnDetails={kilnData}
						handleClose={handleClose}
						subheading={kilnData?.ShortName}
					/>
				}
			/>
			<StyleContainer>
				<Stack className='header'>
					<Stack className='headtitle'>
						<Stack>
							<Typography variant='h5'>{kilnData?.name}</Typography>
						</Stack>
						<IconButton onClick={() => setHideModal()}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>
				<Stack className='nameDetailed'>
					<Stack direction={'row'} className='KilnHeader'>
						<Typography variant='body2'>
							{`${kilnData?.name} (${
								kilnData?.kilnType ? `${kilnData.kilnType} - ` : ''
							}${kilnData?.kilnShape} (${kilnData?.volume} ${
								kilnData?.volumeUnit
							}))`}
						</Typography>
						<IconButton onClick={() => setEditKilnDrawer(true)}>
							<Edit fontSize='small' />
						</IconButton>
					</Stack>
					{kilnDetails}
				</Stack>
			</StyleContainer>
		</>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		width: '90%',
		paddingLeft: theme.spacing(2),
		paddingRight: theme.spacing(2),
		gap: theme.spacing(0),
	},
	'.containerGapped': {
		display: 'flex',
		flexDirection: 'row',
		justifyContent: 'space-between',
	},
	'.headtitle': {
		display: 'flex',
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		width: '100%',
		gap: theme.spacing(1),
	},
	'.nameDetailed': {
		display: 'flex',
		flexDirection: 'column',
		paddingLeft: theme.spacing(2),
		paddingRight: theme.spacing(2),
		gap: theme.spacing(0.5),
		'.KilnHeader': {
			display: 'flex',
			justifyContent: 'space-between',
			alignItems: 'center',
		},
	},
}))
