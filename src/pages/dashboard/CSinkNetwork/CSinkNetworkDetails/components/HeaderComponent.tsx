import {
	CustomCard,
	CustomTagComponent,
	TagComponentWithToolTip,
} from '@/components'
import { MultipleAvatar } from '@/components/MultipleAvatar.tsx'
import { ImageURL, IMedia, INetwork } from '@/interfaces'
import {
	Box,
	Button,
	Card,
	Link,
	Stack,
	Tooltip,
	Typography,
} from '@mui/material'
import React, { useMemo } from 'react'
import { ChartComponent } from './ChartComponent'
import {
	capitalizeFirstLetter,
	convertKgToTon,
	getGoogleMapLink,
} from '@/utils/helper'
import { PlaceOutlined } from '@mui/icons-material'
import { EntityTabEnum } from '@/utils/constant'

type HeaderComponentProps = {
	cSinkNetworkDetails: INetwork
	setShowMethaneStrategy: React.Dispatch<React.SetStateAction<boolean>>
	setShowBiomassPreProccessingStrategy: React.Dispatch<
		React.SetStateAction<boolean>
	>
	setShowOperatorsDrawer?: React.Dispatch<React.SetStateAction<boolean>>
	setOpenEditNetworkModal?: (
		value: React.SetStateAction<EntityTabEnum | null>
	) => void
}

const ToolTipComponent: React.FC<{
	dataArr: { name: string; description?: string }[]
}> = ({ dataArr }) => {
	return (
		<Stack rowGap={1}>
			{dataArr.map((data, index) => (
				<Stack
					key={`${data?.name}-${index}`}
					direction='row'
					columnGap={1}
					alignItems='center'>
					<Typography variant='caption'>{data.name}</Typography>
					{data?.description ? (
						<Typography fontSize={10}>({data?.description})</Typography>
					) : null}
				</Stack>
			))}
		</Stack>
	)
}

const hideChartByLength = (length?: number) => {
	return Number(length) > 1 ? false : true
}
const getCount = (length: number) => {
	if (length > 2) {
		return `+ ${length - 2}`
	}
	return ''
}

const MultipleAvatarWrapper: React.FC<{
	images: ImageURL[]
	length: number
}> = ({ images, length }) => {
	return (
		<Stack direction='row' alignItems='center' columnGap={1}>
			<MultipleAvatar size={30} imageList={images} MaxAvatar={3} />
			<Typography variant='subtitle1'>{getCount(length)}</Typography>
		</Stack>
	)
}

export const HeaderComponent: React.FC<HeaderComponentProps> = ({
	cSinkNetworkDetails,
	setShowBiomassPreProccessingStrategy,
	setShowMethaneStrategy,
	setShowOperatorsDrawer,
	setOpenEditNetworkModal,
}) => {
	const networkDetails: {
		[key: string]: {
			label: string
			value: React.ReactNode
			tooltipComponent?: React.ReactNode
			showTooltip?: boolean
			clickEvents?: (event: React.MouseEvent<HTMLElement>) => void
		}[]
	} = useMemo(
		() => ({
			upper: [
				{
					label: 'Preferred Biomass',
					value: cSinkNetworkDetails?.details?.preferredCrops?.length ? (
						<MultipleAvatarWrapper
							images={
								cSinkNetworkDetails?.details?.preferredCrops?.map(
									(crop) => crop?.image
								) || []
							}
							length={cSinkNetworkDetails?.details?.preferredCrops?.length}
						/>
					) : (
						0
					),
					tooltipComponent: (
						<ToolTipComponent
							dataArr={(
								cSinkNetworkDetails?.details?.preferredCrops ?? []
							)?.map((item) => ({
								name: item.name,
							}))}
						/>
					),
					showTooltip: !!cSinkNetworkDetails?.details?.preferredCrops?.length,
					clickEvents: () =>
						setOpenEditNetworkModal?.(EntityTabEnum.preferredBiomass),
				},

				{
					label: 'Biomass Pre-Processing',
					value: (
						<Box
							component={Stack}
							onClick={() => setShowBiomassPreProccessingStrategy(true)}
							sx={{ cursor: 'pointer' }}>
							{(
								cSinkNetworkDetails?.biomassPreprocessingDetails
									.dryingStrategy || ''
							)?.length > 25
								? `Drying - ${cSinkNetworkDetails?.biomassPreprocessingDetails?.dryingStrategy?.slice(
										0,
										25
								  )}...`
								: `Drying  ${
										cSinkNetworkDetails?.biomassPreprocessingDetails
											?.dryingStrategy ?? ''
								  }`}
						</Box>
					),
					tooltipComponent: (
						<ToolTipComponent
							dataArr={[
								{
									name: `Drying - ${capitalizeFirstLetter(
										cSinkNetworkDetails?.biomassPreprocessingDetails?.dryingType?.replace(
											'_',
											' '
										) || ''
									)}`,
									description:
										cSinkNetworkDetails?.biomassPreprocessingDetails
											?.dryingStrategy || '',
								},
								{
									name: `Shredding - ${capitalizeFirstLetter(
										cSinkNetworkDetails?.biomassPreprocessingDetails?.shreddingType?.replace(
											'_',
											' '
										) || ''
									)}`,
									description:
										cSinkNetworkDetails?.biomassPreprocessingDetails
											?.shreddingStrategy || '',
								},
							]}
						/>
					),
					showTooltip:
						Object.keys(cSinkNetworkDetails?.biomassPreprocessingDetails ?? {})
							.length !== 0,
					clickEvents: () => setShowBiomassPreProccessingStrategy(true),
				},

				{
					label: 'Methane Compensation Strategy',
					value:
						cSinkNetworkDetails?.methaneCompensateStrategies?.length > 1 ? (
							<Button
								onClick={() => setShowMethaneStrategy(true)}
								variant='text'
								size='small'
								color='inherit'>
								View Details
							</Button>
						) : (
							<Box
								component={Stack}
								onClick={() => setShowMethaneStrategy(true)}
								sx={{ cursor: 'pointer' }}>
								{(
									cSinkNetworkDetails?.methaneCompensateStrategies?.[0]
										?.description || ''
								)?.length > 30
									? `${capitalizeFirstLetter(
											cSinkNetworkDetails?.methaneCompensateStrategies?.[0]
												?.biomassName
									  )} ${capitalizeFirstLetter(
											cSinkNetworkDetails?.methaneCompensateStrategies?.[0]?.methaneCompensateType?.replace(
												'_',
												' '
											)
									  )}  ${capitalizeFirstLetter(
											cSinkNetworkDetails?.methaneCompensateStrategies?.[0]?.compensateType?.replace(
												'_',
												' '
											)
									  )}
									      ${cSinkNetworkDetails?.methaneCompensateStrategies?.[0]?.description?.slice(
													0,
													30
												)}...`
									: `${capitalizeFirstLetter(
											cSinkNetworkDetails?.methaneCompensateStrategies?.[0]
												?.biomassName
									  )} - ${capitalizeFirstLetter(
											cSinkNetworkDetails?.methaneCompensateStrategies?.[0]?.methaneCompensateType?.replace(
												'_',
												' '
											)
									  )}  ${
											cSinkNetworkDetails?.methaneCompensateStrategies?.[0]
												?.description || ''
									  }`}
							</Box>
						),
					tooltipComponent: (
						<ToolTipComponent
							dataArr={[
								{
									name: `${
										cSinkNetworkDetails?.methaneCompensateStrategies?.[0]
											?.methaneCompensateType
											? `${capitalizeFirstLetter(
													cSinkNetworkDetails?.methaneCompensateStrategies?.[0]
														?.biomassName
											  )} - ${capitalizeFirstLetter(
													cSinkNetworkDetails?.methaneCompensateStrategies?.[0]?.methaneCompensateType?.replace(
														'_',
														' '
													)
											  )}`
											: ''
									}`,

									description: cSinkNetworkDetails
										?.methaneCompensateStrategies?.[0]?.methaneCompensateType
										? capitalizeFirstLetter(
												cSinkNetworkDetails?.methaneCompensateStrategies?.[0]?.description?.replace(
													'_',
													' '
												)
										  )
										: '',
								},
							]}
						/>
					),
					showTooltip:
						cSinkNetworkDetails?.methaneCompensateStrategies?.length === 1,
					clickEvents: () => setShowMethaneStrategy(true),
				},
			],
		}),
		[
			cSinkNetworkDetails?.details?.preferredCrops,
			cSinkNetworkDetails?.biomassPreprocessingDetails,
			cSinkNetworkDetails?.methaneCompensateStrategies,
			setOpenEditNetworkModal,
			setShowBiomassPreProccessingStrategy,
			setShowMethaneStrategy,
		]
	)

	const chartData = useMemo(
		() => [
			{
				label: 'Biomass',
				hidden: !cSinkNetworkDetails?.details?.biomass?.length,
				chart: (
					<ChartComponent
						hidden={hideChartByLength(
							cSinkNetworkDetails?.details?.biomass?.length
						)}
						data={{
							labels:
								cSinkNetworkDetails?.details?.biomass?.map(
									(item) => item.cropName
								) || [],
							datasets:
								cSinkNetworkDetails?.details?.biomass?.map((item) =>
									convertKgToTon(item.biomassProduced)
								) || [],
							extraDataSet: cSinkNetworkDetails?.details?.biomass?.map((item) =>
								convertKgToTon(item.biomassAvailable)
							),
							showBiomassTooltip: true,
						}}
					/>
				),
			},
			{
				label: 'Biochar',
				hidden: !cSinkNetworkDetails?.details?.biochar?.length,
				chart: (
					<ChartComponent
						hidden={hideChartByLength(
							cSinkNetworkDetails?.details?.biochar?.length
						)}
						data={{
							labels:
								cSinkNetworkDetails?.details?.biochar?.map(
									(item) => item.cropName
								) || [],
							datasets:
								cSinkNetworkDetails?.details?.biochar?.map(
									(item) => item.biocharProducedInTonne
								) || [],
							showUnit: 'ton',
						}}
					/>
				),
			},
			{
				label: 'Carbon Credits',
				hidden: !cSinkNetworkDetails?.details?.carbonCredits?.length,
				chart: (
					<ChartComponent
						hidden={hideChartByLength(
							cSinkNetworkDetails?.details?.carbonCredits?.length
						)}
						data={{
							labels:
								cSinkNetworkDetails?.details?.carbonCredits?.map(
									(item) => item.cropName
								) || [],
							datasets:
								cSinkNetworkDetails?.details?.carbonCredits?.map(
									(item) => item.carbonCredits
								) || [],
							showUnit: 'tCo2',
						}}
					/>
				),
			},
		],
		[
			cSinkNetworkDetails?.details?.biochar,
			cSinkNetworkDetails?.details?.biomass,
			cSinkNetworkDetails?.details?.carbonCredits,
		]
	)

	return (
		<Box className='card_container'>
			<Card className='details-header' elevation={0}>
				<Stack
					flexDirection='row'
					alignItems='flex-start'
					flexWrap='wrap'
					columnGap={8}
					justifyContent='space-between'>
					<Stack rowGap={1}>
						<Typography variant='body2' textTransform='capitalize'>
							{cSinkNetworkDetails?.name}
						</Typography>
						<Typography variant='overline' textTransform='none'>
							Network ID: {cSinkNetworkDetails?.shortName ?? ''}
						</Typography>
						<Stack>
							<CustomTagComponent
								label='Address'
								lighterHeading
								value={
									<Stack flexDirection={'row'} gap={0.5}>
										{cSinkNetworkDetails?.location ? (
											<Stack
												direction='row'
												alignItems='center'
												component={Link}
												target='_blank'
												underline='hover'
												textTransform='none'
												color='common.black'
												href={getGoogleMapLink(
													`${cSinkNetworkDetails?.location?.x}`,
													`${cSinkNetworkDetails?.location?.y}`
												)}>
												<PlaceOutlined
													color='primary'
													sx={{ width: 20, height: 20 }}
												/>
											</Stack>
										) : null}
										<Typography variant='subtitle1'>
											{cSinkNetworkDetails?.locationName ?? '-'}
										</Typography>
									</Stack>
								}
							/>
							<CustomTagComponent
								label='Emissions'
								lighterHeading
								value={
									<Typography variant='subtitle1'>
										{cSinkNetworkDetails?.footPrint
											? `Transportation : ${cSinkNetworkDetails?.footPrint}`
											: `-`}
									</Typography>
								}
							/>
							<CustomTagComponent
								label='Admin'
								value={
									<Stack
										onClick={() =>
											setOpenEditNetworkModal?.(EntityTabEnum.admins)
										}>
										<Tooltip
											title={
												<Stack direction='column'>
													{(cSinkNetworkDetails?.managerDetails ?? [])?.map(
														(item) => (
															<Typography
																key={item?.managerId}
																variant='caption'>
																{item?.managerName} {item?.managerEmail}
																<br />
																{item?.managerPhone
																	? `(${item?.countryCode}-${item?.managerPhone})`
																	: ''}
															</Typography>
														)
													)}
												</Stack>
											}
											placement='bottom-start'
											arrow>
											<Box>
												<MultipleAvatarWrapper
													images={
														cSinkNetworkDetails?.managerDetails?.map(
															(manager) => manager?.profileImageUrl
														) || []
													}
													length={
														cSinkNetworkDetails?.managerDetails?.length || 0
													}
												/>
											</Box>
										</Tooltip>
									</Stack>
								}
								lighterHeading
							/>
							<Stack
								className='managerDetails'
								onClick={() => setShowOperatorsDrawer?.(true)}>
								<CustomTagComponent
									label='Operator'
									value={
										<Tooltip
											title={(cSinkNetworkDetails?.operators ?? [])?.map(
												(item) => (
													<Typography key={item?.id} textAlign='center'>
														{item?.name}
														<br />
														{item?.phoneNo
															? `(${item?.countryCode}-${item?.phoneNo})`
															: ''}
													</Typography>
												)
											)}
											placement='bottom-start'
											arrow>
											<Box>
												<MultipleAvatarWrapper
													images={
														cSinkNetworkDetails?.operators?.map(
															(operator) => operator?.profileImage as IMedia
														) || []
													}
													length={cSinkNetworkDetails?.operators?.length || 0}
												/>
											</Box>
										</Tooltip>
									}
									lighterHeading
								/>
							</Stack>
						</Stack>
					</Stack>
					<Stack flex={1}>
						<CustomCard
							headerComponent={
								<Stack className='network_detail_container'>
									<Typography variant='h5'>Network Details</Typography>
									<Stack className='section_container'>
										{Object.keys(networkDetails).map((position) => (
											<Box key={position} className={`section`}>
												{networkDetails[position].map((item) => (
													<TagComponentWithToolTip
														lighterHeading
														clickEvents={item?.clickEvents}
														key={item.label}
														label={item.label}
														value={item?.value}
														{...(item?.tooltipComponent
															? {
																	tooltipComponent: item?.tooltipComponent,
																	showTooltip: item?.showTooltip,
															  }
															: {})}
													/>
												))}
											</Box>
										))}
										<Box className='chart_section'>
											{chartData.map(
												(chart) =>
													!chart?.hidden && (
														<Stack key={chart.label} alignItems='center'>
															<Typography
																className='chart_text'
																textAlign='center'>
																{chart.label}
															</Typography>
															{chart.chart}
														</Stack>
													)
											)}
										</Box>
									</Stack>
								</Stack>
							}
						/>
					</Stack>
				</Stack>
			</Card>
		</Box>
	)
}
