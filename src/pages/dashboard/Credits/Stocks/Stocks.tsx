import { CustomDataGrid } from '@/components'
import { publicAxios } from '@/contexts'
import { IMixing, IProcessData, IStockDistribution, Site } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { KilnsResponse } from '@/types'
import { dateFormats, defaultLimit, defaultPage } from '@/utils/constant'
import {
	getFormattedDate,
	getGoogleMapLink,
	getSerialNumber,
} from '@/utils/helper'
import { ArrowLeftRounded } from '@mui/icons-material'
import CloseIcon from '@mui/icons-material/Close'
import {
	alpha,
	Box,
	Button,
	Dialog,
	DialogContent,
	DialogTitle,
	Divider,
	Grid,
	IconButton,
	Paper,
	Stack,
	styled,
	Tooltip,
	Typography,
	TypographyProps,
} from '@mui/material'
import { GridColDef, GridValidRowModel } from '@mui/x-data-grid'
import { useQuery } from '@tanstack/react-query'
import { ReactNode, useMemo, useState } from 'react'
import {
	useNavigate,
	useParams,
	useSearchParams,
	useLocation,
} from 'react-router-dom'
import { toast } from 'react-toastify'
import { BatchDetails } from '../../Production'
import { DocumentViewer } from '@/components/TrainingProofRenderer/DocumentViewer'
import { PdfPreviewDialog } from '@/components/TrainingProofRenderer/PdfPreviewDialog'

export const Stocks = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const { stockId } = useParams()
	const page = searchParams.get('page') || defaultPage
	const limit = searchParams.get('limit') || defaultLimit
	const navigate = useNavigate()
	const location = useLocation()
	const [showDialog, setShowDialog] = useState<string>('')
	const [showDocument, setShowDocument] = useState<boolean>(false)

	const isPublic = useMemo(() => {
		return !location.pathname.includes('dashboard')
	}, [location.pathname])

	const handleBack = () => {
		navigate(-1)
	}

	// Private Routes

	// const getBatchListQuery = useQuery({
	// 	queryKey: ['batchesQuery', page, limit, stockId],
	// 	queryFn: async () => {
	// 		try {
	// 			const { data } = await authAxios<IProcessData>(
	// 				`new/stocks/${stockId}/processes?limit=${limit}&page=${page}`
	// 			)
	// 			return data
	// 		} catch (error: any) {
	// 			toast(error?.response?.data?.messageToUser)
	// 			return null
	// 		}
	// 	},
	// })

	// const getMixingQuery = useQuery({
	// 	queryKey: ['getMixingQuery', stockId],
	// 	queryFn: async () => {
	// 		try {
	// 			const { data } = await authAxios<IMixing[]>(
	// 				`new/stocks/${stockId}/mixing?`
	// 			)
	// 			return data
	// 		} catch (error: any) {
	// 			toast(error?.response?.data?.messageToUser)
	// 			return null
	// 		}
	// 	},
	// })

	// const getDistributionQuery = useQuery({
	// 	queryKey: ['getDistributionQuery', stockId],
	// 	queryFn: async () => {
	// 		try {
	// 			const { data } = await authAxios<IStockDistribution[]>(
	// 				`new/stocks/${stockId}/distribution`
	// 			)
	// 			return data
	// 		} catch (error: any) {
	// 			toast(error?.response?.data?.messageToUser)
	// 			return null
	// 		}
	// 	},
	// })

	// public Routes

	const getBatchListQuery = useQuery({
		queryKey: ['publicBatchesQuery', page, limit, stockId],
		queryFn: async () => {
			try {
				const { data } = await publicAxios<IProcessData>(
					`public/stocks/${stockId}/processes?limit=${limit}&page=${page}`
				)
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
	})

	const getMixingQuery = useQuery({
		queryKey: ['getPublicMixingQuery', stockId],
		queryFn: async () => {
			try {
				const { data } = await publicAxios<IMixing[]>(
					`public/stocks/${stockId}/mixing`
				)
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
	})

	const getDistributionQuery = useQuery({
		queryKey: ['getPublicDistributionQuery', stockId],
		queryFn: async () => {
			try {
				const { data } = await publicAxios<IStockDistribution[]>(
					`public/stocks/${stockId}/distribution`
				)
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
	})

	const getProductionQuery = useQuery({
		queryKey: ['getPublicProductionQuery', stockId],
		queryFn: async () => {
			try {
				const { data } = await publicAxios<KilnsResponse>(
					`public/stocks/${stockId}/production`
				)
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
	})

	const projectDetails = useMemo(
		() => [
			{
				label: 'Stock ID:',
				value: stockId,
			},
			{
				label: 'Project Name:',
				value: `${getBatchListQuery?.data?.projectDetail?.ProjectId} ${
					getBatchListQuery?.data?.projectDetail?.registryProjectName
						? `(${getBatchListQuery?.data?.projectDetail?.registryProjectName})`
						: ''
				}`,
			},
			{
				label: 'Total C-Sink(tCO2e)',
				value:
					getBatchListQuery?.data?.projectDetail?.totalCarbonCreditsInTonnes,
			},
			{
				label: 'Total SPC Fraction (tCO2e)',
				value: getBatchListQuery?.data?.projectDetail?.totalSPCFraction,
			},
		],
		[
			stockId,
			getBatchListQuery?.data?.projectDetail?.ProjectId,
			getBatchListQuery?.data?.projectDetail?.registryProjectName,
			getBatchListQuery?.data?.projectDetail?.totalCarbonCreditsInTonnes,
			getBatchListQuery?.data?.projectDetail?.totalSPCFraction,
		]
	)

	const networkDetails = useMemo(() => {
		const networsLength =
			getBatchListQuery?.data?.projectDetail?.networks?.length

		return [
			{
				key: 0,
				label: 'Network Names',
				value: (
					<Stack>
						<Typography
							variant='body2'
							sx={{
								fontWeight: theme.typography.body1.fontWeight,
							}}>
							{getBatchListQuery?.data?.projectDetail?.networks[0]?.name}
						</Typography>
						{(networsLength ?? 0) - 1 > 0 ? (
							<Tooltip
								title={getBatchListQuery?.data?.projectDetail?.networks?.map(
									(item, index) =>
										index > 1 && <Typography>{item.name}</Typography>
								)}>
								<Typography
									variant='body1'
									sx={{
										color: theme.palette.grey[600],
									}}>{`+${(networsLength ?? 0) - 1} more networks`}</Typography>
							</Tooltip>
						) : null}
					</Stack>
				),
			},
			{
				key: 1,
				label: 'C-Sink Manager',
				value: getBatchListQuery?.data?.projectDetail?.csinkManagerName,
			},
			{
				key: 2,
				label: (
					<Stack direction={'row'} gap={theme.spacing(1)} alignItems={'center'}>
						<Typography variant='caption' color={theme.palette.neutral[300]}>
							Feedstock Name
						</Typography>
						<Typography
							variant='caption'
							className='viewDocumentButton'
							onClick={() => setShowDocument(true)}>
							( View Document )
						</Typography>
					</Stack>
				),
				value: getBatchListQuery?.data?.projectDetail?.biomassName,
			},
		]
	}, [
		getBatchListQuery?.data?.projectDetail?.networks,
		getBatchListQuery?.data?.projectDetail?.csinkManagerName,
		getBatchListQuery?.data?.projectDetail?.biomassName,
	])

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S.No',
				flex: 0.5,
				minWidth: 80,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{getSerialNumber(params, Number(limit))}
					</Typography>
				),
			},
			{
				field: 'startDate',
				headerName: 'Date',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{getFormattedDate(params?.value, dateFormats.yyyy_MM_dd)}
					</Typography>
				),
			},

			{
				field: 'biocharQuantity',
				headerName: 'Biochar Qty',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value ?? 0} litres
					</Typography>
				),
			},
			{
				field: 'siteName',
				headerName: 'Site Name',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.row?.isSiteProcess
							? params?.row?.siteName
							: params?.row?.kilnName}
					</Typography>
				),
			},

			{
				field: 'cropName',
				headerName: 'Biomass Type',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value}</Typography>
				),
			},

			{
				field: 'carbonCredits',
				headerName: 'Carbon credits',
				minWidth: 150,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value ? `${params?.value} tCO2` : '-'}
					</Typography>
				),
			},
		],
		[limit, page]
	)

	return (
		<>
			<StyledContainer>
				{!isPublic ? (
					<Stack className='header-navigation'>
						<Stack className='header-navigation-start'>
							<Button
								onClick={handleBack}
								className='batch-button'
								variant='text'
								startIcon={<ArrowLeftRounded fontSize='small' />}>
								Stock
							</Button>
							<Typography variant='body1' color={theme.palette.neutral['500']}>
								&nbsp;/ {stockId}{' '}
							</Typography>
						</Stack>
						<Stack className='header-navigation-end'>
							<Button onClick={() => navigate(`/credits/stocks/${stockId}`)}>
								Public Link
							</Button>
						</Stack>
					</Stack>
				) : null}
				<Stack
					className='container'
					sx={{ margin: `${isPublic ? '20px' : '0px'}` }}>
					<Box className='project-details'>
						{projectDetails.map((detail) => (
							<Stack key={detail.label} className='info-container'>
								<Typography className='label'>{detail.label}</Typography>
								<Typography className='value'>{detail.value || '-'}</Typography>
							</Stack>
						))}
					</Box>
					{/* Network details */}
					<Box className='project-network-details'>
						<Stack
							className='network-details-header'
							sx={{
								width: '100%',
								display: 'flex',
								flexDirection: 'row',
								justifyContent: 'space-between',
							}}>
							{networkDetails.map((detail) => (
								<Stack key={detail.key} className='info-container'>
									{typeof detail.label === 'string' ? (
										<Typography className='label'>{detail.label}</Typography>
									) : (
										detail.label
									)}
									{typeof detail.value === 'string' ? (
										<Typography className='value'>
											{detail.value || '-'}
										</Typography>
									) : (
										detail.value
									)}
								</Stack>
							))}
						</Stack>
						<Divider
							sx={{
								borderTop: '1px solid black',
								borderColor: theme.palette.grey[200],
							}}
						/>
						<HorizontalScrollStack
							sites={getBatchListQuery?.data?.projectDetail?.sites}
						/>
					</Box>
					<Grid container spacing={2.5}>
						{/* Biochar Production */}
						<Grid item xs={12} md={4}>
							<Typography
								variant='body2'
								pb={theme.spacing(1)}
								pl={theme.spacing(1)}>
								Biochar Production
							</Typography>
							<Stack gap={theme.spacing(3)}>
								{getProductionQuery?.data?.kilns?.map((kiln) => (
									<Box className='box-container' key={kiln?.id}>
										<Stack
											p={theme.spacing(1)}
											width={'100%'}
											gap={theme.spacing(0.5)}>
											<Typography
												variant='h6'
												alignSelf={'flex-start'}
												sx={{ textDecoration: 'none', fontStyle: 'normal' }}>
												{kiln?.name}
											</Typography>
											<Typography>
												<Button
													component='a'
													href={getGoogleMapLink(
														String(kiln?.coordinate?.x ?? '0'),
														String(kiln?.coordinate?.y ?? '0')
													)}
													target='_blank'
													variant='text'
													sx={{
														p: 0,
														width: 'fit-content',
														color: 'black',
													}}>
													<Typography
														variant='subtitle1'
														sx={{
															textDecoration: 'underline',
														}}>
														{kiln?.address}
													</Typography>
												</Button>
											</Typography>
										</Stack>
										<Stack className='box-container-bottom' width={'100%'}>
											<CustomTagComponent
												label='Quantity (Litres)'
												value={`${kiln?.biocharQuantity ?? 0} liters`}
											/>
											<CustomTagComponent
												label='Quantity (ton)'
												value={kiln?.biocharQuantityInTonnes}
											/>
										</Stack>
									</Box>
								))}
							</Stack>
						</Grid>

						{/* Mixing */}
						<Grid item xs={12} md={4}>
							<Typography
								variant='body2'
								pb={theme.spacing(1)}
								pl={theme.spacing(1)}>
								Mixing/Packaging :
							</Typography>
							<Stack gap={theme.spacing(3)}>
								{getMixingQuery?.data?.map((mixing) => (
									<Box className='box-container-2' key={mixing?.id}>
										<Stack direction='column' width='100%'>
											<CustomTagComponent
												label='Matrix ID'
												value={`${mixing?.matrixId} (${mixing?.name})`}
											/>
											<CustomTagComponent
												label='Quantity (Litres)'
												value={`${mixing?.biocharQuantity ?? 0} liters`}
											/>
										</Stack>

										<CustomTagComponent
											label='Locations:'
											value={
												<Stack direction='column'>
													{mixing?.locations?.map((x, index) => (
														<Typography
															variant='body1'
															key={index}>{`${x.x}, ${x.y}`}</Typography>
													))}
												</Stack>
											}
										/>
									</Box>
								))}
							</Stack>
						</Grid>

						{/* Distribution */}
						<Grid item xs={12} md={4}>
							<Typography
								variant='body2'
								pb={theme.spacing(1)}
								pl={theme.spacing(1)}>
								Application:
							</Typography>
							<Stack gap={theme.spacing(3)}>
								{getDistributionQuery?.data?.map((dist) => (
									<Box className='box-container-2' key={dist?.id}>
										<Stack direction='column' width='100%'>
											<CustomTagComponent label='Name: ' value={dist?.name} />
											<CustomTagComponent
												label='Quantity (Litres)'
												value={`${dist?.biocharQuantity ?? 0} liters`}
											/>
										</Stack>

										<CustomTagComponent
											label='Locations:'
											value={`${dist?.location?.x}, ${dist?.location?.y}`}
										/>
									</Box>
								))}
							</Stack>
						</Grid>
					</Grid>
				</Stack>

				<Stack className='container'>
					<CustomDataGrid
						onRowClick={(params) => {
							setSearchParams()
							setShowDialog(params?.row?.id)
						}}
						showPagination
						columns={columns}
						headerComponent={<Typography variant='body2'>Batches:</Typography>}
						rows={getBatchListQuery?.data?.processes ?? []}
						rowCount={getBatchListQuery?.data?.count ?? 0}
						loading={getBatchListQuery?.isLoading}
					/>
				</Stack>
			</StyledContainer>
			{getBatchListQuery?.data?.projectDetail?.labReportDocuments?.length ===
			1 ? (
				<PdfPreviewDialog
					open={showDocument}
					close={() => setShowDocument(false)}
					pdfUrl={
						getBatchListQuery?.data?.projectDetail?.labReportDocuments[0]?.url
					}
					showDownloadButton={false}
				/>
			) : (
				<DocumentViewer
					open={showDocument}
					close={() => setShowDocument(false)}
					documents={(
						getBatchListQuery?.data?.projectDetail?.labReportDocuments ?? []
					)?.map((i) => ({
						...i,
						fileName: '.pdf',
					}))}
					showDownloadButton={false}
				/>
			)}

			<Dialog open={!!showDialog} onClose={() => setShowDialog('')} fullScreen>
				<DialogTitle
					sx={{
						py: theme.spacing(5),
						display: 'flex',
						justifyContent: 'space-between',
					}}>
					<Typography variant='h6'>Batch details</Typography>
					<IconButton aria-label='close' onClick={() => setShowDialog('')}>
						<CloseIcon />
					</IconButton>
				</DialogTitle>

				<DialogContent>
					<BatchDetails id={showDialog} showHeader={false} />
				</DialogContent>
			</Dialog>
		</>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header-navigation': {
		padding: theme.spacing(4, 1, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
		alignItems: 'center',
		flexDirection: 'row',
		justifyContent: 'space-between',
		'.batch-button': {
			color: theme.palette.neutral['500'],
			...theme.typography.body1,
			'.arrow-icon': {
				color: theme.palette.neutral['500'],
			},
		},
		'.header-navigation-start': {
			alignItems: 'center',
			flexDirection: 'row',
		},
		'.header-navigation-end': {
			alignItems: 'center',
			flexDirection: 'row',
		},
	},
	'.container': {
		padding: theme.spacing(1, 2.5),
		gap: theme.spacing(3),

		'.box-container': {
			display: 'flex',
			flexDirection: 'column',
			alignItems: 'center',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.5)} 0 ${alpha(
				theme.palette.common.black,
				0.4
			)}`,
			padding: theme.spacing(1, 2),
			'.box-container-bottom': {
				Padding: '0',
				display: 'flex',
				justifyContent: 'space-between',
				flexDirection: 'row',
			},
		},
		'.box-container-2': {
			display: 'flex',
			flexDirection: 'row',
			borderRadius: theme.spacing(2),
			boxShadow: `0 0 ${theme.spacing(0.5)} 0 ${alpha(
				theme.palette.common.black,
				0.4
			)}`,
			padding: theme.spacing(1, 2),
		},
		'.project-details': {
			display: 'grid',
			gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
			rowGap: theme.spacing(2),
			paddingBottom: theme.spacing(4.375),
			'.info-container': {
				gap: theme.spacing(1),
				'.label': {
					fontSize: theme.typography.caption.fontSize,
					color: theme.palette.neutral[300],
				},
				'.value': {
					...theme.typography.body2,
					fontWeight: theme.typography.body1.fontWeight,
				},
				'.btn': {
					width: 'fit-content',
					...theme.typography.body2,
				},
			},
		},
		'.project-network-details': {
			display: 'flex',
			flexDirection: 'column',
			borderRadius: theme.spacing(2),
			border: `${theme.spacing(0.05)} solid ${theme.palette.primary.main}`,
			backgroundColor: '#FBF7F6',
			gap: theme.spacing(2),
			padding: theme.spacing(1, 2),
			'.network-details-header': {
				display: 'grid',
				padding: theme.spacing(0.5),
				gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',

				'.info-container': {
					gap: theme.spacing(1),
					'.label': {
						fontSize: theme.typography.caption.fontSize,
						color: theme.palette.neutral[300],
					},
					'.value': {
						...theme.typography.body2,
						fontWeight: theme.typography.body1.fontWeight,
					},
					'.btn': {
						width: 'fit-content',
						...theme.typography.body2,
					},
				},
			},
		},
		'.viewDocumentButton': {
			color: theme.palette.grey[600],
			cursor: 'pointer',
		},
	},
}))
interface ITagComponent {
	label: string
	value: string | ReactNode
	headingStyle?: TypographyProps
}

function CustomTagComponent(props: ITagComponent) {
	const { label, value, headingStyle } = props

	return (
		<StyledTag>
			<Typography className='title' {...headingStyle}>
				{label}
			</Typography>
			{typeof value === 'string' ? (
				<Typography variant='body1' sx={{ font: 'bold' }}>
					{value}
				</Typography>
			) : (
				value
			)}
		</StyledTag>
	)
}
const StyledTag = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(0.5),
	width: '100%',
	flexDirection: 'column',
	padding: theme.spacing(1),
	display: 'flex',
	'.title': {
		fontSize: theme.spacing(1.75),
		fontStyle: 'normal',
		color: theme.palette.neutral[300],
	},
}))

function HorizontalScrollStack({ sites }: { sites: Site[] | undefined }) {
	return (
		<Box sx={{ overflowX: 'auto' }}>
			<Typography
				variant='body2'
				sx={{
					paddingBottom: theme.spacing(1.5),
					color: theme.palette.grey[600],
				}}>
				Site Information
			</Typography>
			<Stack
				direction='row'
				spacing={2}
				sx={{ paddingBottom: theme.spacing(2) }}>
				{sites?.map((item) => (
					<Paper
						key={item.id}
						sx={{
							borderRadius: theme.spacing(1),
							padding: theme.spacing(1.5),
							minWidth: 250,
						}}>
						<Typography variant='body2'>{item.name}</Typography>
						<Typography>
							<Button
								component='a'
								href={getGoogleMapLink(
									String(item?.coordinate?.x ?? '0'),
									String(item?.coordinate?.y ?? '0')
								)}
								target='_blank'
								variant='text'
								sx={{
									p: 0,
									width: 'fit-content',
									color: 'black',
								}}>
								<Typography
									variant='subtitle1'
									sx={{
										textDecoration: 'underline',
										color: theme.palette.neutral[300],
									}}>
									{item?.address}
								</Typography>
							</Button>
						</Typography>
					</Paper>
				))}
			</Stack>
		</Box>
	)
}
