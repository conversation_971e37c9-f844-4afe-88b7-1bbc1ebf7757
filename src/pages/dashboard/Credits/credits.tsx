import { CustomChip, CustomDataGrid, CustomHeader } from '@/components'
import { authAxios } from '@/contexts'
import { ICarbonCredits } from '@/interfaces/credits.type'
import { defaultLimit, defaultPage } from '@/utils/constant'
import { Box, Stack, styled, Typography } from '@mui/material'
import {
	GridColDef,
	GridEventListener,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'
import { FC, useCallback, useMemo, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

interface IProps {}

const stockStatusClass: { [key: string]: string } = {
	Approved: 'approved',
	Pending: 'pending',
	Rejected: 'rejected',
}

export const Credits: FC<IProps> = () => {
	const [searchParams] = useSearchParams()
	const [rowCount, setRowCount] = useState<number>(0)
	const page = searchParams.get('page') || defaultPage
	const limit = searchParams.get('limit') || defaultLimit
	const navigate = useNavigate()

	const getCarbonCredits = useQuery({
		queryKey: ['carbonCredits', page, limit],
		queryFn: async () => {
			try {
				const { data } = await authAxios<ICarbonCredits>(
					`/new/stocks?limit=${limit}&page=${page}`
				)
				setRowCount(data.count || 0)
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
		select(data) {
			return (data?.stocks ?? [])?.map((item, index) => ({
				...item,
				id: index,
			}))
		},
	})

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'stockId',
				headerName: 'Stock ID',
				flex: 0.5,
				minWidth: 100,
			},
			{
				field: 'projectId',
				headerName: 'Project ID',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'internalProjectName',
				headerName: 'Project Name',
				minWidth: 100,
				flex: 1,
			},
			{
				field: 'biocharQuantity',
				headerName: 'Biochar Qty',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value || 0} tonne
					</Typography>
				),
			},
			{
				field: 'carbonCredits',
				headerName: 'Carbon Credits',
				minWidth: 100,
				flex: 1,
				renderCell: (params) =>
					params?.value ? (
						<Typography mt={0.8} variant='subtitle1'>
							{params?.value || 0} tCO<sub>2</sub>
						</Typography>
					) : (
						<Typography variant='subtitle1'>-</Typography>
					),
			},
			{
				field: 'cropName',
				headerName: 'Crop',
				minWidth: 100,
				flex: 1,
				renderCell: (params) =>
					params?.value ? (
						<Typography variant='subtitle1'>{params?.value}</Typography>
					) : (
						<Typography>-</Typography>
					),
			},
			{
				field: 'sinkId',
				headerName: 'Sink ID',
				minWidth: 100,
				flex: 1,
				renderCell: (params) =>
					params?.value ? (
						<Typography variant='subtitle1'>{params?.value}</Typography>
					) : (
						<Typography variant='subtitle1'>-</Typography>
					),
			},
			// {
			// 	field: 'blendingMatrixId',
			// 	headerName: 'Matrix ID',
			// 	minWidth: 100,
			// 	flex: 1,
			// 	renderCell: (params) =>
			// 		params?.value ? (
			// 			<Stack>
			// 				{params?.value?.map((item: string) => (
			// 					<Typography key={item} variant='subtitle1'>
			// 						{item}
			// 					</Typography>
			// 				))}
			// 			</Stack>
			// 		) : (
			// 			<Typography variant='subtitle1'>-</Typography>
			// 		),
			// },
			{
				field: 'stockCreatedAt',
				headerName: 'Created At',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{params?.value ? (
							format(params?.value, 'd MMM yyyy')
						) : (
							<Stack>-</Stack>
						)}
					</Typography>
				),
			},
			{
				field: 'status',
				headerName: 'Status',
				minWidth: 100,
				flex: 1,
				renderCell: (params) => (
					<CustomChip
						label={params.value}
						appliedClass={stockStatusClass[params.value]}
					/>
				),
			},
		],
		[]
	)

	const handleRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => {
			navigate(`stocks/${params?.row?.stockId}`)
		},
		[navigate]
	)

	return (
		<StyledContainer>
			<Box className='header'>
				<CustomHeader
					showBottomBorder={true}
					heading='Carbon Credits'
					showButton={false}
				/>
			</Box>
			<Stack className='container'>
				<CustomDataGrid
					showPagination
					columns={columns}
					rows={getCarbonCredits?.data || []}
					rowCount={rowCount}
					onRowClick={handleRowClick}
					loading={getCarbonCredits?.isLoading}
					// headerComponent={<HeaderComponent />}
				/>
			</Stack>
		</StyledContainer>
	)
}

// const HeaderComponent = () => {
// 	const [searchParams, setSearchParams] = useSearchParams()
// 	const paramStatus = searchParams.get('status') || 'all'
// 	const paramStockOrSink = searchParams.get('stockOrSink') || 'all'
// 	const status = [
// 		{ label: 'All', value: 'all' },
// 		{ label: 'Completed', value: 'completed' },
// 		{ label: 'Pending', value: 'pending' },
// 		{ label: 'Rejected', value: 'rejected' },
// 	]
// 	const stockOrSink = [
// 		{ label: 'All', value: 'all' },
// 		{ label: 'Stock', value: 'stock' },
// 		{ label: 'Sink', value: 'sink' },
// 	]

// 	const handleSelectChange = useCallback(
// 		(e: SelectChangeEvent<unknown>, key: 'status' | 'stockOrSink') => {
// 			const value = e.target.value as string
// 			setSearchParams(
// 				(prev) => {
// 					if (value === 'all') {
// 						prev.delete(key)
// 					} else {
// 						prev.set(key, value)
// 					}
// 					return prev
// 				},
// 				{ replace: true }
// 			)
// 		},
// 		[setSearchParams]
// 	)

// 	return (
// 		<Stack className='grid-header-component' columnGap={1}>
// 			<QueryInput
// 				queryKey='search'
// 				label='Search'
// 				name='search'
// 				placeholder='Search'
// 				className='search-textFiled'
// 				InputProps={{
// 					startAdornment: <GridSearchIcon />,
// 				}}
// 			/>
// 			<FormControl className='form-controller'>
// 				<InputLabel id='tagLabel'>Status</InputLabel>
// 				<Select
// 					labelId='tagLabel'
// 					label='Status'
// 					value={paramStatus}
// 					onChange={(e) => handleSelectChange(e, 'status')}>
// 					{status.map((item) => (
// 						<MenuItem key={item.label} value={item.value}>
// 							{item.label}
// 						</MenuItem>
// 					))}
// 				</Select>
// 			</FormControl>

// 			<FormControl className='form-controller'>
// 				<InputLabel id='statusLabel'>Stock/Sink</InputLabel>
// 				<Select
// 					labelId='statusLabel'
// 					label='Stock/Sink'
// 					value={paramStockOrSink}
// 					onChange={(e) => handleSelectChange(e, 'stockOrSink')}>
// 					{stockOrSink.map((item) => (
// 						<MenuItem key={item.value} value={item.value}>
// 							{item.label}
// 						</MenuItem>
// 					))}
// 				</Select>
// 			</FormControl>
// 			<FormControl className='form-controller'>
// 				<InputLabel id='statusLabel'>Matrix ID</InputLabel>
// 				<Select labelId='statusLabel' label='Stock/Sink'>
// 					<MenuItem value={'All'}>All</MenuItem>
// 					<MenuItem value={'stock'}>Stock</MenuItem>
// 					<MenuItem value={'sink'}>Sink</MenuItem>
// 				</Select>
// 			</FormControl>
// 			<FormControl className='form-controller'>
// 				<InputLabel id='statusLabel'>Crop</InputLabel>
// 				<Select labelId='statusLabel' label='Stock/Sink'>
// 					<MenuItem value={'All'}>All</MenuItem>
// 					<MenuItem value={'stock'}>Stock</MenuItem>
// 					<MenuItem value={'sink'}>Sink</MenuItem>
// 				</Select>
// 			</FormControl>
// 		</Stack>
// 	)
// }

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(4),
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		'.grid-header-component': {
			flexDirection: 'row',
			alignItems: 'center',
			paddingLeft: theme.spacing(1.875),

			'.search-textFiled': {
				minWidth: 247,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(14),
				width: '100%',
				'.MuiOutlinedInput-notchedOutline': {
					borderRadius: theme.spacing(1.25),
				},
			},
		},
	},
}))
