import {
	But<PERSON>,
	<PERSON>ack,
} from '@mui/material'
import {
	FormProvider,
	useFieldArray,
	useForm,
	useFormContext,
} from 'react-hook-form'
import {
	multiplecertificatesSchema,
	TAddMultipleCertification,
} from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'
import React, { useCallback } from 'react'
import { useParams } from 'react-router-dom'
import { LoadingButton } from '@mui/lab'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { ILabelWithValue } from '@/types'
import { Add } from '@mui/icons-material'
import { CertificateTypeEnum, certificateTypeList } from '@/utils/constant'
import { TCertificateDetails } from '@/interfaces'
import { RenderComponentForCertificates } from './RenderComponentForCertificates'

const initialValues = {
	certificationType: '',
	certificationBodyName: '',
	certificateId: '',
	certificateFile: '',
}
const initialValuesNew = {
	certificates: [initialValues],
}

type TProps = {
	onClose: () => void
	certificates?: TCertificateDetails[]
}

export const AddMultipleCertificates: React.FC<TProps> = ({
	onClose,
	certificates,
}) => {
	const { projectId } = useParams()
	const queryClient = useQueryClient()
	const form = useForm<TAddMultipleCertification>({
		defaultValues: initialValuesNew,
		mode: 'all',
		resolver: yupResolver<TAddMultipleCertification>(
			multiplecertificatesSchema
		),
	})
	const { control, handleSubmit } = form

	const { fields, append } = useFieldArray({
		control,
		name: 'certificates',
	})

	const handleAdd = useCallback(() => {
		append(initialValues)
	}, [append])

	const addCertificationToProject = useMutation({
		mutationKey: ['addCertificationToProject', projectId],
		mutationFn: async (values: any) => {
			const { certificationDate, ...rest } = values
			const payload = {
				certificationDate: certificationDate
					? new Date(certificationDate)
					: undefined,
				...rest,
			}
			const certificates = payload?.certificates?.map((certificate: any) => {
				return {
					type: certificate.certificationType,
					bodyName: certificate.certificationBodyName,
					expiryDate: null,
					issueDate: certificate?.certificationDate,
					certificateId: certificate?.certificateId,
					file: certificate.certificateFile,
				}
			})
			const { data } = await authAxios.put(`/internal-project/${projectId}`, {
				certificates,
			})
			return data
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['internalProjectDetails'] })
			onClose()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})

	const handleFormSubmit = useCallback(
		(values: TAddMultipleCertification) => {
			addCertificationToProject.mutate(values)
		},
		[addCertificationToProject]
	)

	return (
		<Stack gap={2}>
			<FormProvider {...form}>
				{fields?.map((_, index) => {
					return (
						<AddCertificateFields
							fieldIndex={index}
							options={
								certificateTypeList.filter(
									(item) =>
										!certificates?.find(
											(i) => i.certificate?.type === item.value
										)
								) as ILabelWithValue[]
							}
						/>
					)
				})}
				<Button
					variant={'text'}
					startIcon={<Add fontSize='small' />}
					sx={{
						justifyContent: 'flex-end',
					}}
					onClick={handleAdd}>
					Add Certificates
				</Button>
				{form.getValues('certificates')?.length ? (
					<LoadingButton
						variant='contained'
						fullWidth
						onClick={handleSubmit(handleFormSubmit)}
						loading={addCertificationToProject.isPending}
						disabled={addCertificationToProject.isPending}>
						Add
					</LoadingButton>
				) : null}
			</FormProvider>
		</Stack>
	)
}

interface IProps {
	fieldIndex: number
	options?: ILabelWithValue[]
}
const AddCertificateFields = ({ fieldIndex, options }: IProps) => {
	const {
		register,
		watch,
		setValue,
		clearErrors,
		formState: { errors },
		getValues,
	} = useFormContext()

	const fieldArray = [
		{
			id: 'certificationType',
			label: 'Certification Type',
			hidden: false,
			type: 'select',
		},
		{
			id: 'certificationBodyName',
			label: 'Certification Body Name',
			hidden:
				watch(`certificates.${fieldIndex}.certificationType`) !==
				CertificateTypeEnum.CERES,
			type: 'textField',
		},
		{
			id: 'certificationDate',
			label: 'Certification Date',
			hidden:
				watch(`certificates.${fieldIndex}.certificationType`) !==
				CertificateTypeEnum.CERES,
			type: 'datePicker',
		},
		{
			id: 'certificateId',
			label: 'Certificate Id',
			hidden:
				watch(`certificates.${fieldIndex}.certificationType`) !==
				CertificateTypeEnum.CERES,
			type: 'textField',
		},
		{
			id: 'certificateFile',
			label: 'Certificate File',
			hidden: false,
			type: 'fileUploader',
		},
	]
	return (
		<>
			{fieldArray.map(
				(field) =>
					!field?.hidden && (
						<RenderComponentForCertificates
							{...field}
							fieldIndex={fieldIndex}
							register={register}
							setValue={setValue}
							clearErrors={clearErrors}
							errors={errors}
							getValues={getValues}
							options={options}
							key={field.id}
						/>
					)
			)}
		</>
	)
}
