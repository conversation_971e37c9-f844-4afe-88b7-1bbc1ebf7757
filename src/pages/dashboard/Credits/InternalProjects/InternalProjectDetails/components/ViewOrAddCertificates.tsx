import { NoD<PERSON>, TrainingProofRenderer } from '@/components'
import { IMedia, TCertificateDetails } from '@/interfaces'
import { certificateTypeList, dateFormats } from '@/utils/constant'
import { Add, Close } from '@mui/icons-material'
import { Button, IconButton, Stack, styled, Typography } from '@mui/material'
import { format } from 'date-fns'
import { FC, useState } from 'react'
import { AddCertificates } from './AddCertificates'
import { AddMultipleCertificates } from './AddMultipleCertificates'

type TModalViewType = 'view' | 'upload'
type TModalType = 'project' | 'stock'

type TProps = {
	onClose: () => void
	mode?: TModalViewType
	modal?: TModalType
	certificates?: TCertificateDetails[]
	stockId?: string
}

const showTitle: { [key: string]: string } = {
	view: 'Certification Details',
	upload: 'Add Certification',
}

export const ViewOrAddCertificates: FC<TProps> = ({
	onClose,
	mode = 'view',
	modal = 'project',
	certificates,
	stockId,
}) => {
	const [viewMode, setViewMode] = useState<TModalViewType>(mode)

	return (
		<StyledStack>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>{showTitle[viewMode]}</Typography>
					<Stack direction='row' columnGap={2} alignItems='center'>
						{viewMode === 'view' && modal === 'stock' && (
							<Button
								onClick={() => setViewMode('upload')}
								startIcon={<Add fontSize='small' />}
								sx={{ color: 'common.black' }}>
								Add
							</Button>
						)}
						<IconButton onClick={onClose}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>
			</Stack>
			<Stack className='container'>
				<RenderComponent
					mode={viewMode}
					onClose={onClose}
					certificates={certificates}
					modal={modal}
					stockId={stockId}
				/>
				{viewMode === 'view' && (certificates?.length ?? 0) < certificateTypeList.length && (
					<Button
						variant={'text'}
						onClick={() => setViewMode('upload')}
						startIcon={<Add fontSize='small' />}
						sx={{
							justifyContent: 'flex-end',
						}}>
						Add Certificate
					</Button>
				)}
			</Stack>
		</StyledStack>
	)
}

const RenderComponent: FC<TProps> = ({
	onClose,
	mode,
	certificates,
	modal,
	stockId,
}) => {
	switch (mode) {
		case 'view':
			return <ViewCertificates certificates={certificates ?? []} />
		case 'upload':
			return stockId ? (
				<AddCertificates
					onClose={onClose}
					submitFor={modal}
					stockId={stockId}
				/>
			) : (
				<AddMultipleCertificates certificates={certificates ?? []} onClose={onClose} />
			)

		default:
			return null
	}
}

const ViewCertificates: FC<Pick<TProps, 'certificates'>> = ({
	certificates,
}) => {
	if (certificates?.length === 0) return <NoData />
	return (
		<Stack rowGap={6}>
			{certificates?.map((certificate) => (
				<Stack key={certificate?.certificateId} rowGap={3}>
					{!!certificate?.certificate?.type && <TagComponent
						label='Certification Type Name'
						value={certificateTypeList.filter((item) => item.value === certificate?.certificate?.type)?.[0]?.label}
					/>}
					{!!certificate?.certificationBodyName && <TagComponent
						label='Certification Body Name'
						value={certificate?.certificationBodyName}
					/>}
					{!!certificate?.certificateId && <TagComponent
						label='Certificate Id'
						value={certificate?.certificateId}
					/>}
					{!!certificate?.certificationDate && <TagComponent
						label='Certification Date'
						value={
							certificate?.certificationDate
								? format(certificate?.certificationDate, dateFormats.dd_MM_yyyy)
								: 'Not Mentioned'
						}
					/>}
					<Stack rowGap={3}>
						<Typography className='font_size_14 font_weight_600'>
							Certificate:
						</Typography>
						<TrainingProofRenderer
							viewMode='table'
							hideTitle
							showInRow
							componentSize={40}
							media={[
								{
									...certificate?.certificate,
									fileName: certificate?.certificate?.path ?? '',
									path: certificate?.certificate?.path ?? '',
									url: certificate?.certificate?.url ?? '',
									type: certificate?.certificationBodyName as IMedia['type'] ?? 'image',
									id:certificate?.certificate?.path ?? ''
								},
							]}
						/>
					</Stack>
				</Stack>
			))}
		</Stack>
	)
}

const TagComponent: FC<{
	label: string
	value: string | number
	className?: string
}> = ({ label, value, className }) => {
	return (
		<Stack className={`tag_component ${className}`}>
			<Typography className='font_size_14 font_weight_600'>{label}:</Typography>
			<Typography className='font_size_14 first_letter_capitalize'>
				{value}
			</Typography>
		</Stack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		gap: theme.spacing(4),
		'.formcontrol': {
			gap: theme.spacing(0.6),

			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
		},
	},
	'.tag_component': {
		flexDirection: 'row',
		columnGap: 5,
		alignItems: 'center',
	},
	'.font_size_14': {
		fontSize: theme.typography.subtitle2.fontSize,
	},
	'.font_weight_600': {
		fontWeight: theme.typography.caption.fontWeight,
	},
}))
