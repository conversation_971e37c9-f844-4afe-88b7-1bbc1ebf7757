import { CertificateTypeEnum } from '@/utils/constant'
import * as Yup from 'yup'

export const addCertification = Yup.object({
	internalProjectCertificateId: Yup.string().required(
		'Please select Certificate type'
	),
	certificationBodyName: Yup.string().required(
		'Please enter certification Body Name'
	),
	certificationDate: Yup.date().required('Please enter certification Date'),
	certificateId: Yup.string().required('Please enter certificate Id'),
	certificateFile: Yup.string().required('Please enter certificate File'),
	certificateUrl: Yup.string(),
})

export const addMultipleCertification = Yup.object({
	certificationType: Yup.string().required('Please select certification type'),
	certificationBodyName: Yup.string().when(['certificationType'], {
		is: (certificationType: string) =>
			certificationType === CertificateTypeEnum.CERES,
		then: (schema) => schema.required('Please enter certification Name'),
		otherwise: (schema) => schema,
	}),
	certificationDate: Yup.date().when(['certificationType'], {
		is: (certificationType: string) =>
			certificationType === CertificateTypeEnum.CERES,
		then: (schema) => schema.required('Please enter certification Date'),
		otherwise: (schema) => schema,
	}),
	certificateId: Yup.string().when(['certificationType'], {
		is: (certificationType: string) =>
			certificationType === CertificateTypeEnum.CERES,
		then: (schema) => schema.required('Please enter certificate Id'),
		otherwise: (schema) => schema,
	}),
	certificateFile: Yup.string().required('Please enter certificate File'),
})

export const uploadSinkFileScehma = Yup.object({
	uploadId: Yup.string().required('Please upload File'),
	description: Yup.string().required('Please enter Description'),
})
export const multiplecertificatesSchema = Yup.object({
	certificates: Yup.array()
		.of(addMultipleCertification)
		.required('Please enter certificate'),
})

export type TAddCertification = Yup.InferType<typeof addCertification>
export type TAddMultipleCertification = Yup.InferType<
	typeof multiplecertificatesSchema
>
export type TAddMultipleCertificateBody = Yup.InferType<
	typeof addMultipleCertification
>
export type TUploadSinkFile = Yup.InferType<typeof uploadSinkFileScehma>
