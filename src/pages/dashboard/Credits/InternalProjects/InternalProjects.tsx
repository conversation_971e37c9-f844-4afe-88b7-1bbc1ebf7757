import {
	ActionInformationDrawer,
	AddInternalProject,
	CustomDataGrid,
	CustomHeader,
	QueryInput,
} from '@/components'
import { authAxios } from '@/contexts'
import { IInternalProjectList } from '@/interfaces'
import { defaultLimit, defaultPage, longWordMaxLimit } from '@/utils/constant'
import { getSerialNumber } from '@/utils/helper'
import { Add } from '@mui/icons-material'
import { Box, Button, Stack, styled, Tooltip, Typography } from '@mui/material'
import {
	GridColDef,
	GridEventListener,
	GridSearchIcon,
	GridValidRowModel,
} from '@mui/x-data-grid'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useMemo, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'

export const InternalProjects = () => {
	const [searchParams] = useSearchParams()
	const page = searchParams.get('page') || defaultPage
	const limit = searchParams.get('limit') || defaultLimit
	const searchText = searchParams.get('search') || ''
	const navigate = useNavigate()
	const [isActionInfoDrawer, setIsActionInfoDrawer] = useState<boolean>(false)

	const getProjectList = useQuery({
		queryKey: ['internalProjects', page, limit, searchText],
		queryFn: async () => {
			try {
				const { data } = await authAxios<IInternalProjectList>(
					`/internal-project?limit=${limit}&page=${page}&search=${searchText}`
				)
				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
	})

	const columns: GridColDef<GridValidRowModel>[] = useMemo(
		() => [
			{
				field: 'id',
				headerName: 'S. No',
				flex: 0.5,
				minWidth: 80,
				renderCell: (params) => (
					<Typography variant='subtitle1'>
						{getSerialNumber(params, Number(limit))}
					</Typography>
				),
			},
			{
				field: 'name',
				headerName: 'Project Name',
				flex: 1,
				minWidth: 180,
				renderCell: (params) => (
					<Typography className='first_letter_capitalize' variant='subtitle1'>
						{params?.value}
					</Typography>
				),
			},
			{
				field: 'csinkManagerName',
				headerName: 'Csink Manager',
				flex: 1,
				minWidth: 180,
			},
			{
				field: 'projectId',
				headerName: 'Project ID',
				flex: 1,
				minWidth: 150,
				renderCell: (params) => (
					<Typography variant='subtitle1'>{params?.value || '-'}</Typography>
				),
			},
			{
				field: 'comments',
				headerName: 'Comments',
				flex: 1,
				minWidth: 130,
				renderCell: (params) => (
					<Tooltip
						title={params?.row?.comments}
						componentsProps={{
							tooltip: {
								sx: {
									backgroundColor: 'common.white',
									boxShadow: '0px 4px 4px rgba(0, 0, 0, 0.25)',
									color: 'black',
									fontSize: '14px',
									padding: '8px',
									wordWrap: 'break-word',
								},
							},
						}}>
						<Typography
							variant='subtitle1'
							style={{
								display: 'inline-block',
								wordWrap: 'break-word',
								textOverflow:
									(params?.row?.comments?.length ?? 0) > longWordMaxLimit
										? 'ellipsis'
										: 'clip',
							}}>
							{params?.row?.comments?.length > longWordMaxLimit
								? `${params?.row?.comments?.substring(0, longWordMaxLimit)}...`
								: params?.row?.comments}
						</Typography>
					</Tooltip>
				),
			},
		],
		[page, limit]
	)

	const HeaderEndComponent = () => {
		return (
			<Stack direction='row' alignItems='center' gap={2}>
				<Button
					variant='contained'
					color='primary'
					startIcon={<Add />}
					onClick={() => setIsActionInfoDrawer(true)}>
					Add Project
				</Button>
			</Stack>
		)
	}

	const handleRowClick: GridEventListener<'rowClick'> = useCallback(
		(params) => navigate(`${params?.id}/details`),
		[navigate]
	)

	return (
		<>
			<ActionInformationDrawer
				open={isActionInfoDrawer}
				onClose={() => setIsActionInfoDrawer(false)}
				anchor='right'
				component={
					<AddInternalProject
						setIsActionInfoDrawer={setIsActionInfoDrawer}
						cb={() => getProjectList?.refetch()}
					/>
				}
			/>

			<StyledContainer>
				<Box className='header'>
					<CustomHeader
						showBottomBorder={true}
						heading='Projects'
						showButton={false}
					/>
				</Box>
				<Stack className='container'>
					<CustomDataGrid
						showPagination
						columns={columns}
						rows={getProjectList?.data?.internalProjectList ?? []}
						rowCount={getProjectList?.data?.count}
						headerEndComponent={<HeaderEndComponent />}
						onRowClick={handleRowClick}
						headerComponent={
							<Stack className='grid-header-component'>
								<QueryInput
									queryKey='search'
									label='Search'
									name='search'
									placeholder='Search'
									className='search-textFiled'
									setPageOnSearch
									InputProps={{
										startAdornment: <GridSearchIcon />,
									}}
								/>
							</Stack>
						}
						loading={getProjectList?.isLoading}
					/>
				</Stack>
			</StyledContainer>
		</>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(4),
	'.header': {
		padding: theme.spacing(4, 0),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		'.grid-header-component': {
			paddingLeft: theme.spacing(1.875),
			flexDirection: 'row',
			alignItems: 'center',
			'.search-textFiled': {
				minWidth: 347,
				width: '100%',
				'.MuiInputBase-root': {
					height: theme.spacing(4.5),
					borderRadius: theme.spacing(1.25),
				},
			},
			'.form-controller': {
				margin: theme.spacing(0.125),
				minWidth: theme.spacing(14),
				width: '100%',
				'.MuiOutlinedInput-notchedOutline': {
					borderRadius: theme.spacing(1.25),
				},
			},
		},
	},
}))
