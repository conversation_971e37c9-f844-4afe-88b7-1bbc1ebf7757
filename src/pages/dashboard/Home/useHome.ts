import { authAxios, useAuthContext } from '@/contexts'
import {
	GetArtisanProsAndCSinkNetworksResponse,
	IQuantityData,
	NetworkEnumForHomePage,
	SiteKilnResponse,
} from '@/types'
import { userRoles } from '@/utils/constant'
import { useQuery } from '@tanstack/react-query'
import { useCallback, useMemo, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

export const useHome = () => {
	const { userDetails } = useAuthContext()
	const [searchParams, setSearchParams] = useSearchParams()
	const paramsSubNetwork = searchParams.get('subNetwork') || ''
	const paramsBa = searchParams.get('baId') || ''
	const paramsMultipleBa = searchParams.getAll('biomassAggregatorIds') || ''
	const networkId = searchParams.get('networkIds') || ''
	const siteId = searchParams.get('siteId') || ''
	const [mapCenter, setMapCenter] = useState({ lat: 0, lng: 0 })
	const fetchQuantityData = useQuery({
		queryKey: [
			'quantityData',
			paramsSubNetwork,
			siteId,
			networkId,
			paramsMultipleBa,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				subNetwork: paramsSubNetwork == 'all' ? '' : paramsSubNetwork,
				networkIds: networkId,
				siteIds: siteId,
				biomassAggregatorIds: paramsMultipleBa?.map((id) => id).join(','),
			})
			const { data } = await authAxios.get<IQuantityData>(
				`new/biochar-details?${queryParams.toString()}`
			)
			return data
		},
	})
	type AssetsData = {
		sites: number
		kilns: number
		farmers: number
		artisanPros: number
		cSinkNetworks: number
		biomassCollected: number
	}
	const extractLatLongFromCoordinateString = (coord: string) => {
		const [x, y] = coord.split(',')
		const lat = Number(x.split('(')[1] || 0)
		const lng = Number(y.split(')')[0] || 0)
		return {
			lat,
			lng,
		}
	}

	const fetchSites = useQuery({
		queryKey: [
			'fetchSites',
			paramsBa,
			paramsSubNetwork,
			paramsMultipleBa,
			networkId,
			siteId,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				subNetwork: paramsSubNetwork == 'all' ? '' : paramsSubNetwork,
				biomassAggregatorId: paramsBa,
				biomassAggregatorIds: paramsMultipleBa?.map((id) => id).join(','),
			})
			const { data } = await authAxios.get<SiteKilnResponse>(
				`/new/sites-kilns?${queryParams.toString()}`
			)
			return data
		},
		select: (data) => {
			const filteredSites = (data?.siteKilns ?? []).filter((item) => {
				const id = item?.isArtisan ? item?.siteId : item?.kilnId
				const matchesSiteId = siteId ? id === siteId : true
				//if the siteId is selected then filter the sites, else return all

				const matchesNetworkId = networkId
					? item?.isArtisan
						? item?.artisanProId === networkId
						: item?.csinkNetworkId === networkId
					: true
				//similarly filtering on the basis of selected network

				return matchesSiteId && matchesNetworkId
			})
			return filteredSites?.map((item) => ({
				id: (item?.isArtisan ? item?.siteId : item?.kilnId) || '',
				name: (item?.isArtisan ? item?.siteName : item?.kilnName) || '',
				position: extractLatLongFromCoordinateString(
					(item?.isArtisan ? item?.siteLocation : item?.kilnLocation) || '(0,0)'
				),
			}))
		},
	})

	const fetchAssets = useQuery({
		queryKey: ['allAssets', siteId, networkId, paramsMultipleBa],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				networkIds: networkId,
				siteIds: siteId,
				biomassAggregatorIds: paramsMultipleBa?.map((id) => id).join(','),
			})
			return authAxios.get<AssetsData>(`/new/asset?${queryParams.toString()}`)
		},
		select: ({ data }) => {
			return data
		},
		enabled: [
			userRoles.Admin,
			userRoles.CsinkManager,
			userRoles.BiomassAggregator,
			userRoles.ArtisanPro,
			userRoles.artisanProNetworkManager,
			userRoles.cSinkNetwork,
		].includes(userDetails?.accountType as userRoles),
	})

	const fetchArtisanProsAndCSinkNetworks = useQuery({
		queryKey: [
			'fetchArtisanProsAndCSinkNetworks',
			paramsBa,
			paramsSubNetwork,
			paramsMultipleBa,
			networkId,
			siteId,
		],
		queryFn: async () => {
			const queryParams = new URLSearchParams({
				subNetwork: paramsSubNetwork == 'all' ? '' : paramsSubNetwork,
				biomassAggregatorId: paramsBa,
				networkIds: networkId,
				siteIds: siteId,
				biomassAggregatorIds: paramsMultipleBa?.map((id) => id).join(','),
			})
			const { data } =
				await authAxios.get<GetArtisanProsAndCSinkNetworksResponse>(
					`/new/networks?${queryParams.toString()}`
				)
			return data
		},
		select: (data) => {
			return (data?.networks ?? [])
				.filter((item) => {
					const hasLocation = !!item?.location
					const matchesNetworkId = networkId ? item?.id === networkId : true
					return hasLocation && matchesNetworkId
				})
				.map((item) => ({
					id: item?.id,
					name: item?.name,
					position: extractLatLongFromCoordinateString(item?.location),
				}))
		},
	})
	const subTabOptions = useMemo(
		() => [
			{ label: 'All', value: NetworkEnumForHomePage.all },
			...(![userRoles.cSinkNetwork].includes(
				userDetails?.accountType as userRoles
			)
				? [
						{
							label: 'Artisan Pro',
							value: NetworkEnumForHomePage.artisanPro,
						},
				  ]
				: []),
			...(![userRoles.ArtisanPro, userRoles.artisanProNetworkManager].includes(
				userDetails?.accountType as userRoles
			)
				? [
						{
							label: 'C Sink Network',
							value: NetworkEnumForHomePage.network,
						},
				  ]
				: []),
		],
		[userDetails?.accountType]
	)

	const handleBa = useCallback(
		(ids: string[]) => {
			const nsp = new URLSearchParams(searchParams)
			nsp.delete('networkId')
			if (ids.length) {
				nsp.set('biomassAggregatorIds', ids.join(','))
			} else {
				nsp.delete('biomassAggregatorIds')
			}
			setSearchParams(nsp, { replace: true })
		},
		[searchParams, setSearchParams]
	)

	const handleSubNetwork = useCallback(
		(value: NetworkEnumForHomePage) => {
			setSearchParams(
				(prev) => {
					if (value === 'all') {
						prev.delete('subNetwork')
					} else {
						prev.set('subNetwork', value)
					}
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)

	const handleChipFilter = useCallback(
		(key: string) => {
			setSearchParams(
				(prev) => {
					if (key === 'withLabel') {
						if (prev.get(key) === 'false') {
							prev.delete(key)
						} else {
							prev.set(key, 'false')
						}
						return prev
					}
					if (prev.get(key) === 'true') {
						prev.delete(key)
					} else {
						prev.set(key, 'true')
					}
					return prev
				},
				{ replace: true }
			)
		},
		[setSearchParams]
	)
	const currentLocationQuery = useQuery({
		queryKey: ['currentLocationQuery'],
		queryFn: () => {
			return new Promise((resolve, reject) => {
				navigator.geolocation.getCurrentPosition(
					(position) => {
						const coords = {
							lat: position.coords.latitude,
							lng: position.coords.longitude,
						}
						setMapCenter(coords)
						resolve(coords)
					},
					(error) => {
						reject(error)
					}
				)
			})
		},
	})

	return {
		fetchQuantityData,
		fetchSites,
		handleBa,
		fetchAssets,
		searchParams,
		setSearchParams,
		fetchArtisanProsAndCSinkNetworks,
		handleSubNetwork,
		subTabOptions,
		paramsBa,
		paramsMultipleBa,
		handleChipFilter,
		mapCenter,
		currentLocationQuery,
	}
}
