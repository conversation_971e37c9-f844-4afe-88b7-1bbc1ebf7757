import { Nullable } from '@/types'
import { IImage } from './image.type'
import { ILocation } from './farmFarmer.type'

type BagData = {
	bagCount: number
	bagId: string
	bagName: string
	biocharQuantity: number
	createdAt: string
	farmId: string | null
	farmerId: string | null
	farmerName: string | null
	fieldSize: number | null
	fieldSizeUnit: string | null
	fpuId: string
	fpuName: string
	id: string
	images: IMediaWithDeleteParams[]
	isMixingApplication: boolean
	landmark: string | null
	mixTypeId: string
	mixTypeName: string
}

export interface IBatchDetails {
	kilnProcessDetail: IKIlnProcessDetails
	kilnProcessBiomass: KilnProcessBiomass[]
	kilnProcessTemperature: KilnProcessTemperature[]
	mixedPackedInventory: MixedPackedInventory[]
	mixingDetails: MixingDetails[]
	distributedInventory: IDistribution[]
	measuringContainers: IMeasuringContainer[]
	samplingContainer: ISamplingContainer
	totalPackedQuantity: number
	applications: BagData[]
}

export interface IMeasuringContainer {
	id: string
	shortName: string
	diameter: number
	diameterUnit: string
	height: number
	heightUnit: string
	imageURL: ImageURL
	imageURLs: ImageURL[]
	createdAt: Date
	volume: number
	count: number
	length: null
	lengthUnit: string
	breadth: null
	breadthUnit: string
	shape: string
	name: null
	isPartialFilled: boolean
	measuringContainerHeight: number
	upperSurfaceDiameter: null
	upperSurfaceDiameterUnit: string
	lowerSurfaceDiameter: null
	lowerSurfaceDiameterUnit: string
}

export interface ImageURL {
	id: string
	url: string
	path: string
}

export interface KilnProcessBiomass {
	id: string
	kilnDropTime: Date
	biomassQuantity: number
	farmer: Farmer
	crop: Crop
	totalProcessBiomassQuantity?: number
	processBiomassRelation: ProcessBiomassRelation[]
	fpu: IFPU
	dropImages: IMediaWithDeleteParams[]
}

export interface Crop {
	id: string
	cropName: string
}

export interface Farmer {
	id: string
	name: string
	number: string
}

export interface ProcessBiomassRelation {
	processDrop: ProcessDrop
	biomassAddition: BiomassAddition
}

export interface BiomassAddition {
	id: string
	biomassQuantity: number
	dropTime: Date
	moistureArray: number[]
	moistureImages: IMediaWithDeleteParams[]
}

export interface ProcessDrop {
	id: string
	biomassAddedAt: Date
	biomassQuantity: number
}

export interface KilnProcessTemperature {
	temperature: number
	temperatureUnit: string
	images: IMediaWithDeleteParams[]
}

export interface MixedPackedInventory {
	packagingName: string
	packagingType: string
	packagingCreatedAt: Date
	totalActualQty: number
	inventoryList: InventoryList[]
	images: IMediaWithDeleteParams[]
}

export interface MixingDetails {
	id?:string
	bagCount: number
	bags: {
		id: string
		name: string
		images: null
		inventoryCount: number
	}[]
	biocharQuantity: number
	createdAt: Date
	mixType: string
	images: IMediaWithDeleteParams[]
	videos: IMediaWithDeleteParams[]
}

export interface InventoryList {
	id: string
	quantity: number
	packedAt: Date
	displayId: string
}

export interface IKIlnProcessDetails {
	id: string
	actualCarbonCredits?: number
	reason?: string
	biomassAggregatorName: string
	biomassAggregatorShortName: string
	networkName: string
	networkShortName: string
	createdAt: string
	endTime: string
	stackImagesAddedAt: string
	cropName: string
	carbonPercentage: null
	carbonCredits: null
	co2Emission: null
	methaneEmission: null
	biomassQty: number
	density: null
	shortTermCarbonSink: null
	kilnName: string
	processShortName: string
	status: string
	statusAccessedByName: string
	statusAccessedTime: Date
	artisanProName: string
	artisanProShortName: string
	bioCharQty: number
	// processImages: IMediaWithDeleteParams[]
	// processVideos: IMediaWithDeleteParams[]
	processImagesAndVideos: IMediaWithDeleteParams[]
	stackImages: IMediaWithDeleteParams[]
	artisanProId: string
	siteId: string
	kilnCoordinate?: ILocation | null
	kilnAddress?: string
	siteName: string
	kilnId: string
	networkId: string
	kilnVolume: number
}

export interface ISamplingContainer {
	ShortCode: string
	id: string
	name: string
}
export interface IFPU {
	fpuId: string
	fpuName: string
}

export enum ESupportingDocStep {
	biomassCollection = 'biomassCollection',
	feedStockPrepration = 'feedStockPrepration',
	productionTempMedia = 'productionTempMedia',
	bioProduction = 'bioProduction',
	mixing = 'mixing',
	application = 'application',
}

export interface IDistribution {
	mixTypeName: string
	isOpenDistribution: boolean
	name: string
	number: string
	distributedAt: string
	totalActualQty: number
	inventoryList: IInventoryList[]
	images: IMediaWithDeleteParams[]
}

export interface IInventoryList {
	id: string
	quantity: number
	packedAt: Date
	displayId: string
	distributedAt: Date
	name: string
	number: string
	distributionId: string
}
export type BatchstatusType =
	| 'approved'
	| 'rejected'
	| 'not_assessed'
	| 'started'
	| 'partially-completed'
	| 'admin-rejected'
	| 'admin-approved'
	| 'compensate'
	| null

export interface IMedia extends IImage {
	thumbnailURL?: string
}
export interface IMediaLoading {
	images?: boolean
	docs?: boolean
}
export interface IMediaWithDeleteParams extends IMedia {
	type: 'video' | 'image'
	fileType: 'video' | 'image'
	deletionReason?: Nullable<string>
	deletionStatus?: Nullable<string>
	isProductionMedia?: boolean
	isDistrubtedMedia?: boolean
}

export interface IMarkedDeleteMedia {
	images: IMediaWithDeleteParams[]
	temperatureImages: IMediaWithDeleteParams[]
	stackImages: IMediaWithDeleteParams[]
	videos: IMediaWithDeleteParams[]
	doneImages: IMediaWithDeleteParams[]
}

type IDrawerStateData = {
	sampling?: ISamplingContainer
	containers: IBatchDetails['measuringContainers']
	bioCharQty: IBatchDetails['kilnProcessDetail']['bioCharQty']
	packedQty: IBatchDetails['totalPackedQuantity']
	biomass: number
	networkDetails: {
		artisanProId: string
		siteId: string
		networkId: string
		kilnId: string
	}
}

export type TDrawerState = {
	open: boolean
	type: 'comment' | 'container' | 'biomass'
	data: IDrawerStateData
}

export type TRenderDrawerComponent = Pick<TDrawerState, 'type' | 'data'> & {
	onClose: () => void
}
