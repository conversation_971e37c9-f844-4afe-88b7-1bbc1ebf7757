import { IImage } from './image.type'

export interface IInventories {
	id: string
	displayId: string
	kilnProcessDisplayIds: string[]
	artisanProId: string
	artisanProName: string
	siteId: string
	siteName: string
	siteShortName: string
	csinkNetworkId: string | null
	csinkNetworkName: string | null
	kilnId: string | null
	kilnName: string | null
	kilnShortName: string | null
	biomassAggregatorId: string
	biomassAggregatorName: string
	biomassAggregatorShortName: string
	packedDate: string
	bagType: string
	bagQuantity: number
	bagQuantityUnit: string
	bagMeasuringType: string
	packagingType: string
	bioCharOnly: boolean
	actualQuantity: number
	status: string
}

export interface IBiomassCollectionList {
	id: string
	biomassType: string
	biomassQuantity: number
	vehicleNumber: string
	vehicleType: string
	vehicleFuelType: string
	createdAt: string
	fpuName: string
	images: IImage[]
	farmerId: string | null
	farmerName: string | null
	farmerNumber: string | null
	farmerCountryCode: string | null
	distance: number | null
	processUsedBiomassqty: number | null
	dropTime: string
	siteId: string
	siteName: string
	siteShortName: string
	networkId: string | null
	networkName: string | null
	networkShortName: string | null
	artisanProId: string
	artisanProName: string
	artisanProShortName: string
	kilnName: string | null
	address: string
	biomassAggregatorName: string
	biomassAggregatorShortName: string
	density: number
	cropName: string
}

export interface GetBioCharProducedResponse {
	approvedBiochar: number
	rejectedBiochar: number
	pendingBiochar: number
	approvedBiocharinTonnes: number
	pendingBiocharInTonnes: number
	rejectedBiocharInTonnes: number
}

export interface GetBioCharMixingResponse {
	totalMixedBiochar: number
	totalPackedMixedBiochar: number
	totalUnPackedMixedBiochar: number
	totalPackedDistributedMixedBiochar: number
	totalOpenDistributedMixedBiochar: number
	totalPackedBiocharOnly: number
	totalPackedDistributedBiocharOnly: number
	totalMixedBiocharInTonnes: number
	totalUnPackedMixedBiocharInTonnes: number
	totalPackedMixedBiocharInTonnes: number
	totalOpenDistributedMixedBiocharInTonnes: number
	totalPackedDistributedMixedBiocharInTonnes: number
	totalPackedDistributedBiocharOnlyInTonnes: number
	totalPackedBiocharOnlyInTonnes: number
	unMixedOpenBiochar: number
	unMixedOpenBiocharInTonnes: number
}

export interface GetProductionGraphDataResponse {
	totalBiomass: TotalBiomass[]
	processDetail: ProcessDetail[]
}

export interface ProcessDetail {
	totalBiocharInTonnes: number
	total: number
	totalCarbonCredits: number
	period: string
}

export interface TotalBiomass {
	total: number
	period: string
}

export enum PeriodEnum {
	wtd = 'week',
	mtd = 'month',
	ytd = 'year',
	custom = 'custom',
}

export interface GetBiomassAvailableResponse {
	count: number
	biomassAvailable: BiomassAvailable[]
}

export interface BiomassAvailable {
	id: string
	name: string
	shortName: string
	biomassAggregatorId: string
	biomassAggregatorName: string
	biomassQuantity: number
	isArtisan: boolean
	biomassDetails: IBiomassDetail[]
}

export interface IBiomassDetail {
	biomassId: string
	biomassName: string
	entityId: string
	entityName: string
	biomassQuantity: number
}

export interface GetBiomassOrCropResponse {
	totalBiomassQuantity: number
	crops: CropDetail[]
}

export interface CropDetail {
	cropId: string
	cropName: string
	totalBiomassQuantity: number
	availableBiomassQuantity: number
}
