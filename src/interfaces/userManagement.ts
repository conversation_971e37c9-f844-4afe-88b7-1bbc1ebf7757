export interface IUsersResponse {
	count: number
	users: User[]
}

export interface CertificateDetails {
	deletionReason?: string
	deletionStatus?: string
	fileType?: string
	id?: string
	path?: string
	url?: string
}
export interface User {
	id: string
	name: string
	email: string
	countryCode: string
	number: string
	accountType: string
	profileImageUrl: ProfileImageURL
	csinkManagerId: string
	csinkManagerName: string
	csinkManagerShortName: string
	biomassAggregatorId: string
	biomassAggregatorName: string
	biomassAggregatorShortName: string
	artisanProNetworkId: string
	artisanProNetworkName: string
	artisanProNetworkShortName: string
	artisanProId: string
	artisanProName: string
	artisanProShortName: string
	csinkNetworkId: string
	csinkNetworkName: string
	csinkNetworkShortName: null
	trainingImageUrls: ProfileImageURL[]
	aadhaarImageUrl: ProfileImageURL
	aadhaarNumber: string
	artisanPros: IArtisanPro[]
	pendingUploadsCount?: number
	pendingActionCount?: number
	certificateDetails?: CertificateDetails
	gender?: string
	address?: string
	cropId?: string
	landmark?: string
}

export interface ProfileImageURL {
	id: string
	url: string
	path: string
}

export interface IFarmForUser {
	id: string
	farmLocation: FarmLocation
	landmark: string
	fieldSize: number
	fieldSizeUnit: string
	farmCrops: FarmCrop[]
	farmImages: ProfileImageURL[]
}

export interface FarmCrop {
	id: string
	cropName: string
	createdAt: string
	cropStage: string
}

export interface FarmLocation {
	x: number
	y: number
}

export interface IArtisanPro {
	id: string
	name: string
	shortName: string
}
export enum AddOperatorTypeEnum {
	kiln = 'kiln',
	csink = 'csinkOperator',
	artisanPro = 'artisanPro',
}
export type TModalTypeForUserManagement =
	| 'view'
	| 'add'
	| 'edit'
	| 'promote'
	| 'demote'
