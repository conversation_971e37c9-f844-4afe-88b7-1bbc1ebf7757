import { Nullable } from '@/types'
import { IOperator, ManagerDetail } from './artisanProNetworkDetails.type'
import {
	ImageURL,
	IMeasuringContainer,
	IMediaWithDeleteParams,
} from './batch.type'
import { IImage } from './image.type'
import { IFileData } from './mixing.type'
import { IKiln } from './site.type'

export interface INetwork {
	id: string
	name: string
	shortName: string
	locationName: string
	bighaInHectare: number
	acreInHectare: number
	farmersCount: number
	adminsCount: number
	managerDetails: ManagerDetail[]
	networkKilnCount: number
	managerId: string
	managerName: string
	managerEmail: string
	managerPhoneNumber: string
	managerCountryCode: string
	trainingImageURLs: null
	managerTrained: boolean
	certificateUrl: null
	certificateStatus: string
	biomassAggregatorId: string
	biomassAggregatorName: string
	biomassAggregatorShortName: string
	isCsinkManager: boolean
	kilnCount: number
	farmerCount: number
	operatorCount: number
	farmsCount: number
	containerCount: number
	hasMaxBiocharGenerationLimitReached: boolean
	methaneCompensationStrategy: string
	methaneCompensateType: Nullable<string>
	measuringBagsCount: number
	trained: boolean
	totalBiocharProduced: null
	totalBiocharProducedInTonne: null
	totalBiomass: number
	details: NetworkEntityDetails
	buyerCount: number
	address: string
	footPrint: string
	compensationType: string
	csinkManagerId: Nullable<string>
	operators?: ICsinkFarmer[]
	documentIds: string[]
	documents: Nullable<IImage[]>
	kmlCoordinates: Nullable<string[]>
	location: Nullable<{ x: number; y: number }>
	biomassPreprocessingDetails: TBiomassProcessingDetails
	methaneCompensateStrategies: TMethaneCompensateStrategies[]
	isCsinkManagerSuspended?: boolean
	isBiomassAggregatorSuspended?: boolean
	isCsinkNetworkSuspended?: boolean
}

export type TMethaneCompensateStrategies = {
	id?: string
	biomassName?: Nullable<string>
	biomassId: Nullable<string>
	methaneCompensateType: Nullable<string>
	description: Nullable<string>
	compensateType: Nullable<string>
	documentIds: Nullable<IFileData[]>
	documents?: Nullable<IFileData[]>
}
export type TBiomassProcessingDetails = {
	shreddingStrategy: string
	dryingStrategy: string
	dryingType: string
	shreddingType: string
	dryingDocuments: Nullable<TImage[]>
	shreddingDocuments: Nullable<TImage[]>
}

export type TImage = {
	id: string
	url: string
	fileName: string
	path: string
	deletionStatus: Nullable<string>
	deletionReason: Nullable<string>
}

export interface ICsink {
	count: number
	network: INetwork[]
}

export interface NetworkEntityDetails {
	preferredCrops: PreferredCrop[]
	vehicles: Vehicle[]
	measuringContainer: IMeasuringContainer[]
	otherBuyers: OtherBuyer[]
	bags: Bag[]
	carbonCredits: CarbonCredits[]
	biochar: BioCharDetails[]
	biomass: BiomassDetails[]
	operators: IOperator[]
}

export interface Bag {
	id: string
	name: string
	bagType: string
	quantity: number
	quantityUnit: string
	measuringType: string
	imageURLs: ImageURL[]
	cSinkNetworkId: Nullable<string>
	cSinkNetworkName: Nullable<string>
	artisanProId: Nullable<string>
	artisanProName: Nullable<string>
}

export interface Vehicle {
	id: string
	name: string
	number: null
	type: string
	fuelType: string
	categoryId: string | null
	categoryName: null
	imageURLs: IImage[]
}

export interface OtherBuyer {
	id: string
	name: string
	number: string
	countryCode: string
	pinCode: string
	district: string
	state: string
	postalLocation: string
	latitude: number
	longitude: number
}
export interface PreferredCrop {
	cropId: string
	name: string
	createdAt: Date
	image: ImageURL
}

export interface CarbonCredits extends Pick<PreferredCrop, 'cropId'> {
	cropName: string
	carbonCredits: number
}

export interface BioCharDetails extends Omit<CarbonCredits, 'carbonCredits'> {
	biocharProduced: number
	biocharProducedInTonne: number
}

export interface BiomassDetails
	extends Pick<CarbonCredits, 'cropId' | 'cropName'> {
	biomassAvailable: number
	biomassProduced: number
}
export interface IPackagingBag {
	id: string
	bagType: 'metal' | 'sack' | null
	name: string
	quantity: number
	quantityUnit: string
	imageURLs: IImage[]
	measuringType: string
	inUse: boolean
}
export interface kilnOperatorOrFarmer {
	id: string
	name: string
	phoneNo: string
	countryCode: string
	email: string
	accountType: null
	aadhaarNo?: null
	trainingImageUrls?: null
	language?: string
	certificateStatus?: null
	certificateUrl?: null
	profileImageUrl?: ImageURL
	aadhaarNumber?: null
	aadhaarImageUrl?: null
}
export interface ICsinkFarmer {
	id: string
	name: string
	email: string
	phoneNo: string
	countryCode: string
	siteId: string
	profileImage?: IImage
}
export interface ICsinkOperator {
	id: string
	name: string
	countryCode: string
	number: string
	email: string
	profileImage: IImage | null
	farmers: ICsinkFarmer[]
}

export interface artisianProOperator extends kilnOperatorOrFarmer {}

export interface otherNetworkType {
	count: number
	limit: number
	page: number
	kilnOperatorOrFarmer?: kilnOperatorOrFarmer[]
	artisianProOperators?: artisianProOperator[]
}

export interface IContainerDetails {
	id: string
	ShortName: string
	diameter: number
	diameterUnit: string
	shape: string
	height: number
	length: number
	lengthUnit: string
	lowerSurfaceDiameter?: number
	upperSurfaceDiameter?: number
	breadth: number
	breadthUnit: string
	heightUnit: string
	csinkNetworkID: string
	imageURL: IImage[] | null
	imageURLs?: IImage[] | null
	createdAt: string
	volume: number
	name: string
	inUse: boolean
	upperBase?: number
	lowerBase?: number
	container?: string
	sno?: number | null
	containerShapeName?: string | null
}

export interface ISamplingContainerDetails extends IContainerDetails {
	filled?: boolean
}
export interface IOtherBuyers {
	id?: string
	name?: string
	number?: string
	countryCode?: string
	pinCode?: string
	district?: string
	state?: string
	postalLocation?: string
	latitude?: number
	longitude?: number
}
export interface IVehicle {
	id?: string
	name?: string
	number?: string
	type?: string
	fuelType?: string
	categoryId?: string | null
	categoryName?: string | null
	imageURLs?: IImage[]
	co2Emission?: number
}
export interface IIPreferredBiomass {
	cropId?: string
	name?: string
	createdAt?: string
	image: IImage
}

export interface KilnListResponse {
	count: number
	limit: number
	page: number
	kilns: IKiln[]
}

export interface Kiln {
	id: string
	name: string
	ShortName: string
	address: string
	coordinate: string
	networkId: null
	networkName: null
	siteId: null
	siteName: null
	siteShortName: null
	apId: null
	apName: null
	apShortName: null
	csManagerName: null
	artisianProManagerName: null
	kilnType: null
	baID: string
	baName: null
	baShortName: null
	isCsinkManager: boolean
	createdAt: Date
	kilnShape: string
	upperSurfaceDiameter: null
	upperSurfaceDiameterUnit: null
	lowerSurfaceDiameter: null
	lowerSurfaceDiameterUnit: null
	lowerSide: null
	lowerSideUnit: string
	upperSide: null
	upperSideUnit: string
	volume: null
	volumeUnit: null
	depth: null
	depthUnit: null
	kilnOperators: KilnOperator[]
	imageURLs: ImageURL[]
	biocharQuantity: number
	distributedQuantity: number
	packedQuantity: number
	notAccessedQuantity: null
	approvedQuantity: null
	diameter: null
	diameterUnit: string
	biocharDetails: BiocharDetailsForKiln
	biomassDetails: BiomassDetailForKiln[]
	carbonCredits: number
	cropCarbonCredits: CarbonCredits[]
	cropBiochar: BioCharDetails[]
	longBase?: number
	shortBase?: number
}

export interface BiocharDetailsForKiln {
	biocharQuantity: number
	distributedBiocharQuantity: number
	mixedBiocharQuantity: number
	approvedBiocharQuantity: number
	notAssessedBiocharQuantity: number
	totalBiocharProduced: number
	totalBiocharProducedInTonne: number
}

export interface BiomassDetailForKiln {
	kilnID: string
	cropID: string
	cropName: string
	currentBiomassQuantity: number
}

export interface KilnOperator {
	id: string
	name: string
	phoneNo: string
	countryCode: string
	email: string
	accountType: null
	aadhaarNo: null
	trainingImageUrls: null
	language: string
	certificateStatus: null
	certificateUrl: null
	profileImageUrl: ImageURL
	aadhaarNumber: null
	aadhaarImageUrl: null
	profileImage?: IMediaWithDeleteParams
}

export interface GetFarmerListResponse {
	count: number
	limit: number
	page: number
	totalFarmersAndFarms: TotalFarmersAndFarms
	farmers: FarmerInfo[]
}

export interface FarmerInfo extends Pick<NetworkEntityDetails, 'biochar'> {
	id: string
	name: string
	address: string
	phoneNo: string
	countryCode: string
	email: null
	isOperator: boolean
	farmsCount: number
	cSinkNetworkId: null
	cSinkNetworkName: null
	cSinkNetworkShortCode: null
	artisanProId: null
	artisanProName: null
	artisanProShortCode: null
	kilnNames: null
	flagCount: number
	ProfileImagePath: null
	profileImageURL: string
	BAName: null
	totalBiocharProduced: number
	trainingImageUrls: null
	certificateUrl: null
	certificateStatus: string
	profileImageUrl: null
	aadhaarNumber: null
	aadhaarImageUrl: null
	createdAt: Date
	totalFarmArea: number
	biomassQty: number
	farms: Farm[]
	gender: null
	biomassAggregatorId: null
}

export interface Location {
	x: number
	y: number
}

export interface Farm {
	id: string
	farmArea: null
	farmImages: null
	croppingPattern: string
	location: Location
	landmark: null
	fieldSize: number
	fieldSizeUnit: string
}

export interface TotalFarmersAndFarms {
	totalFarmers: number
	totalFarms: number
}

export interface GetBuyerListResponse {
	count: number
	othersBuyers: OtherBuyer[]
}

// export type IResponseCsinkMixingType = {
// 	count: number
// 	mixingType: ICsinkMixingType[]
// }

export type ICsinkMixingType = {
	id: string
	name: string
	biocharOnly: boolean
	type: string
	otherMixName: Nullable<string>
	fixedRatio: boolean
	density: Nullable<number>
	category: string
}

export type ICsinkApplicationType = {
	id: string
	category: string
	type: string
	csinkId: string
	matrixId: string
}
