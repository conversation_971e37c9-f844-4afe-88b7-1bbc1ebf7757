declare module '@mui/material/styles' {
	interface Palette {
		custom: {
			// add new colors here in future is needed to excellent palette support
			lightest: {
				primary: string
			}
			red: {
				300: string
				400: string
				500: string
				600: string
				700: string
			}
			green: {
				200: string
				300: string
				400: string
				500: string
				600: string
				700: string
				800: string
				900: string
			}
			yellow: {
				200: string
				300: string
				500: string
			}
			blue: {
				500: string
				200: string
			}
			grey: {
				200: string
				300: string
				800: string
			}
		}
	}
	interface PaletteOptions {
		custom?: {
			// add new colors here in future is needed to excellent palette support
		}
	}
}
export {}
