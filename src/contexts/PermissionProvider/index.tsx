import {
	createContext,
	PropsWithChildren,
	useCallback,
	useEffect,
	useMemo,
} from 'react'
import { useAuthContext } from '../Auth'
import { useLocation, useNavigate } from 'react-router-dom'
import { match } from 'path-to-regexp'
import { emptyToken, initialToken } from '@/utils/constant'
import { useAuthStore } from '../Auth/useAuthStore'
const UserContext = createContext<boolean>(false)

const unauthenticatedOnlyRoutes = [
	'/login',
	'/create-new-password',
	'/forgot-password',
	'/invitation',
]

const PublicRoutes = [
	...unauthenticatedOnlyRoutes,
	'/credits/projects',
	'/credits/projects/:projectId/details',
	'/account-management/registration',
	'/account-management/dmrv-customization',
	'/credits/stocks/:stockId',
]

export const PermissionProvider = ({ children }: PropsWithChildren) => {
	const { userDetails, isLoggedIn, logout } = useAuthContext()
	const navigate = useNavigate()
	const { pathname } = useLocation()

	const tokenStore = useAuthStore()

	const routesForMultipleArtisanPros = useMemo(
		() =>
			userDetails?.artisanProIds?.reduce(
				(acc: string[], artisanProId: string) => {
					acc.push(`/dashboard/artisan-pro/${artisanProId}/details`)
					return acc
				},
				[]
			),
		[userDetails?.artisanProIds]
	)
	const routesForMultipleCsinkNetworks = useMemo(
		() =>
			userDetails?.csinkNetworkIds?.reduce(
				(acc: string[], csinkNetworkId: string) => {
					acc.push(`/dashboard/c-sink-network/${csinkNetworkId}/details`)
					return acc
				},
				[]
			),
		[userDetails?.csinkNetworkIds]
	)
	const allowedRoutesForUserRoles: Record<string, string[]> = useMemo(
		() => ({
			admin: [
				'/dashboard/home',
				'/dashboard/admin',
				'/dashboard/admin/files',
				'/dashboard/artisan-pro',

				'/dashboard/admin/queries',
				'/dashboard/admin/crm',
				'/dashboard/admin/company-details/:id',
				'/dashboard/admin/company-registerations',

				'/dashboard/admin/projects',
				'/dashboard/admin/projects/:projectId/details',
				'/dashboard/admin/projects/create',
				'/dashboard/admin/projects/:id/edit',

				'/dashboard/admin/otp-bypass',

				'/dashboard/admin/email-bypass',

				// '/dashboard/admin/pending-tasks',

				'/dashboard/admin/biomass',

				// '/dashboard/admin/geo-tag',

				'/dashboard/admin/user-management',

				'/dashboard/admin/entity-management',
				'/dashboard/admin/pending-task',
				'/dashboard/admin/add-constants',
				'/dashboard/admin/settings',

				'/dashboard/production',

				'/dashboard/production/biomass',
				'/dashboard/production/batches',
				'/dashboard/production/mixing',
				'/dashboard/production/packaging',
				'/dashboard/production/batches/:id/details',

				'/dashboard/production/inventory',
				'/dashboard/c-sink-network',
				'/dashboard/c-sink-network/:cSinkNetworkId/details',
				'/dashboard/artisan-pro-network',
				'/dashboard/artisan-pro-network/:artisanProNetworkId/details',

				'/dashboard/artisan-pro/:artisanProId/details',
				// '/dashboard/farmers',
				// '/dashboard/farmers/:farmerId/details',

				// '/dashboard/farmers/:farmId/farm-details',
				'/dashboard/credits',
				'/dashboard/credits/stocks/:stockId',
				'/dashboard/credits/projects',
				'/dashboard/credits/projects/:projectId/details',

				'/login',
				'/forget-password',
				// '/invitation',
				'/create-new-password',
			],
			biomass_aggregator: [
				'/dashboard/home',
				// '/dashboard/admin/pending-tasks',
				'/dashboard/admin/entity-management',
				'/dashboard/production',
				'/dashboard/production/biomass',
				'/dashboard/production/batches',
				'/dashboard/production/batches/:id/details',

				'/dashboard/c-sink-network',
				'/dashboard/c-sink-network/:cSinkNetworkId/details',
				// '/dashboard/artisan-pro/:artisanProNetworkId/details',

				'/dashboard/artisan-pro',
				'/dashboard/artisan-pro/:artisanProId/details',
				// '/dashboard/farmers',
				// '/dashboard/farmers/:farmerId/details',

				// '/dashboard/farmers/:farmId/farm-details',

				'/dashboard/admin/user-management',
				'/create-new-password',
				'/dashboard/admin/settings',
			],
			c_sink_manager: [
				'/dashboard/home',

				// '/dashboard/admin/pending-tasks',

				'/dashboard/admin/biomass',

				'/dashboard/admin/user-management',
				// '/dashboard/admin/add-constants',
				'/dashboard/admin/settings',

				'/dashboard/admin/entity-management',
				'/dashboard/admin/otp-bypass',
				'/dashboard/admin/email-bypass',
				'/dashboard/production',
				'/dashboard/production/biomass',
				'/dashboard/production/batches',
				'/dashboard/production/batches/:id/details',

				'/dashboard/c-sink-network',
				'/dashboard/c-sink-network/:cSinkNetworkId/details',
				'/dashboard/artisan-pro',
				'/dashboard/artisan-pro/:artisanProId/details',
				// '/dashboard/farmers',
				// '/dashboard/farmers/:farmerId/details',

				// '/dashboard/farmers/:farmId/farm-details',
				// '/dashboard/credits',
				// '/dashboard/credits/projects',
				// '/dashboard/credits/projects/:projectId/details',

				'/login',
				'/forget-password',
				'/invitation',
				'/create-new-password',
			],
			network_admin: [
				'/dashboard/home',

				...(routesForMultipleCsinkNetworks &&
				routesForMultipleCsinkNetworks?.length
					? routesForMultipleCsinkNetworks
					: []),
				`/dashboard/c-sink-network/${userDetails?.networkId}/details`,

				// '/dashboard/admin/pending-tasks',

				'/dashboard/admin/user-management',

				'/dashboard/production',
				'/dashboard/production/biomass',
				'/dashboard/production/batches',
				'/dashboard/production/batches/:id/details',

				'/dashboard/c-sink-network',

				'/login',
				'/forget-password',

				'/create-new-password',
			],
			artisan_pro_admin: [
				'/dashboard/home',
				'/dashboard/artisan-pro',
				`/dashboard/artisan-pro/${userDetails?.artisianProId}/details`,
				...(routesForMultipleArtisanPros && routesForMultipleArtisanPros?.length
					? routesForMultipleArtisanPros
					: []),
				// '/dashboard/admin/pending-tasks',

				'/dashboard/admin/user-management',
				'/dashboard/production',
				'/dashboard/production/biomass',
				'/dashboard/production/batches',
				'/dashboard/production/batches/:id/details',

				'/login',
				'/forget-password',
				'/create-new-password',
			],
			artisan_pro_network_manager: [
				'/dashboard/home',
				'/dashboard/artisan-pro',
				'/dashboard/artisan-pro/:artisanProId/details',
				'/dashboard/production',
				'/dashboard/production/biomass',
				'/dashboard/production/batches',
				'/dashboard/production/batches/:id/details',
				'/dashboard/admin/user-management',
				'/dashboard/admin/entity-management',
				'/login',
				'/forget-password',
				'/create-new-password',
			],
			circonomy_employee: [
				'/dashboard/admin/queries',
				'/dashboard/admin/projects',
				'/dashboard/admin/projects/:projectId/details',
				'/dashboard/admin/biomass',
				'/login',
				'/forget-password',
				'/invitation',
				'/create-new-password',
			],
			compliance_manager: [
				// '/dashboard/production',
				'/dashboard/production/batches',
				'/dashboard/production/batches/:id/details',
				'/login',
				'/forget-password',
				'/invitation',
				'/create-new-password',
			],
			company_admin: ['/account-management/validation'],
		}),
		[
			routesForMultipleArtisanPros,
			routesForMultipleCsinkNetworks,
			userDetails?.artisianProId,
			userDetails?.networkId,
		]
	)

	const redirectRoutes = useCallback(() => {
		if (userDetails) {
			navigate(
				allowedRoutesForUserRoles[userDetails?.accountType as string]?.[0]
			)
		}
	}, [allowedRoutesForUserRoles, navigate, userDetails])

	const isRouteAllowed = useMemo(
		() =>
			(
				allowedRoutesForUserRoles[userDetails?.accountType as string] ?? []
			)?.some((value) => {
				const urlMatch = match(value, {
					decode: decodeURIComponent,
				})
				return !!urlMatch(pathname)
			}),
		[allowedRoutesForUserRoles, pathname, userDetails?.accountType]
	)

	const isPublicRoute = PublicRoutes.some((route) => {
		const urlMatch = match(route, { decode: decodeURIComponent })
		return urlMatch(pathname)
	})

	useEffect(() => {
		if (userDetails && unauthenticatedOnlyRoutes.includes(pathname)) {
			redirectRoutes()
		}
		if (!isRouteAllowed && !isPublicRoute && isLoggedIn) {
			redirectRoutes()
		}
	}, [
		isRouteAllowed,
		redirectRoutes,
		isLoggedIn,
		userDetails,
		pathname,
		isPublicRoute,
	])

	useEffect(() => {
		if (tokenStore.token === initialToken) return
		if (
			tokenStore.token === emptyToken &&
			tokenStore.refreshToken === emptyToken
		) {
			if (isPublicRoute) return
			logout()
			navigate('/login')
		}
	}, [
		isPublicRoute,
		logout,
		navigate,
		pathname,
		tokenStore,
		tokenStore.refreshToken,
		tokenStore.token,
	])
	return (
		<UserContext.Provider value={isRouteAllowed}>
			{children}
		</UserContext.Provider>
	)
}

// export const useUser = () => useContext(UserContext);
