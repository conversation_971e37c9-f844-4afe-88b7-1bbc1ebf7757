import { Box, CircularProgress, Stack, Typography, styled } from '@mui/material'
import {
	GoogleMap,
	GoogleMapProps,
	InfoWindowF,
	LoadScriptProps,
	MarkerF,
	useLoadScript,
} from '@react-google-maps/api'
import { useCallback } from 'react'

interface IMaker {
	id: string
	name: string
	desc?: string
	position: {
		lat: number
		lng: number
	}
}

interface IProps extends Omit<GoogleMapProps, 'mapContainerStyle'> {
	mapContainerStyle?: React.CSSProperties
	markers: IMaker[]
	showLabel?: boolean
}

const libraries: LoadScriptProps['libraries'] = ['drawing', 'maps']

const { VITE_GOOGLE_MAPS_API_KEY } = import.meta.env

export const GoogleMapWithMultipleMarker = ({
	markers,
	showLabel,
	...props
}: IProps) => {
	const { isLoaded } = useLoadScript({
		id: 'google-map-script',
		googleMapsApiKey: VITE_GOOGLE_MAPS_API_KEY,
		libraries,
	})

	const handleOnLoad = useCallback(
		(map: any) => {
			const bounds = new google.maps.LatLngBounds()
			markers.forEach(({ position }) => bounds.extend(position))
			map.fitBounds(bounds)
		},
		[markers]
	)
	console.log({ mapCenter:props?.center })


	if (!isLoaded) return <CircularProgress />

	return (
		<ClassWrapper>
			<GoogleMap
				zoom={7}
				options={{
					minZoom: 1.5,
					mapTypeId: google.maps.MapTypeId.HYBRID,
					mapTypeControlOptions: {},
					...props?.options,
					restriction: {
						latLngBounds: {
							north: 85, // Upper latitude limit
							south: -85, // Lower latitude limit
							west: -179.999, // Left longitude limit (slightly adjusted to prevent wrapping)
							east: 179.999, // Right longitude limit (slightly adjusted to prevent wrapping)
						},
						strictBounds: true, // Enforce the bounds strictly to prevent panning out
					},
				}}
				onLoad={handleOnLoad}
				// center={markerPosition}
				{...props}>
				{markers.map(({ id, name, position, desc }) => (
					<MarkerF key={id} position={position}>
						{position && showLabel ? (
							<InfoWindowF position={position}>
								<Stack>
									<Typography textAlign='center' fontSize={12}>
										{name}
									</Typography>
									{desc && (
										<Typography textAlign='center' fontSize={12}>
											{desc}
										</Typography>
									)}
								</Stack>
							</InfoWindowF>
						) : null}
					</MarkerF>
				))}
			</GoogleMap>
		</ClassWrapper>
	)
}

const ClassWrapper = styled(Box)(({ theme }) => ({
	position: 'relative',
	'.gm-style-iw ': {
		'& .gm-ui-hover-effect': {
			display: 'none !important',
		},
	},
	'.gm-style-iw-ch': {
		paddingTop: theme.spacing(0),
	},
	'.gm-style-iw-c': {
		padding: '0px 0px 0px 12px',
	},
	'.gm-style-iw-d': {
		overflow: 'visible !important',
		padding: '2px 12px 2px 0px',
	},
}))
