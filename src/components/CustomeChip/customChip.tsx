import { warning } from '@/lib/theme/colors'
import { Chip, styled } from '@mui/material'
import { ReactElement } from 'react'

interface IProps {
	label?: string
	color?: string
	background?: string
	setShowAcceptOrRejectDialog?: (value: boolean) => void
	icon?: ReactElement
	onClick?: () => void
	appliedClass: string
}

export const CustomChip = ({
	label,
	color,
	background,
	icon,
	appliedClass,
	onClick,
	setShowAcceptOrRejectDialog,
}: IProps) => {
	return (
		<StyledChip
			onMouseEnter={
				setShowAcceptOrRejectDialog && (() => setShowAcceptOrRejectDialog(true))
			}
			onClick={onClick}
			className={appliedClass}
			label={label}
			icon={icon}
			sx={{
				color: color || warning.darkest,
				backgroundColor: background || warning.lightest,
				textTransform: 'capitalize',
			}}
		/>
	)
}

const StyledChip = styled(Chip)(({ theme }) => ({
	...theme.typography.subtitle2,
	'&.approved': {
		color: theme.palette.success.main,
		background: theme.palette.success.light,
	},
	'&.pending': {
		color: theme.palette.warning.dark,
		background: theme.palette.warning.light,
	},
	'&.rejected': {
		color: theme.palette.error.dark,
		background: theme.palette.error.light,
	},
	'&.compensated': {
		color: theme.palette.text.secondary,
		background: '#FFE0BE',
	},
}))
