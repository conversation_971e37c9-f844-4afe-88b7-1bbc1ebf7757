import { authAxios, useAuthContext } from '@/contexts'
import { userRoles } from '@/utils/constant'
import {
	Autocomplete,
	Button,
	FormControl,
	FormHelperText,
	Stack,
	styled,
	Typography,
	useTheme,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { FieldError, useForm } from 'react-hook-form'
import { addCsinkManager, TAddCSinkManager } from '../schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { PhoneInputComponent } from '@/components/PhoneInput'
import { FC, useCallback, useEffect } from 'react'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { CustomProfileElement } from '@/components/CustomProfileElement'
import { User } from '@/interfaces'
import { proxyImage } from '@/utils/helper'
import { CustomTextField } from '@/utils/components'
import { AxiosError } from 'axios'

const initialValues = {
	id: ',',
	name: '',
	email: '',
	phone: null,
	countryCode: null,
	profileImage: null,
}

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	editMode?: boolean
	userData?: User
}

export const AddCsinkManager: FC<TProps> = ({
	setIsActionInfoDrawer,
	editMode = false,
	userData,
}) => {
	const theme = useTheme()
	const { userDetails } = useAuthContext()
	const queryClient = useQueryClient()
	const {
		handleSubmit,
		watch,
		setValue,
		register,
		formState: { errors },
		clearErrors,
	} = useForm<TAddCSinkManager>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddCSinkManager>(addCsinkManager),
	})

	const fetchCsinkManager = useQuery({
		queryKey: ['allCsinkManager'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '1000',
				page: '0',
			})
			return authAxios.get<{
				csinkManagers: { id: string; name: string; shortName: string }[]
			}>(`/csink-manager?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.csinkManagers?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
		enabled:
			!!userDetails?.accountType &&
			userDetails?.accountType === userRoles.Admin &&
			!editMode,
	})

	const handleOnChange = (value: string, dialCode: string) => {
		setValue('countryCode', `+${dialCode}`)
		setValue('phone', value)
	}

	const addCSinkNetworkMutate = useMutation({
		mutationKey: ['addCsinkManager'],
		mutationFn: async (payload: TAddCSinkManager) => {
			const { id, profileImage, ...rest } = payload
			const body = {
				...rest,
				profileImageId: profileImage?.id ?? null,
			}
			return await authAxios.post(`csink-manager/${id}/assign-user`, body)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			queryClient.refetchQueries({
				queryKey: ['users'],
			})
			close()
		},
		onError: (error: any) => {
			toast(error.response?.data?.messageToUser)
		},
	})

	const editCSinkNetworkMutate = useMutation({
		mutationKey: ['editCsinkManager'],
		mutationFn: async (payload: TAddCSinkManager) => {
			const { id, profileImage, ...rest } = payload
			const body = {
				...rest,
				profileImageId: profileImage?.id || null,
				id: userData?.id,
			}
			return await authAxios.put(`csink-manager/${id}/user`, body)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			queryClient.refetchQueries({
				queryKey: ['users'],
			})
			close()
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	const handleAddOrEditCsinkManager = useCallback(
		async (values: TAddCSinkManager) => {
			const { phone, countryCode, ...rest } = values

			const payload = {
				...rest,
				phoneNumber: phone || null,
				countryCode: phone ? countryCode : null,
			}
			editMode
				? editCSinkNetworkMutate.mutate(payload)
				: addCSinkNetworkMutate.mutate(payload)
		},
		[addCSinkNetworkMutate, editCSinkNetworkMutate, editMode]
	)

	useEffect(() => {
		if (userDetails?.accountType !== userRoles.CsinkManager) return
		setValue('id', userDetails?.csinkManagerId)
	}, [setValue, userDetails?.accountType, userDetails?.csinkManagerId])

	useEffect(() => {
		if (!editMode) return
		setValue('id', userData?.csinkManagerId || '')
		setValue('name', userData?.name || '')
		setValue('countryCode', userData?.countryCode || '')
		setValue('email', userData?.email || '')
		setValue('phone', userData?.number || '')
		setValue('profileImage', {
			id: userData?.profileImageUrl?.id || '',
			url: proxyImage(userData?.profileImageUrl?.path || '') || '',
		})
	}, [editMode, setValue, userData])

	return (
		<StyledStack>
			{userDetails?.accountType === userRoles.Admin && !editMode ? (
				<Autocomplete
					onChange={(_, newValue: any) => {
						setValue('id', newValue?.value)
						clearErrors('id')
					}}
					options={fetchCsinkManager.data || []}
					renderInput={(params) => (
						<CustomTextField
							schema={addCsinkManager}
							{...params}
							label='Csink Manager'
							name='id'
						/>
					)}
				/>
			) : null}
			<CustomProfileElement
				{...(editMode && !!watch('profileImage')
					? { value: watch('profileImage') }
					: {})}
				errorMessage={errors?.profileImage?.id?.message}
				setValue={(id, url) =>
					setValue('profileImage', {
						id,
						url,
					})
				}
				clearErrors={() => clearErrors('profileImage.id')}
			/>
			<CustomTextField
				schema={addCsinkManager}
				fullWidth
				id='name'
				type='text'
				label='Enter Name '
				variant='outlined'
				error={!!errors.name?.message}
				helperText={(errors?.name as FieldError)?.message}
				{...register('name')}
			/>

			<CustomTextField
				schema={addCsinkManager}
				id='email'
				label='Enter Email'
				autoComplete='off'
				variant='outlined'
				type='email'
				error={!!errors.email?.message}
				helperText={(errors?.email as FieldError)?.message}
				fullWidth
				inputProps={{
					form: {
						autocomplete: 'off',
					},
				}}
				{...register('email')}
			/>
			<FormControl>
				<PhoneInputComponent
					value={watch('phone') ?? ''}
					handleOnChange={handleOnChange}
					dialCode={watch('countryCode')}
					getSelectedCountryDialCode={(dialCode) =>
						setValue('countryCode', dialCode)
					}
				/>
				<FormHelperText error={Boolean(errors?.phone)}>
					{errors?.phone && (
						<Typography color='error' variant='caption'>
							{(errors?.phone as FieldError)?.message}
						</Typography>
					)}
				</FormHelperText>
			</FormControl>
			<Stack
				direction='row'
				justifyContent='space-between'
				className='buttonContainer'>
				<Button
					onClick={() => setIsActionInfoDrawer(false)}
					sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
					Cancel
				</Button>{' '}
				<LoadingButton
					loading={addCSinkNetworkMutate.isPending}
					disabled={addCSinkNetworkMutate.isPending}
					onClick={handleSubmit(handleAddOrEditCsinkManager)}
					variant='contained'>
					{editMode ? 'Save' : 'Add'}
				</LoadingButton>
			</Stack>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(4),
}))
