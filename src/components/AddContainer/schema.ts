import { containerShapesEnum } from '@/utils/constant'
import * as Yup from 'yup'

const imageSchema = Yup.object({
	id: Yup.string(),
	url: Yup.string(),
	fileName: Yup.string().notRequired(),
})

export const AddContainerSchema = (isGlobal: boolean = false) =>
	Yup.object({
		name: Yup.string().required('Name is required'),
		shape: Yup.string().required('Shape is required'),
		container: Yup.string().notRequired(),
		diameter: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.cylinder,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),
				otherwise: (schema) => schema,
			}),
		height: Yup.number()
			.nullable()
			.required('Please enter Dimensions')
			.typeError('Please enter Dimensions'),
		breadth: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.cuboid,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),
				otherwise: (schema) => schema,
			}),
		upperSide: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.conical,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),
		lowerSide: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.conical,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),
		upperBase: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.rectangular,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),
		lowerBase: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) => shape === containerShapesEnum.rectangular,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),
		length: Yup.number()
			.nullable()
			.when(['shape'], {
				is: (shape: string) =>
					shape === containerShapesEnum.cuboid ||
					shape === containerShapesEnum.rectangular,
				then: (schema) =>
					schema
						.required('Please enter Dimensions')
						.typeError('Please enter Dimensions'),

				otherwise: (schema) => schema,
			}),

		containerImage: Yup.array().when([], {
			is: () => !isGlobal,
			then: (schema) =>
				schema
					.of(imageSchema)
					.nullable()
					.required('Container image is Required')
					.min(2, 'Add more Container images'),
			otherwise: (schema) => schema,
		}),
		volume: Yup.number()
			.nullable()
			.min(1, 'Volume must be greater than 1')
			.required('volume is Required')
			.typeError('Volume is Required'),
	})

export type TAddContainer = Yup.InferType<ReturnType<typeof AddContainerSchema>>
