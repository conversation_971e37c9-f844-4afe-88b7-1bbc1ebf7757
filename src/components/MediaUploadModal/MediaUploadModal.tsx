import { theme } from '@/lib/theme/theme'
import { Cancel } from '@mui/icons-material'
import { LoadingButton } from '@mui/lab'
import ImageOutlinedIcon from '@mui/icons-material/ImageOutlined'
import PlayCircleOutlinedIcon from '@mui/icons-material/PlayCircleOutlined'

import { MediaUploadModalDialog } from '.'
import { BatchProps, ResponseData, Payload } from './interface'

import {
	Box,
	DialogActions,
	DialogContent,
	DialogTitle,
	IconButton,
	InputLabel,
	Stack,
} from '@mui/material'
import { ReactNode, useRef, useState } from 'react'
import { handleVideoUpload } from '@/utils/helper'
import { toast } from 'react-toastify'

interface IProps {
	open: boolean
	handleClose: () => void
	dialogeElement?: ReactNode
	handleSaveClick: (payload: Payload) => void
	handleCancelClick: () => void
	title?: string | ReactNode
	labelThumbnail: string | ReactNode
	labelVideo: string | ReactNode
	batchDetail?: BatchProps
}

const ERROR_MESSAGES = {
	NO_FILE: 'No file selected. Please choose a file to upload.',
	INVALID_TYPE: 'Unsupported file type. Please upload a valid image.',
	FILE_TOO_LARGE:
		'File size exceeds the 10MB limit. Please choose a smaller file.',
	UPLOAD_FAILURE: 'File upload failed. Please try again later.',
}

export const MediaUploadModal = ({
	open,
	handleClose,
	dialogeElement,
	handleSaveClick,
	handleCancelClick,
	title,
	labelThumbnail,
	labelVideo,
	batchDetail,
}: IProps) => {
	const [selectedThumbnailFile, setSelectedThumbnailFile] =
		useState<ResponseData | null>(null)
	const [selectedVideoFile, setSelectedVideoFile] =
		useState<ResponseData | null>(null)

	const fileThumbnailInputRef = useRef<HTMLInputElement>(null)
	const fileVideoInputRef = useRef<HTMLInputElement>(null)

	const handleFileThumbnailClick = () => {
		fileThumbnailInputRef.current?.click()
	}
	const handleFileVideoClick = () => {
		fileVideoInputRef.current?.click()
	}

	const handleError = (error: { type: string; message: string }) => {
		toast.error(error.message)
	}

	const handleFileChange = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		try {
			if (!event.target.files || event.target.files.length === 0) {
				throw { type: 'NO_FILE', message: ERROR_MESSAGES.NO_FILE }
			}

			const file = event.target.files[0]

			const data = await handleVideoUpload(file)
			if (!data || !data.id || !data.url) {
				throw {
					type: 'UPLOAD_FAILURE',
					message: ERROR_MESSAGES.UPLOAD_FAILURE,
				}
			}
			if (event.target.id === 'processThumbnail') {
				setSelectedThumbnailFile(data)
			} else {
				setSelectedVideoFile(data)
			}
			event.target.value = ''
		} catch (error: any) {
			handleError(error)
		} finally {
			event.target.value = ''
		}
	}

	const handleRemoveThumbnail = () => {
		setSelectedThumbnailFile(null)
	}

	const handleRemoveVideo = () => {
		setSelectedVideoFile(null)
	}

	const handleSave = () => {
		if (!batchDetail) {
			toast.error('Batch details are missing.')
			return
		}

		// Final Payload
		const payload: Payload = {
			processVideos: [
				{
					videoId: selectedVideoFile?.id,
					thumbnailImageId: selectedThumbnailFile?.id,
				},
			],
		}

		handleSaveClick(payload)
	}

	return (
		<MediaUploadModalDialog
			open={open}
			onClose={handleClose}
			aria-labelledby='alert-dialog-title'
			aria-describedby='alert-dialog-description'
			fullWidth
			maxWidth='xs'
			PaperProps={{
				style: {
					padding: theme.spacing(1.2),
				},
			}}>
			<IconButton className='icon-button-align' onClick={handleClose}>
				<Cancel />
			</IconButton>
			{title ? <DialogTitle textAlign='center'>{title}</DialogTitle> : null}
			<DialogContent className='dialog-content-align'>
				{dialogeElement}
				<InputLabel>{labelThumbnail}</InputLabel>
				{selectedThumbnailFile === null ? (
					<IconButton onClick={handleFileThumbnailClick}>
						<ImageOutlinedIcon className='upload-icon' />
						<input
							ref={fileThumbnailInputRef}
							id='processThumbnail'
							name='processThumbnail'
							type='file'
							accept='.jpg, .jpeg, .png, .heic, .webp'
							style={{ display: 'none' }}
							onChange={handleFileChange}
						/>
					</IconButton>
				) : (
					<Stack className='list-image-align'>
						<Box key={selectedThumbnailFile.id} position={'relative'}>
							{' '}
							<Box
								component={'img'}
								src={selectedThumbnailFile.url}
								alt={`Image ${selectedThumbnailFile.id}`}
								className='selected-image-align'
							/>
							<IconButton
								className='icon-image-cancel'
								onClick={() => {
									handleRemoveThumbnail()
								}}>
								<Cancel className='image-cancel-button' />
							</IconButton>
						</Box>
					</Stack>
				)}
				<InputLabel>{labelVideo}</InputLabel>
				{selectedVideoFile === null ? (
					<IconButton onClick={handleFileVideoClick}>
						<PlayCircleOutlinedIcon className='upload-icon' />
						<input
							ref={fileVideoInputRef}
							id='processVideo'
							name='processVideo'
							type='file'
							accept='video/*'
							style={{ display: 'none' }}
							onChange={handleFileChange}
						/>
					</IconButton>
				) : (
					<Box component={'div'} className='selected-video-file'>
						{selectedVideoFile?.fileName}
						<IconButton onClick={handleRemoveVideo}>
							<Cancel
								sx={{
									fontSize: '50px',
								}}
								className='image-cancel-button'
							/>
						</IconButton>
					</Box>
				)}
			</DialogContent>
			<DialogActions className='dialog-actions-align'>
				<LoadingButton
					variant='contained'
					onClick={() => {
						handleSave()
					}}>
					Save
				</LoadingButton>
				<LoadingButton onClick={handleCancelClick}>Cancel</LoadingButton>
			</DialogActions>
		</MediaUploadModalDialog>
	)
}
