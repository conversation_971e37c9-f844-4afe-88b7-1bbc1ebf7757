import { IImage } from '@/interfaces'
import { isProxyPath, proxyImage } from '@/utils/helper'
import { Cancel } from '@mui/icons-material'
import { Chip, Dialog, Stack, Typography, colors, styled } from '@mui/material'
import Box from '@mui/material/Box'
import { Carousel } from 'react-responsive-carousel'
import 'react-responsive-carousel/lib/styles/carousel.min.css'
import { useState } from 'react'
import { Confirmation } from '../Confirmation'
import { toast } from 'react-toastify'

interface IProps {
	open: boolean
	close: () => void
	ImagesList: IImage[]
	ShowDeleteOption?: boolean
	DeleteImageHandler?: (imgId: string) => void | undefined
	imageIndex?: number
	showDownload?: boolean
}

export const ImageCarouselDialog = ({
	open,
	close,
	ImagesList,
	ShowDeleteOption,
	DeleteImageHandler,
	imageIndex,
	showDownload,
}: IProps) => {
	const [isDeleteConfirm, setIsDeleteConfirm] = useState(false)
	const [imgId, setImgId] = useState('')

	return (
		<Dialog
			open={open}
			onClose={close}
			fullWidth
			sx={{
				'& .MuiPaper-root': {
					p: 5,
					backgroundColor:
						ImagesList.length === 0 ? colors.common.white : 'transparent',
					boxShadow: 'none',
				},
			}}>
			{ImagesList.length === 0 && (
				<Box
					sx={{ position: 'absolute', right: 10, top: 10, cursor: 'pointer' }}
					onClick={close}>
					<Cancel />
				</Box>
			)}
			{ImagesList.length !== 0 ? (
				<Carousel
					autoFocus
					autoPlay
					dynamicHeight
					infiniteLoop
					selectedItem={imageIndex ?? 0}
					showIndicators={ImagesList?.length > 1}
					showStatus={false}
					showThumbs={false}>
					{ImagesList?.map(({ fileName, path, id, url }: IImage) => (
						<Stack key={id}>
							<StyledContainer>
								{ShowDeleteOption === true ? (
									<Chip
										label='Delete'
										data-id={id}
										className='delete-btn'
										onClick={() => {
											setIsDeleteConfirm(true)
											setImgId(id)
										}}
									/>
								) : null}
								{showDownload && (
									<Chip
										label='Download'
										className='download-btn'
										onClick={() => {
											const link = document.createElement('a')
											link.href = url
											document.body.appendChild(link)
											link.click()
											toast('Media Downloaded')
										}}
									/>
								)}
							</StyledContainer>
							{fileName || path || url ? (
								<Box
									component='img'
									src={
										isProxyPath(fileName || path)
											? proxyImage((fileName || path) ?? '','800:')
											: url
									}
									alt='Img Error'
									maxHeight='70vh'
									borderRadius={2}
								/>
							) : (
								<Stack>
									<Typography variant='subtitle2'>No Images Found</Typography>
								</Stack>
							)}
						</Stack>
					))}
				</Carousel>
			) : (
				<Stack>
					{/* <Typography variant='subtitle2'>No Images Found</Typography> */}
					<Typography variant='subtitle2'>Image List Empty</Typography>
				</Stack>
			)}
			{isDeleteConfirm ? (
				<Confirmation
					open={isDeleteConfirm}
					confirmationSuccessBtnText='Yes'
					confirmationNotSuccessBtnText='Cancel'
					successBtnVariant='contained'
					handleClose={() => setIsDeleteConfirm(false)}
					handleNoClick={() => setIsDeleteConfirm(false)}
					handleYesClick={() => {
						DeleteImageHandler?.(imgId)
						setIsDeleteConfirm(false)
					}}
					confirmationText={
						'Are you sure you want to delete this, as this will delete all the details and you can revert this.'
					}
					title={'Are you sure you want to delete this?'}
				/>
			) : null}
		</Dialog>
	)
}

const StyledContainer = styled(Stack)(({ theme }) => ({
	flexDirection: 'row',
	gap: theme.spacing(1),
	alignItems: 'center',
	position: 'absolute',
	right: 5,
	top: 5,
	'.delete-btn': {
		background: theme.palette.grey[300],
		opacity: 0.8,
		color: theme.palette.error.main,
		fontWeight: 600,
		zIndex: 10,
		cursor: 'pointer',
	},
	'.download-btn': {
		color: theme.palette.primary.main,
		fontWeight: 600,
		zIndex: 10,
		cursor: 'pointer',
		background: theme.palette.grey[300],
		opacity: 0.8,
	},
}))
