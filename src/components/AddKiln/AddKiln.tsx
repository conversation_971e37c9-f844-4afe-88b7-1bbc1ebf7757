import {
	kilnShapeEnum,
	KilnShapeLabelValue,
	kilnTypeEnum,
	measuringUnitEnum,
	userR<PERSON>s,
	KilnTypeLabelValue,
} from '@/utils/constant'
import { Close } from '@mui/icons-material'
import {
	Autocomplete,
	Button,
	FormHelperText,
	IconButton,
	InputAdornment,
	MenuItem,
	Select,
	Stack,
	styled,
	TextField,
	Typography,
	useTheme,
} from '@mui/material'
import React, { useCallback, useEffect, useState } from 'react'
import {
	Controller,
	FormProvider,
	useForm,
	useFormContext,
} from 'react-hook-form'
import { AddKilnSchema, addKilnSchema } from './schema'
import { yupResolver } from '@hookform/resolvers/yup'
import { ILabelWithValue } from '@/types'
import { LoadingButton } from '@mui/lab'
import { GoogleMapsWithNonDraggableMarker } from '../GoogleMap'
import {
	calculateVolumeConicalKiln,
	calculateVolumeCylindricalKiln,
	calculateVolumePyramidalKiln,
	calculateVolumeRectangularKiln,
	convertMillimeterCubetoLitre,
	convertMillimeterToMeter,
	convertUnit,
	roundNumber,
} from '@/utils/helper'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { useParams, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { IKiln } from '@/interfaces'
import { MultipleFileUploader } from '../MultipleFileUploader'
import { CustomTextField } from '@/utils/components'
import { AnyObjectSchema } from 'yup'
import { GridCloseIcon } from '@mui/x-data-grid'
import { IUserContext } from '@/contexts/Auth/type'

type Props = {
	kilnDetails?: IKiln
	editMode?: boolean
	addText?: string
	subheading?: string
	isGlobalAddKiln?: boolean
	siteDetailsAddress?: string
	handleClose: () => void
}

interface InputListType {
	label: string
	value: string
	type: string
	options?: { label: string; value: string }[]
	disabled?: boolean | undefined
	isNumericInput?: boolean | undefined
	forCalculation?: boolean | undefined
}

const initialValues = {
	name: '',
	kilnType: kilnTypeEnum.KonTiki,
	kilnShape: kilnShapeEnum.Conical,
	upperSurfaceDiameter: '',
	lowerSurfaceDiameter: '',
	volume: 0,
	depth: '',
	latitude: 0,
	longitude: 0,
	images: [],
	diameter: '',
	address: '',
	longBase: '',
	shortBase: '',
	length: '',
	selectKiln: '',
}
const postApiforKiln = (
	isGlobalAddKiln?: boolean,
	cSinkNetworkId?: string,
	artisanProId?: string,
	siteId?: string,
	selectedFarmersSiteId?: string,
	userDetails?: IUserContext
) => {
	if (isGlobalAddKiln) {
		switch (userDetails?.accountType) {
			case userRoles.CsinkManager:
				return `/csink-manager/${userDetails?.csinkManagerId}/settings/kiln-template`
			case userRoles.BiomassAggregator:
				return `/biomass-aggregator/${userDetails?.biomassAggregatorId}/kiln-template`
			case userRoles.Admin:
			default:
				return `/settings/kiln-template`
		}
	}
	return cSinkNetworkId
		? // ? '/kiln'
		  `cs-network/${cSinkNetworkId}/site/${selectedFarmersSiteId}/kiln`
		: `artisian-pro/${artisanProId}/site/${siteId}/kiln`
}

// const getApiforKiln = (userDetails?: IUserContext) => {
// 	switch (userDetails?.accountType) {
// 		case userRoles.BiomassAggregator:
// 			return `/csink-manager/${userDetails?.csinkManagerId}/settings/kiln-template`
// 		case userRoles.CsinkManager:
// 		default:
// 			return `/settings/kiln-template`
// 	}
// }

const getApiforKiln = (
	csinkNetworkId?: string,
	artisanProId?: string,
	userDetails?: IUserContext,
	isGlobalAddKiln?: boolean
) => {
	if (
		userDetails?.accountType === userRoles.BiomassAggregator &&
		isGlobalAddKiln
	) {
		return `csink-manager/${userDetails.csinkManagerId}/settings/kiln-template?limit=1000&page=0`
	} else if (csinkNetworkId)
		return `/cs-network/${csinkNetworkId}/kiln-template?isAssigned=true`
	else if (artisanProId)
		return `/artisian-pro/${artisanProId}/kiln-template?isAssigned=true`
	// else if (userDetails?.accountType === userRoles.CsinkManager)
	// 	return `/csink-manager/${userDetails?.csinkManagerId}/settings/kiln-template`
	else return `/settings/kiln-template?`
}

export const AddKiln: React.FC<Props> = ({
	handleClose,
	editMode = false,
	kilnDetails,
	subheading,
	addText,
	siteDetailsAddress,
	isGlobalAddKiln = false,
}) => {
	const theme = useTheme()
	const { cSinkNetworkId, artisanProId } = useParams()
	const [searchParams] = useSearchParams()
	const siteId = searchParams.get('siteTab') ?? ''
	const { userDetails } = useAuthContext()
	const [unit, setUnit] = useState<measuringUnitEnum>(measuringUnitEnum.m)
	const [kilnSelected, setKilnSelected] = useState('')
	const lat = String(kilnDetails?.coordinate?.split(',')[0].slice(1))
	const lng = String(kilnDetails?.coordinate?.split(',')[1].slice(0, -1))
	const form = useForm<AddKilnSchema>({
		defaultValues: initialValues,
		resolver: yupResolver<AddKilnSchema>(
			addKilnSchema(isGlobalAddKiln, !!cSinkNetworkId)
		),
		mode: 'all',
	})
	const {
		formState: { errors },
		setValue,
	} = form

	const selectedKilnTemplate = form.watch('selectKiln')
	const queryClient = useQueryClient()

	const coordinationTextFieldContent = [
		...(!isGlobalAddKiln
			? [
					{
						label: 'Latitude',
						value: 'latitude',
						isNumericInput: true,
						type: 'textField',
					},
					{
						label: 'Longitude',
						value: 'longitude',
						isNumericInput: true,
						type: 'textField',
					},
			  ]
			: []),
	]

	const getAllGlobalKilns = async () => {
		const queryParams = new URLSearchParams({
			limit: '1000',
			page: '0',
		})

		const endpoint = `${getApiforKiln(
			cSinkNetworkId,
			artisanProId,
			userDetails,
			isGlobalAddKiln
		)}&${queryParams.toString()}`

		const response = await authAxios.get(endpoint)

		return response
	}

	const getAllGlobalKilnsQuery = useQuery({
		queryKey: ['getAllGlobalKilnsQuery', userDetails, isGlobalAddKiln],
		queryFn: getAllGlobalKilns,
		select(data) {
			const kilnsOptionsKeyValue = data?.data?.kilns.map((item: IKiln) => {
				return {
					label: item.name,
					value: item.id,
				}
			})

			return {
				globalData: data?.data,
				kilnOptions: kilnsOptionsKeyValue,
			}
		},
	})

	const getAllFarmers = async () => {
		const { data } = await authAxios.get(
			`/cs-network/${cSinkNetworkId}/farmers`,
			{
				params: {
					limit: 1000,
				},
			}
		)
		return data?.farmers || []
	}

	const getFarmers = useQuery({
		queryKey: ['getFarmers'],
		queryFn: () => getAllFarmers(),
		select: (data) =>
			data.map((farmer: any) => ({
				name: farmer.name,
				countryCode: farmer.countryCode,
				phoneNo: farmer.phoneNo,
				id: farmer.siteId,
				email:farmer?.email
			})),
		enabled: !!cSinkNetworkId,
	})

	useEffect(() => {
		if (!!selectedKilnTemplate) {
			const selectedKilnForEdit: IKiln[] =
				getAllGlobalKilnsQuery.data?.globalData?.kilns?.filter(
					(item: IKiln) => item?.id === selectedKilnTemplate
				)
			editModeSetValues({
				editModeKilnDetails: selectedKilnForEdit[0],
			})
			setKilnSelected(selectedKilnTemplate)
		}
	}, [getAllGlobalKilnsQuery.isLoading, selectedKilnTemplate])

	const selectKilnsList: InputListType[] = [
		{
			label: 'Select Kiln',
			value: 'selectKiln',
			type: 'select',
			disabled: !getAllGlobalKilnsQuery?.data?.kilnOptions?.length,
			options: getAllGlobalKilnsQuery?.data?.kilnOptions,
		},
	]

	const inputList: InputListType[] = [
		...(!editMode && !isGlobalAddKiln
			? selectKilnsList
			: [userRoles.CsinkManager, userRoles.BiomassAggregator]?.includes(
					userDetails?.accountType as userRoles
			  )
			? selectKilnsList
			: []),
		...(cSinkNetworkId
			? [
					{
						label: 'Select farmer',
						value: 'FarmerId',
						type: 'autocomplete',
						disabled: editMode,
						options: getFarmers?.data?.map(
							({ name, id, countryCode, phoneNo, email }: any) => ({
								label: `${name} ${phoneNo ? `(${countryCode} ${phoneNo})` : `(${email})`}`,
								value: id,
							})
						),
					},
			  ]
			: []),
		{
			label: 'Enter the Kiln Name',
			value: 'name',
			type: 'textField',
		},
		...(!isGlobalAddKiln
			? [
					{
						label: 'Address',
						value: 'address',
						type: 'textField',
					},
			  ]
			: []),
		{
			label: 'Kiln Type',
			value: 'kilnType',
			type: 'select',
			options: KilnTypeLabelValue,
		},
		{
			label: 'Kiln Shape',
			value: 'kilnShape',
			type: 'select',
			disabled:
				form.watch('kilnType') === kilnTypeEnum.RoCC ||
				(!!cSinkNetworkId && editMode),
			options: KilnShapeLabelValue,
		},
		...(form.watch('kilnShape') === kilnShapeEnum.Cylindrical
			? [
					{
						label: 'Diameter',
						value: 'diameter',
						isNumericInput: true,
						forCalculation: true,
						disabled: !!cSinkNetworkId && editMode,
						type: 'textField',
					},
					{
						label: 'Depth',
						value: 'depth',
						isNumericInput: true,
						forCalculation: true,
						disabled: !!cSinkNetworkId && editMode,
						type: 'textField',
					},
			  ]
			: form.watch('kilnShape') === kilnShapeEnum.Rectangular
			? [
					{
						label: 'Length',
						value: 'length',
						isNumericInput: true,
						forCalculation: true,
						disabled: !!cSinkNetworkId && editMode,
						type: 'textField',
					},
					{
						label: 'Short Base',
						value: 'shortBase',
						isNumericInput: true,
						forCalculation: true,
						disabled: !!cSinkNetworkId && editMode,
						type: 'textField',
					},
					{
						label: 'Long Base',
						value: 'longBase',
						isNumericInput: true,
						forCalculation: true,
						disabled: !!cSinkNetworkId && editMode,
						type: 'textField',
					},
					{
						label: 'Depth',
						value: 'depth',
						isNumericInput: true,
						forCalculation: true,
						disabled: !!cSinkNetworkId && editMode,
						type: 'textField',
					},
			  ]
			: [
					{
						label: 'Upper Surface Diameter',
						value: 'upperSurfaceDiameter',
						isNumericInput: true,
						forCalculation: true,
						disabled: !!cSinkNetworkId && editMode,

						type: 'textField',
					},
					{
						label: 'Lower Surface Diameter',
						value: 'lowerSurfaceDiameter',
						isNumericInput: true,
						forCalculation: true,
						disabled: !!cSinkNetworkId && editMode,
						type: 'textField',
					},
					{
						label: 'Depth',
						value: 'depth',
						isNumericInput: true,
						forCalculation: true,
						disabled: !!cSinkNetworkId && editMode,
						type: 'textField',
					},
			  ]),
	]

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', Number(lat.toFixed(6)))
			setValue('longitude', Number(lng.toFixed(6)))
		},
		[setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter, kilnSelected])

	const calculateKilnVolume = useCallback(
		(column?: string, value?: string) => {
			let volume = 0

			const lowerSurfaceDiameter = convertUnit(
				column === 'lowerSurfaceDiameter'
					? Number(value)
					: Number(form.watch('lowerSurfaceDiameter')),
				unit
			)

			const upperSurfaceDiameter = convertUnit(
				column === 'upperSurfaceDiameter'
					? Number(value)
					: Number(form.watch('upperSurfaceDiameter')),
				unit
			)
			const depth = convertUnit(
				column === 'depth' ? Number(value) : Number(form.watch('depth')),
				unit
			)

			const diameter = convertUnit(
				column === 'diameter' ? Number(value) : Number(form.watch('diameter')),
				unit
			)

			const length = convertUnit(
				column === 'length' ? Number(value) : Number(form.watch('length')),
				unit
			)
			const upperBase = convertUnit(
				column === 'longBase' ? Number(value) : Number(form.watch('longBase')),
				unit
			)
			const lowerBase = convertUnit(
				column === 'shortBase'
					? Number(value)
					: Number(form.watch('shortBase')),
				unit
			)

			switch (form.watch('kilnShape')) {
				case kilnShapeEnum.Conical:
					volume = calculateVolumeConicalKiln(
						upperSurfaceDiameter,
						lowerSurfaceDiameter,
						depth
					)
					break
				case kilnShapeEnum.Cylindrical:
					volume = calculateVolumeCylindricalKiln(diameter, depth)
					break
				case kilnShapeEnum.Pyramidal:
					volume = calculateVolumePyramidalKiln(
						upperSurfaceDiameter,
						lowerSurfaceDiameter,
						depth
					)
					break
				case kilnShapeEnum.Rectangular:
					volume = calculateVolumeRectangularKiln(
						length,
						depth,
						lowerBase,
						upperBase
					)
					break
				default:
					volume = 0
					break
			}
			form.setValue(
				'volume',
				convertMillimeterCubetoLitre(volume)
			)
		},
		[form, unit]
	)

	const addKilnMutation = useMutation({
		mutationKey: ['addKiln', unit, cSinkNetworkId, userDetails],
		mutationFn: async (values: AddKilnSchema) => {
			const {
				upperSurfaceDiameter,
				lowerSurfaceDiameter,
				depth,
				diameter,
				images,
				length,
				shortBase,
				longBase,
				// FarmerSiteId,
				...rest
			} = values

			const lsd = convertUnit(Number(lowerSurfaceDiameter), unit)

			const usd = convertUnit(Number(upperSurfaceDiameter), unit)
			const depthValue = convertUnit(Number(depth), unit)

			const l = convertUnit(Number(length), unit)
			const sb = convertUnit(Number(shortBase), unit)
			const lb = convertUnit(Number(longBase), unit)

			const diameterValue = convertUnit(Number(diameter), unit)

			const objectForConical = {
				upperSurfaceDiameter: usd,
				lowerSurfaceDiameter: lsd,
				depth: depthValue,
			}
			const objectForPyramidal = {
				upperSide: usd,
				lowerSide: lsd,
				depth: depthValue,
			}
			const objectForRectangular = {
				frustumLength: l,
				depth: depthValue,
				shortBase: sb,
				longBase: lb,
			}
			const objectForCylinder = {
				diameter: diameterValue,
				depth: depthValue,
			}
			const payloadAccordToKilnShape = {
				[kilnShapeEnum.Conical]: objectForConical,
				[kilnShapeEnum.Cylindrical]: objectForCylinder,
				[kilnShapeEnum.Rectangular]: objectForRectangular,
				[kilnShapeEnum.Pyramidal]: objectForPyramidal,
			}

			const payload = {
				...rest,
				...payloadAccordToKilnShape[values.kilnShape as kilnShapeEnum],
				volume: Number(values.volume),
				...(!isGlobalAddKiln
					? {
							latitude: Number(values.latitude),
							longitude: Number(values.longitude),
							networkId: cSinkNetworkId,
					  }
					: {}),
				imageIds: images?.map((img) => img.id),
			}

			const selectedFarmersSiteId = form.watch('FarmerId')

			const { data } = await authAxios.post(
				postApiforKiln(
					isGlobalAddKiln,
					cSinkNetworkId,
					artisanProId,
					siteId,
					selectedFarmersSiteId,
					userDetails
				),
				{
					...payload,
				}
			)

			return data
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			handleClose()
			queryClient.refetchQueries({
				queryKey: [cSinkNetworkId ? 'kilnDetails' : 'getKilnList'],
			})
			cSinkNetworkId &&
				queryClient.refetchQueries({
					queryKey: ['cSinkNetworkDetails'],
				})
			queryClient.refetchQueries({ queryKey: ['allKilns'] })
			artisanProId && queryClient.refetchQueries({ queryKey: ['allSites'] })
		},
	})

	const editKilnMutation = useMutation({
		mutationKey: ['editKiln', unit, cSinkNetworkId],
		mutationFn: async (values: AddKilnSchema) => {
			const {
				upperSurfaceDiameter,
				lowerSurfaceDiameter,
				depth,
				diameter,
				images,
				length,
				shortBase,
				longBase,
				...rest
			} = values

			const lsd = convertUnit(Number(lowerSurfaceDiameter), unit)

			const usd = convertUnit(Number(upperSurfaceDiameter), unit)
			const depthValue = convertUnit(Number(depth), unit)

			const l = convertUnit(Number(length), unit)
			const sb = convertUnit(Number(shortBase), unit)
			const lb = convertUnit(Number(longBase), unit)

			const diameterValue = convertUnit(Number(diameter), unit)

			const objectForConical = {
				upperSurfaceDiameter: usd,
				lowerSurfaceDiameter: lsd,
				depth: depthValue,
			}
			const objectForPyramidal = {
				upperSide: usd,
				lowerSide: lsd,
				depth: depthValue,
			}
			const objectForRectangular = {
				frustumLength: l,
				depth: depthValue,
				shortBase: sb,
				longBase: lb,
			}
			const objectForCylinder = {
				diameter: diameterValue,
				depth: depthValue,
			}
			const payloadAccordToKilnShape = {
				[kilnShapeEnum.Conical]: objectForConical,
				[kilnShapeEnum.Cylindrical]: objectForCylinder,
				[kilnShapeEnum.Rectangular]: objectForRectangular,
				[kilnShapeEnum.Pyramidal]: objectForPyramidal,
			}

			const payload = {
				...rest,
				...payloadAccordToKilnShape[values.kilnShape as kilnShapeEnum],
				volume: Number(values.volume),
				latitude: Number(values.latitude),
				longitude: Number(values.longitude),
				imageIds: images?.map((img) => img.id),
			}
			const api = `/kiln/${kilnDetails?.id}`
			const { data } = await authAxios.put(api, {
				...payload,
				networkId: cSinkNetworkId,
			})
			return data
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			handleClose()
			queryClient.invalidateQueries({ queryKey: ['getKilnList'] })
		},
	})

	const handleAddKiln = useCallback(
		(values: AddKilnSchema) => {
			editMode
				? editKilnMutation.mutate(values)
				: addKilnMutation.mutate(values)
		},
		[addKilnMutation, editKilnMutation, editMode]
	)

	useEffect(() => {
		getCurrentLocation()
	}, [getCurrentLocation, kilnSelected])

	const editModeSetValues = ({
		editModeKilnDetails,
	}: {
		editModeKilnDetails: IKiln | undefined
	}) => {
		form.setValue('name', editModeKilnDetails?.name ?? '')
		form.setValue(
			'kilnType',
			editModeKilnDetails?.kilnType ?? kilnTypeEnum.KonTiki
		)
		form.setValue(
			'kilnShape',
			editModeKilnDetails?.kilnShape ?? kilnShapeEnum.Conical
		)
		if (cSinkNetworkId) {
			form.setValue('FarmerId', editModeKilnDetails?.farmerId)
		}
		form.setValue(
			'upperSurfaceDiameter',
			String(
				convertMillimeterToMeter(
					editModeKilnDetails?.upperSurfaceDiameter ??
						editModeKilnDetails?.upperSide ??
						0
				)
			) ?? ''
		)
		form.setValue(
			'lowerSurfaceDiameter',
			String(
				convertMillimeterToMeter(
					editModeKilnDetails?.lowerSurfaceDiameter ??
						editModeKilnDetails?.lowerSide ??
						0
				)
			) ?? ''
		)
		form.setValue(
			'longBase',
			String(
				convertMillimeterToMeter(
					editModeKilnDetails?.longBase ?? editModeKilnDetails?.lowerBase ?? 0
				)
			) ?? ''
		)
		form.setValue(
			'shortBase',
			String(
				convertMillimeterToMeter(
					editModeKilnDetails?.shortBase ?? editModeKilnDetails?.upperBase ?? 0
				)
			) ?? ''
		)
		if (editModeKilnDetails?.kilnShape !== kilnShapeEnum.Rectangular) {
			form.setValue(
				'length',
				String(convertMillimeterToMeter(editModeKilnDetails?.length ?? 0)) ?? ''
			)
		} else {
			form.setValue(
				'length',
				String(
					convertMillimeterToMeter(editModeKilnDetails?.frustumLength ?? 0)
				) ?? ''
			)
		}
		form.setValue('volume', editModeKilnDetails?.volume ?? 0)
		form.setValue(
			'depth',
			String(convertMillimeterToMeter(editModeKilnDetails?.depth ?? 0)) ?? ''
		)
		form.setValue('latitude', Number(lat))
		form.setValue('longitude', Number(lng))
		form.setValue('images', editModeKilnDetails?.imageURLs ?? [])
		form.setValue(
			'diameter',
			String(convertMillimeterToMeter(editModeKilnDetails?.diameter ?? 0)) ?? ''
		)
		form.setValue('address', editModeKilnDetails?.address ?? '')
	}

	useEffect(() => {
		if (!editMode) return
		// set values to form and convert mm into meter for initial render
		editModeSetValues({ editModeKilnDetails: kilnDetails })
	}, [])

	useEffect(() => {
		if (addText) form.setValue('name', addText)
	}, [])

	useEffect(() => {
		if (siteDetailsAddress) form.setValue('address', siteDetailsAddress)
	}, [form, siteDetailsAddress])

	const selectedKilnType = form.watch('kilnType')
	const selectedKiln = KilnTypeLabelValue.find(
		(kiln) => kiln.value === selectedKilnType
	)
	const selectedTemperature = selectedKiln ? selectedKiln.temperature : 0

	useEffect(() => {
		if (selectedKilnType === kilnTypeEnum.RoCC) {
			form.unregister('temperature')
		} else if (selectedTemperature !== undefined) {
			form.setValue('temperature', selectedTemperature, {
				shouldValidate: true,
			})
		}
	}, [selectedKilnType, selectedTemperature, form])

	return (
		<FormProvider {...form}>
			<StyleContainer>
				<Stack className='header'>
					<Stack
						direction='row'
						spacing={1}
						alignItems='center'
						width='100%'
						justifyContent='space-between'>
						<Stack>
							<Typography variant='h5'>
								{editMode ? 'Edit' : 'Add'} Kiln
							</Typography>
							<Typography variant='subtitle1'>{subheading}</Typography>
						</Stack>
						<IconButton onClick={handleClose}>
							<Close />
						</IconButton>
					</Stack>
				</Stack>
				<Stack className='container'>
					{inputList.map((item) => (
						<RenderComponent
							type={item.type}
							label={item.label}
							key={item.label}
							schema={addKilnSchema(isGlobalAddKiln)}
							name={item.value as keyof AddKilnSchema}
							forCalculation={!!item?.forCalculation}
							isNumericalInput={!!item?.isNumericInput}
							disabled={!!item?.disabled}
							unit={unit}
							options={item?.options || []}
							handleUnit={(value) => setUnit(value as measuringUnitEnum)}
							calculateKilnVolume={calculateKilnVolume}
							isLoading={getFarmers.isLoading}
						/>
					))}
					<Stack>
						<Typography variant='subtitle1'>
							Total Volume: {roundNumber(form.watch('volume') || 0)}
						</Typography>
						{form.watch('volume') < 10 && (
							<Typography variant='subtitle1' color='error'>
								Warning: Volume of the klin is too low
							</Typography>
						)}
					</Stack>
					{!isGlobalAddKiln ? (
						<Stack gap={theme.spacing(2)}>
							<Stack>
								<Typography variant='subtitle1'>Kiln Image</Typography>
								<Typography variant='caption'>
									Please enter the Images after measuring the Kiln Dimensions
								</Typography>
							</Stack>
							<MultipleFileUploader
								heading='Add Kiln Image'
								sx={{
									height: { xs: 100, md: 166 },
									width: '100%',
								}}
								data={kilnDetails?.imageURLs}
								imageHeight={100}
								setUploadData={(data) => {
									form.setValue('images', data)
									form.clearErrors('images')
								}}
								required
								training={false}
							/>
							<FormHelperText error={Boolean(errors.images)}>
								{errors?.images?.message}
							</FormHelperText>
						</Stack>
					) : null}
					<Stack direction='row' columnGap={1}>
						{coordinationTextFieldContent.map((item) => (
							<RenderComponent
								type={item.type}
								label={item.label}
								schema={addKilnSchema(isGlobalAddKiln)}
								key={item.label}
								name={item.value as keyof AddKilnSchema}
								isNumericalInput={!item?.isNumericInput}
							/>
						))}
					</Stack>
					{!isGlobalAddKiln ? (
						<GoogleMapsWithNonDraggableMarker
							center={{
								lat: Number(form.watch('latitude')),
								lng: Number(form.watch('longitude')),
							}}
							setMapCenter={setMapCenter}
							mapContainerStyle={{
								width: '100%',
								height: 300,
								position: 'relative',
							}}
						/>
					) : null}
					<Stack
						direction='row'
						gap={2}
						justifyContent='space-between'
						className='buttonContainer'>
						<Button
							onClick={handleClose}
							sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
							Cancel
						</Button>
						<LoadingButton
							loading={addKilnMutation.isPending}
							disabled={addKilnMutation.isPending}
							onClick={form?.handleSubmit(handleAddKiln)}
							variant='contained'>
							Add
						</LoadingButton>
					</Stack>
				</Stack>
			</StyleContainer>
		</FormProvider>
	)
}

type RenderComponentProps = {
	type: string
	name: keyof AddKilnSchema
	options?: ILabelWithValue[]
	label: string
	unit?: string
	isNumericalInput?: boolean
	forCalculation?: boolean
	schema?: AnyObjectSchema
	handleUnit?: (value: string) => void
	disabled?: boolean
	calculateKilnVolume?: (column?: string, value?: string) => void
	isLoading?: boolean
}

const RenderComponent: React.FC<RenderComponentProps> = ({
	type,
	name,
	options,
	label,
	unit,
	isNumericalInput,
	schema,
	forCalculation,
	handleUnit,
	disabled,
	calculateKilnVolume,
	isLoading = false,
}) => {
	const {
		register,
		formState: { errors },
		setValue,
		getValues,
		watch,
		clearErrors,
		reset,
	} = useFormContext<AddKilnSchema>()

	const valueUpToThreeDecimalPlaces = useCallback(
		(value: string) => {
			const regexForSingleDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,1})/s
			const regexForThreeDecPlace = /([0-9]*[.|,]{0,1}[0-9]{0,3})/s

			return (
				value.match(
					unit === 'm' ? regexForThreeDecPlace : regexForSingleDecPlace
				)?.[0] || ''
			)
		},
		[unit]
	)

	const handleValueChange = useCallback(
		(key: keyof AddKilnSchema, value: string) => {
			if (!value.includes('.')) return
			setValue(key, valueUpToThreeDecimalPlaces(value))
		},
		[setValue, valueUpToThreeDecimalPlaces]
	)

	const handleSelectChange = useCallback(
		(type: string, value: string) => {
			if (type === 'selectKiln') {
				setValue('selectKiln', value)
			} else if (type === 'kilnType') {
				setValue('kilnType', value)
				if (value === kilnTypeEnum.RoCC) {
					setValue('kilnShape', kilnShapeEnum.Cylindrical)
				}
				clearErrors('kilnType')
			} else {
				setValue('kilnShape', value)
				clearErrors('kilnShape')
				setValue('upperSurfaceDiameter', '')
				setValue('lowerSurfaceDiameter', '')
				setValue('depth', '')
				setValue('volume', 0)
				setValue('diameter', '')
			}
		},
		[clearErrors, setValue]
	)

	const onClear = () => {
		reset()
	}

	switch (type) {
		case 'textField':
			return (
				<CustomTextField
					schema={schema}
					label={label}
					fullWidth
					disabled={disabled}
					{...register(name)}
					watch={watch}
					hideNumberArrows={isNumericalInput}
					InputProps={{
						endAdornment: forCalculation ? (
							<DimensionUnit
								unit={unit as string}
								setUnit={(value) => handleUnit?.(value)}
								onChange={() => {
									setValue('lowerSurfaceDiameter', '')
									setValue('upperSurfaceDiameter', '')
									setValue('volume', 0)
									setValue('depth', '')
									setValue('diameter', '')
								}}
							/>
						) : null,
					}}
					error={!!errors?.[name]?.message}
					helperText={errors?.[name]?.message}
					onInputCapture={(e: React.ChangeEvent<HTMLInputElement>) => {
						if (forCalculation) {
							calculateKilnVolume?.(
								name,
								valueUpToThreeDecimalPlaces(e.target.value)
							)
							handleValueChange(name as keyof AddKilnSchema, e.target.value)
						}
					}}
				/>
			)
		case 'select':
			return (
				<CustomTextField
					schema={schema}
					label={label}
					select
					fullWidth
					value={watch(name)}
					{...register(name)}
					className='input-disable-class'
					error={!!errors?.[name]?.message}
					InputProps={{
						endAdornment: !!getValues('selectKiln') &&
							name === 'selectKiln' && (
								<InputAdornment
									sx={{
										paddingRight: 2,
									}}
									position='end'>
									<IconButton
										aria-label='clear field'
										onClick={onClear}
										edge='end'
										size='small'>
										<GridCloseIcon fontSize='small' />
									</IconButton>
								</InputAdornment>
							),
					}}
					disabled={disabled}
					helperText={errors?.[name]?.message}
					onChange={(e) => handleSelectChange(name, e.target.value)}>
					{(options || [])?.map((option) => (
						<MenuItem key={option.value} value={option.value}>
							{option.label}
						</MenuItem>
					))}
				</CustomTextField>
			)
		case 'autocomplete':
			return (
				<Controller
					name={name}
					render={({ field }) => (
						<Autocomplete
							{...field}
							options={options || []}
							getOptionLabel={(option) => option.label}
							value={options?.find((opt) => opt.value === field.value) || null}
							onChange={(_, newValue) => {
								field.onChange(newValue?.value || '')
							}}
							disabled={disabled}
							loading={isLoading}
							renderInput={(params) => (
								<TextField
									{...params}
									label='Select Farmer'
									variant='outlined'
									placeholder='Search by name or phone number'
									error={!!errors?.[name]?.message}
									helperText={errors?.[name]?.message}
								/>
							)}
						/>
					)}
				/>
			)

		default:
			return null
	}
}

const DimensionUnit = ({
	unit,
	setUnit,
	onChange,
}: {
	unit: string
	setUnit: (value: string) => void
	onChange: () => void
}) => (
	<Select
		value={unit}
		onChange={(e) => {
			setUnit(e.target.value)
			onChange()
		}}
		sx={{
			'& fieldset': {
				border: 'none',
			},
		}}>
		<MenuItem value='cm'>cm</MenuItem>
		<MenuItem value='m'>m</MenuItem>
	</Select>
)

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.tabList': {
		'& .MuiTabs-scroller': {
			overflow: 'auto !important',
		},
		'& ::-webkit-scrollbar': {
			display: 'none',
		},
		'-ms-overflow-style': 'none' /* IE and Edge */,
		'scrollbar-width': 'none' /* Firefox */,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		'.buttonContainer button': {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
			marginBottom: theme.spacing(10),
		},
	},
}))
