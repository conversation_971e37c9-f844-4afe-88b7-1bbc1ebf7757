import { authAxios } from '@/contexts'
import {
	IContainerDetails,
	IFpu,
	IKiln,
	ILocation,
	ISamplingContainerDetails,
	IVehicle,
} from '@/interfaces'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { EntityTabEnum } from '@/utils/constant'

export const useEditSiteEntity = () => {
	const [tab, setTab] = useState(EntityTabEnum.kilns)
	const [searchParams, setSearchParams] = useSearchParams()
	const [farmCoordinates, setFarmCoordinates] = useState<
		google.maps.LatLng[] | google.maps.LatLngLiteral[]
	>([])
	const [showMap, setShowMap] = useState(false)
	const navigate = useNavigate()
	const siteId = searchParams.get('siteTab')
	const { artisanProId, cSinkNetworkId } = useParams()
	const queryClient = useQueryClient()

	const containerListQuery = useQuery({
		queryKey: [
			'getContainerList',
			artisanProId,
			cSinkNetworkId,
			tab,
			EntityTabEnum,
		],
		queryFn: async () => {
			const { data } = await authAxios<{
				count: number
				containers: IContainerDetails[]
			}>(
				`/artisian-pro/${artisanProId}/site/${siteId}/measuring-container?limit=1000`
			)
			return data
		},
		enabled: tab === EntityTabEnum.containers,
	})

	const kilnListQuery = useQuery({
		queryKey: ['getKilnList', artisanProId, cSinkNetworkId, tab, EntityTabEnum],
		queryFn: async () => {
			const { data } = await authAxios<{
				count: number
				kilns: IKiln[]
			}>(`/artisian-pro/${artisanProId}/site/${siteId}/kiln?limit=1000`)
			return data
		},
		enabled: tab === EntityTabEnum.kilns,
	})
	const biomassSourceListQuery = useQuery({
		queryKey: [
			'getBiomassSourceList',
			artisanProId,
			cSinkNetworkId,
			tab,
			EntityTabEnum,
		],
		queryFn: async () => {
			const { data } = await authAxios<{
				count: number
				fpu: IFpu[]
			}>(`/artisian-pro/${artisanProId}/site/${siteId}/fpu?limit=1000`)
			return data
		},
		enabled: tab === EntityTabEnum.biomassSource,
	})

	const samplingContainerListQuery = useQuery({
		queryKey: [
			'getSamplingContainerList',
			artisanProId,
			cSinkNetworkId,
			tab,
			EntityTabEnum,
		],
		queryFn: async () => {
			const { data } = await authAxios<{
				count: number
				samplingContainers: ISamplingContainerDetails[]
			}>(`/artisian-pro/${artisanProId}/site/${siteId}/container?limit=1000`)
			return data
		},
		enabled: tab === EntityTabEnum.samplingContainer,
	})

	const deleteContainerMutation = useMutation({
		mutationKey: ['deleteContainerMutation'],
		mutationFn: async (id: string) => {
			const { data } = await authAxios.delete(
				`/artisian-pro/${artisanProId}/site/${siteId}/measuring-container/${id}`
			)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			containerListQuery.refetch()
		},
	})

	const handleDeleteContainer = useCallback(
		(id: string) => {
			deleteContainerMutation.mutate(id)
		},
		[deleteContainerMutation]
	)
	const deleteSamplingContainerMutation = useMutation({
		mutationKey: ['deleteSamplingContainerMutation'],
		mutationFn: async (id: string) => {
			const { data } = await authAxios.delete(
				`/artisian-pro/${artisanProId}/site/${siteId}/container/${id}`
			)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			samplingContainerListQuery.refetch()
		},
	})

	const deleteBiomassSourceMutation = useMutation({
		mutationKey: ['deleteBiomassSourceMutation'],
		mutationFn: async (id: string) => {
			const { data } = await authAxios.delete(
				`/artisian-pro/${artisanProId}/site/${siteId}/fpu/${id}`
			)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			biomassSourceListQuery.refetch()
		},
	})
	const handleDeleteBiomassSource = useCallback(
		(id: string) => {
			deleteBiomassSourceMutation.mutate(id)
		},
		[deleteBiomassSourceMutation]
	)

	const handleDeleteSamplingContainer = useCallback(
		(id: string) => {
			deleteSamplingContainerMutation.mutate(id)
		},
		[deleteSamplingContainerMutation]
	)

	const vehicleListQuery = useQuery({
		queryKey: ['getVehicleList', artisanProId, cSinkNetworkId],
		queryFn: async () => {
			const { data } = await authAxios<{
				count: number
				vehicles: IVehicle[]
			}>(`/artisian-pro/${artisanProId}/site/${siteId}/vehicle?limit=1000`)
			return data
		},
		enabled: tab === EntityTabEnum.vehicles,
	})

	const fillSamplingContainerMutation = useMutation({
		mutationKey: ['deleteSamplingContainerMutation'],
		mutationFn: async ({ id, filled }: { id: string; filled?: boolean }) => {
			const apiEndPoint = filled ? 'empty' : 'fill'
			const { data } = await authAxios.put(
				`/sampling-container/${apiEndPoint}/id/${id}`
			)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			samplingContainerListQuery.refetch()
		},
	})

	const handleFillSamplingContainer = (
		container: ISamplingContainerDetails
	) => {
		fillSamplingContainerMutation.mutate({
			id: container?.id,
			filled: container?.filled,
		})
	}

	const handleSaveKmlMutation = useMutation({
		mutationKey: ['SaveKml'],
		mutationFn: async (mapData: ILocation[]) => {
			const networkId = searchParams.get('networkId')
			if (!networkId) {
				return
			}
			const payload = {
				fpuArea: mapData,
			}
			const api = `/artisian-pro/${artisanProId}/fpu/${networkId}`
			await authAxios.put(api, payload)
		},
		onSuccess: () => {
			biomassSourceListQuery.refetch()
			toast('Farm KML added')
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { userToMessage: string })?.userToMessage)
		},
	})
	const handleSaveKml = useCallback(
		async (mapData: ILocation[]) => {
			await handleSaveKmlMutation.mutateAsync(mapData)
		},
		[handleSaveKmlMutation]
	)

	const deleteVehicleMutation = useMutation({
		mutationKey: ['deleteVehicleMutation'],
		mutationFn: async (id: string) => {
			const api = `/artisian-pro/${artisanProId}/site/${siteId}/${id}`
			const { data } = await authAxios.delete(api)
			return data
		},
		onError: (error: AxiosError) => {
			toast(
				(error?.response?.data as { messageToUser: string })?.messageToUser ??
					'Error deleting the vehicle.'
			)
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['getVehicleList'] })
		},
	})

	const handleDeleteVehicle = useCallback(
		(id: string) => {
			deleteVehicleMutation.mutate(id)
		},
		[deleteVehicleMutation]
	)

	return {
		containerList: containerListQuery?.data?.containers ?? [],
		vehicleList: vehicleListQuery?.data?.vehicles ?? [],
		kilnListQuery,
		samplingContainerListQuery,
		biomassSourceListQuery,
		handleDeleteContainer,
		handleFillSamplingContainer,
		handleDeleteBiomassSource,
		handleDeleteVehicle,
		vehicleListQuery,
		handleDeleteSamplingContainer,
		showMap,
		setShowMap,
		farmCoordinates,
		setSearchParams,
		navigate,
		handleSaveKml,
		setFarmCoordinates,
		containerListQuery,
		tab,
		setTab,
	}
}
