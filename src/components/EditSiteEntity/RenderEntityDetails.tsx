import { Divider, Stack } from '@mui/material'
import { NoData } from '../NoData'
import { theme } from '@/lib/theme/theme'
import { EntityColumns } from '../EditNetwork'
import {
	IContainerDetails,
	IFpu,
	ILocation,
	IOperator,
	ISamplingContainerDetails,
	IVehicle,
	Kiln,
} from '@/interfaces'
import { EntityTabEnum } from '@/utils/constant'
import { handleViewKMLFile } from '@/utils/helper'
import { NavigateFunction, SetURLSearchParams } from 'react-router-dom'

export const RenderEntityDetails = ({
	dataList,
	entityType,
	getSubText,
	renderExtraButtons,
	deleteButton = false,
	handleDelete,
	showWrapperPadding = true,
	navigate,
	setFarmCoordinates,
	setShowMap,
	setSearchParams,
}: {
	dataList?:
	| Kiln[]
	| IContainerDetails[]
	| ISamplingContainerDetails[]
	| IVehicle[]
	| IFpu[]
	| IOperator[]
	| any[]
	entityType: EntityTabEnum
	deleteButton?: boolean
	showWrapperPadding?: boolean
	getSubText?: (item: any) => string | JSX.Element
	handleDelete?: (id: string) => void
	renderExtraButtons?: (item: any, subText: string | JSX.Element) => JSX.Element
	navigate?: NavigateFunction
	setFarmCoordinates?: React.Dispatch<
		React.SetStateAction<google.maps.LatLng[] | google.maps.LatLngLiteral[]>
	>
	setShowMap?: (bool: boolean) => void
	setSearchParams?: SetURLSearchParams
}) => {
	const getEntityDetailsData = (
		item:
			| Kiln
			| IContainerDetails
			| ISamplingContainerDetails
			| IVehicle
			| IFpu
			| IOperator
			| any
	) => {
		switch (entityType) {
			case EntityTabEnum.kilns:
				return { kilnDetails: item }
			case EntityTabEnum.containers:
				return { containerDetails: item, isCsink: false }
			case EntityTabEnum.samplingContainer:
				return { containerDetails: item }
			case EntityTabEnum.vehicles:
				return { vehicleDetails: item }
			case EntityTabEnum.biomassSource:
				return {
					biomassSourceDetails: item,
					detailProps: navigate &&
						setFarmCoordinates &&
						setShowMap &&
						setSearchParams && {
						data: {
							name: item?.name ?? '',
							landmark: item?.address ?? '',
							coordinate: item?.coordinate ?? null,
							farmArea: item?.fpuArea ?? [],
							id: item?.id ?? '',
						},
						handleViewKMLFile: (
							farmCoordinates: ILocation[],
							center: {
								x: string
								y: string
							},
							networkId: string
						) =>
							handleViewKMLFile({
								center,
								farmCoordinates,
								navigate,
								networkId,
								setFarmCoordinates,
								setShowMap: () => setShowMap(true),
							}),
						setSearchParams,
						setShowMap: (bool: boolean) => setShowMap(bool),
					},
				}
			default:
				return null
		}
	}

	return (
		<Stack
			paddingTop={showWrapperPadding ? theme.spacing(2.5) : 0}
			gap={theme.spacing(1.5)}>
			{dataList?.length ? (
				dataList.map((item, index) => (
					<Stack key={index} alignItems='center'>
						<EntityColumns
							entityType={entityType}
							entityName={entityType != EntityTabEnum.vehicles ? item?.name : ""}
							subText={
								entityType != EntityTabEnum.samplingContainer
									? getSubText?.(item)
									: ''
							}
							icon={item?.imageURLs?.[0] || item?.profileImageUrl}
							deleteButton={deleteButton}
							editButton
							handleDelete={() => handleDelete?.(item?.id ?? '')}
							subheadingButtons={renderExtraButtons?.(
								item,
								getSubText?.(item) || ''
							)}
							{...getEntityDetailsData(item)}
						/>
						<Divider
							color={theme.palette.custom.grey[200]}
							sx={{ width: '80%' }}
						/>
					</Stack>
				))
			) : (
				<NoData size='small' />
			)}
		</Stack>
	)
}