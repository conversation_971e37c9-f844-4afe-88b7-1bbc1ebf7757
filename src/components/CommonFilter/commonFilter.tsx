import { Chip, Stack } from '@mui/material'
import { useCommonFilter } from './useCommonFilter'
import { QueryAutoComplete } from '../QueryInputs'
import { FC } from 'react'

type TCommonFilter = {
	showOnlyBaAndNetwork?: boolean
	showChipsForKiln?: boolean
	showDisabled?: boolean
}

export const CommonFilter: FC<TCommonFilter> = ({
	showOnlyBaAndNetwork,
	showChipsForKiln,
}) => {
	const { allFilter, chipsFilter, handleChipsClick, searchParams } =
		useCommonFilter(showOnlyBaAndNetwork, showChipsForKiln, )

	return (
		<>
			<Stack direction='row' gap={1} className='filter-container'>
				{allFilter?.map(
					({
						id,
						options,
						queryKey,
						filtersToReset,
						hideIfFilterNotPresent,
						label,
						isDisable,
						initialValue,
						onChangeSelect,
					}) => (
						<QueryAutoComplete
							key={id}
							options={options}
							queryKey={queryKey}
							filtersToReset={filtersToReset}
							hideIfFilterNotPresent={hideIfFilterNotPresent}
							label={label}
							isDisable={isDisable}
							initialValue={initialValue}
							onChangeSelect={onChangeSelect}
						/>
					)
				)}
			</Stack>
			<Stack direction='row' gap={1} className='filter-container' width='100%'>
				{showChipsForKiln &&
					chipsFilter?.options?.map((item, index) => (
						<Chip
							key={`filterChips-${index}`}
							label={item?.label}
							color={
								searchParams.get(chipsFilter?.queryKey)?.includes(item?.value)
									? 'primary'
									: 'default'
							}
							onClick={() =>
								handleChipsClick(chipsFilter?.queryKey, item?.value)
							}
						/>
					))}
			</Stack>
		</>
	)
}
