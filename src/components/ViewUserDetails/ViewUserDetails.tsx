import {
	IFarmForUser,
	IImage,
	TModalTypeForUserManagement,
	User,
} from '@/interfaces'
import { dateFormats, userRoles, userRolesNameUserManagement } from '@/utils/constant'
import { getGoogleMapLink, proxyImage } from '@/utils/helper'
import {
	ArrowDownward,
	ArrowDownwardOutlined,
	ArrowUpward,
	BadgeOutlined,
	Close,
	DeleteForeverOutlined,
	FmdGoodOutlined,
	MoreVert,
} from '@mui/icons-material'
import LogoutIcon from '@mui/icons-material/Logout'

import {
	Avatar,
	Box,
	IconButton,
	Link,
	ListItemIcon,
	ListItemText,
	Menu,
	MenuItem,
	Stack,
	styled,
	Typography,
} from '@mui/material'
import React, { FC, useCallback, useMemo, useState } from 'react'
import { TrainingProofRenderer } from '../TrainingProofRenderer'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { ImageGrid } from '../ImageGrid'
import { getFormattedDate } from '@/utils/helper/getFormattedDate'
import { toast } from 'react-toastify'
import { Confirmation } from '../Confirmation'
import { ImageCarouselDialog } from '../ImageCarousel'
import { SelectApplicationTypeForLogOut } from '../SelectApplicationTypeForLogOut'
import { ActionInformationDrawer } from '../ActionInformationDrawer'
import GenerateCertificateDrawer from '@/pages/dashboard/Admin/Settings/components/GenerateCertificateDrawer'
import { AddUser } from '../AddUser'
import CertificateDialog from './CertificateDialog'

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	userData: User
	handleModalTypeAndOpenDrawer: (id: TModalTypeForUserManagement) => void
	userList: User[]
}

export const ViewUserDetails: FC<TProps> = ({
	setIsActionInfoDrawer,
	userData,
	userList,
	handleModalTypeAndOpenDrawer,
}) => {
	const queryClient = useQueryClient()
	const [showConfirmationDialog, setShowConfirmationDialog] =
		useState<boolean>(false)
	const [showSelectApplicationTypeLogOut, setShowSelectApplicationTypeLogOut] =
		useState<boolean>(false)
	const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
	const open = Boolean(anchorEl)
	const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
		setAnchorEl(event.currentTarget)
	}
	const handleCloseMenu = () => {
		setAnchorEl(null)
	}
	const accountType = userRolesNameUserManagement[userData.accountType as userRoles]

	const { userDetails } = useAuthContext()

	const [imageToShow, setImageToView] = useState<IImage[]>([])

	const [showCertificateDrawer, setShowCertificateDrawer] = useState(false)
	const [showTrainingImagesDrawer, setshowTrainingImagesDrawer] =
		useState(false)

	const showDetails = useMemo(
		() => ({
			['csinkManager']: ![
				userRoles.Admin,
				userRoles.CirconomyEmployee,
				userRoles.compliance_manager
			].includes(userData?.accountType as userRoles),
			['Ba']: ![
				userRoles.CsinkManager,
				userRoles.Admin,
				userRoles.CirconomyEmployee,
				userRoles.compliance_manager
			].includes(userData?.accountType as userRoles),
			['artisanProNetwork']:
				[
					userRoles.ArtisanPro,
					userRoles.artisanProNetworkManager,
					userRoles.artisanProOperator,
				].includes(userData?.accountType as userRoles) ||
				!!userData?.artisanProId,
			['artisanPro']:
				[userRoles.ArtisanPro, userRoles.artisanProOperator].includes(
					userData?.accountType as userRoles
				) || !!userData?.artisanProId,
			['network']:
				[userRoles.cSinkNetwork, userRoles.kilnOperator].includes(
					userData?.accountType as userRoles
				) || !!userData?.csinkNetworkId,
		}),
		[userData?.accountType, userData?.artisanProId, userData?.csinkNetworkId]
	)

	const showMediaCount = useMemo(() => {
		const isEligibleRole = [
			userRoles.CsinkManager,
			userRoles.BiomassAggregator,
			userRoles.ArtisanPro,
			userRoles.artisanProNetworkManager,
			userRoles.cSinkNetwork,
			userRoles.csinkNetworkOperator,
			userRoles.artisanProOperator,
			userRoles.csink_operator_farmer,
		].includes(userData?.accountType as userRoles)

		const isAdmin = userDetails?.accountType === userRoles.Admin

		return isEligibleRole && isAdmin
	}, [userData?.accountType, userDetails?.accountType])

	const getFarms = useQuery({
		queryKey: ['farm_list'],
		queryFn: async () =>
			authAxios.get<IFarmForUser[]>(`/new/farmers/${userData?.id}/farms`),
		enabled: userData?.accountType === 'farmer',
	})

	const removeUserMutation = useMutation({
		mutationKey: ['removeUser'],
		mutationFn: async () => {
			if (!userData) throw new Error('User data is missing')

			let endpoint = ''
			switch (userData.accountType) {
				case userRoles.cSinkNetwork:
					endpoint = `/cs-network-manager/${userData?.id}?csinkNetworkId=${userData?.csinkNetworkId}`
					break
				case userRoles.csinkNetworkOperator:
					endpoint = `/cs-network/${userData?.csinkNetworkId}/operator/${userData?.id}`
					break
				case userRoles.artisanProOperator:
					endpoint = `/artisian-pro/${userData?.artisanProId}/artisian-pro-operator/${userData?.id}`
					break
				case userRoles.CsinkManager:
					endpoint = `/csink-manager/user/${userData?.id}`
					break
				case userRoles.BiomassAggregator:
					endpoint = `/biomass-aggregator/manager/${userData?.id}`
					break
				case userRoles.ArtisanPro:
					endpoint = `/artisan-pro-network/${userData?.artisanProNetworkId}/artisan-pro-manager/${userData?.id}`
					break
				case userRoles.artisanProNetworkManager:
					endpoint = `/artisan-pro-network/${userData?.artisanProNetworkId}/manager/${userData?.id}`
					break
				default:
					throw new Error('Unsupported account type')
			}

			const { data } = await authAxios.delete(endpoint)
			return data
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser || 'Failed to remove user')
		},
		onSuccess: ({ data }) => {
			toast(data?.message || 'User removed successfully')
			queryClient.refetchQueries({ queryKey: ['users'] })
			setIsActionInfoDrawer(false)
		},
	})

	const decreaseCountMutation = useMutation({
		mutationKey: ['removeUser'],
		mutationFn: async () => {
			const { data } = await authAxios.patch(
				`/user/${userData.id}/pending-uploads-count`
			)
			return data
		},
		onError: (error: any) => {
			toast(
				error?.response?.data?.messageToUser ||
				'Failed to decrease count of user'
			)
		},
		onSuccess: ({ data }) => {
			toast(data?.message || 'Counts decreased successfully')
			queryClient.refetchQueries({ queryKey: ['users'] })
			setIsActionInfoDrawer(false)
		},
	})

	const handleRemoveUser = useCallback(() => {
		removeUserMutation.mutate()
	}, [removeUserMutation])

	const handleMenuClickToRemoveUser = useCallback(() => {
		switch (userData?.accountType) {
			default:
				{
					setShowConfirmationDialog(true)
					handleCloseMenu()
				}
				break
		}
	}, [userData?.accountType])

	const showDelete = useMemo(() => {
		switch (userData?.accountType) {
			case userRoles.cSinkNetwork:
				return [
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
				].includes(userDetails?.accountType as userRoles)
			case userRoles.ArtisanPro:
				return [
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
				].includes(userDetails?.accountType as userRoles)
			case userRoles.CsinkManager:
				return userDetails?.accountType === userRoles.Admin
			case userRoles.BiomassAggregator:
				return [userRoles.Admin, userRoles.CsinkManager].includes(
					userDetails?.accountType as userRoles
				)
			case userRoles.csinkNetworkOperator:
			case userRoles.artisanProOperator:
				return [
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
				].includes(userDetails?.accountType as userRoles)
			case userRoles.artisanProNetworkManager:
				return [
					userRoles.Admin,
					userRoles.CsinkManager,
					userRoles.BiomassAggregator,
				].includes(userDetails?.accountType as userRoles)
			default:
				return false
		}
	}, [userData, userDetails])

	const menuItemList = useMemo(
		() => [
			{
				label: 'Edit Details',
				icon: (
					<ListItemIcon>
						<Box
							width={30}
							height={30}
							component='img'
							src='/images/editPencil.svg'
						/>
					</ListItemIcon>
				),
				onClick: () => handleModalTypeAndOpenDrawer('edit'),
				show: ![userRoles.Admin, userRoles.farmer, userRoles.compliance_manager].includes(
					userData?.accountType as userRoles
				),
			},
			{
				label:
					(userData?.accountType as userRoles) === userRoles.kilnOperator
						? 'Make Network Admin'
						: 'Make Artisan Pro Admin',
				icon: <ArrowUpward fontSize='small' />,
				onClick: () => handleModalTypeAndOpenDrawer('promote'),
				show:
					[userRoles.kilnOperator, userRoles.artisanProOperator].includes(
						userData?.accountType as userRoles
					) &&
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
						userRoles.artisanProNetworkManager,
					]?.includes(userDetails?.accountType as userRoles),
			},
			{
				label: 'Remove the User',
				icon: <DeleteForeverOutlined fontSize='small' />,
				onClick: handleMenuClickToRemoveUser,
				show: showDelete,
			},
			{
				label: 'Demote to Operator',
				icon: <ArrowDownwardOutlined fontSize='small' />,
				onClick: () => handleModalTypeAndOpenDrawer('demote'),
				show:
					[userRoles.cSinkNetwork, userRoles.ArtisanPro].includes(
						userData?.accountType as userRoles
					) &&
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
						userRoles.artisanProNetworkManager,
					]?.includes(userDetails?.accountType as userRoles),
			},
			{
				label: 'Promote to Csink Network Admin',
				icon: <ArrowUpward fontSize='small' />,
				onClick: () => handleModalTypeAndOpenDrawer('promote'),
				show:
					[userRoles.csinkNetworkOperator].includes(
						userData?.accountType as userRoles
					) &&
					[
						userRoles.Admin,
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
						userRoles.artisanProNetworkManager,
					]?.includes(userDetails?.accountType as userRoles),
			},
			{
				label: 'Logout from App',
				icon: <LogoutIcon fontSize='small' />,
				onClick: () => setShowSelectApplicationTypeLogOut(true),
				show:
					[
						userRoles.CsinkManager,
						userRoles.BiomassAggregator,
						userRoles.csinkNetworkOperator,
						userRoles.csink_operator_farmer,
						userRoles.artisanProOperator,
						userRoles.ArtisanPro,
						userRoles.artisanProNetworkManager,
					].includes(userData?.accountType as userRoles) &&
					[userRoles.Admin]?.includes(userDetails?.accountType as userRoles),
			},
			{
				label: 'Decrease Count to 0',
				icon: <ArrowDownward fontSize='small' />,
				onClick: () => decreaseCountMutation.mutate(),
				show: showMediaCount && (userData?.pendingUploadsCount ?? 0) > 0,
			},
			{
				label: 'Generate Certificate',
				icon: <BadgeOutlined fontSize='small' />,
				onClick: () => {
					if (userData.trainingImageUrls) {
						setShowCertificateDrawer(true)
						handleCloseMenu()
					} else {
						toast('Provide training images.')
						setshowTrainingImagesDrawer(true)
						handleCloseMenu()
					}
				},
				show: [
					userRoles.BiomassAggregator,
					userRoles.artisanProNetworkManager,
					userRoles.ArtisanPro,
					userRoles.cSinkNetwork,
					userRoles.artisanProOperator,
					userRoles.csink_operator_farmer,
					userRoles.csinkNetworkOperator,
				].includes(userData?.accountType as userRoles),
				disabled: userData?.certificateDetails?.id,
			},
		],
		[
			decreaseCountMutation,
			handleMenuClickToRemoveUser,
			handleModalTypeAndOpenDrawer,
			showDelete,
			showMediaCount,
			userData?.accountType,
			userData?.certificateDetails?.id,
			userData?.pendingUploadsCount,
			userData.trainingImageUrls,
			userDetails?.accountType,
		]
	)
	const isShowMenuOptions = useMemo(() => {
		return menuItemList.some((item) => item.show)
	}, [menuItemList])

	function formatAccountName(accountType: string): string {
		return accountType
			.split('_')
			.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
			.join(' ')
	}

	return (
		<>
			<ActionInformationDrawer
				open={showTrainingImagesDrawer}
				onClose={() => setshowTrainingImagesDrawer(false)}
				anchor='right'
				component={
					<AddUser
						setIsActionInfoDrawer={() => {
							setshowTrainingImagesDrawer(false)
							setShowCertificateDrawer(true)
						}}
						userData={userData}
						modalType={'edit'}
						addingTrainingImages
					/>
				}
			/>
			<ActionInformationDrawer
				open={
					showCertificateDrawer &&
					!!userList.find((user) => user.id === userData.id)?.trainingImageUrls
				}
				onClose={() => setShowCertificateDrawer(false)}
				anchor='right'
				component={
					<GenerateCertificateDrawer
						onClose={() => {
							setShowCertificateDrawer(false)
							setIsActionInfoDrawer(false)
						}}
						userData={userData}
					/>
				}
			/>

			{showConfirmationDialog ? (
				<Confirmation
					confirmationText={
						<Typography>
							Are you sure you want to remove this{' '}
							{formatAccountName(userData?.accountType ?? '')}?
						</Typography>
					}
					open={showConfirmationDialog}
					handleClose={() => setShowConfirmationDialog(false)}
					handleNoClick={() => setShowConfirmationDialog(false)}
					handleYesClick={handleRemoveUser}
				/>
			) : null}
			{showSelectApplicationTypeLogOut ? (
				<SelectApplicationTypeForLogOut
					userId={userData?.id}
					open={showSelectApplicationTypeLogOut}
					onClose={() => setShowSelectApplicationTypeLogOut(false)}
					cb={() => {
						queryClient.refetchQueries({ queryKey: ['users'] })
						setIsActionInfoDrawer(false)
					}}
				/>
			) : null}
			<StyleContainer>
				<Stack className='header'>
					<Stack
						direction='row'
						spacing={1}
						alignItems='center'
						width='100%'
						justifyContent='space-between'>
						<Typography variant='body2' className='first_letter_capitalize'>
							{userData?.name}
						</Typography>
						<IconButton onClick={() => setIsActionInfoDrawer(false)}>
							<Close />
						</IconButton>
					</Stack>
					<Typography className='first_letter_capitalize account_type'>
						{accountType}
					</Typography>
				</Stack>
				<Stack className='container'>
					<Stack className='user_connection_section'>
						<Stack className='profile_section'>
							<Avatar
								alt={userData?.name}
								src={
									userData?.profileImageUrl?.id
										? proxyImage(userData?.profileImageUrl?.path)
										: ''
								}
								sx={{
									width: 100,
									height: 100,
								}}
								onClick={() => {
									userData?.profileImageUrl?.path &&
										setImageToView([
											{
												fileName: userData?.profileImageUrl?.path,
												...userData?.profileImageUrl,
											},
										])
								}}
							/>
							<Stack>
								{isShowMenuOptions && (
									<IconButton onClick={handleOpenMenu}>
										<MoreVert />
									</IconButton>
								)}
								<Menu
									id='basic-menu'
									anchorEl={anchorEl}
									open={open}
									onClose={handleCloseMenu}
									MenuListProps={{
										'aria-labelledby': 'basic-button',
									}}>
									{menuItemList.map((item) =>
										item.show ? (
											<MenuItem
												onClick={item.onClick}
												key={item.label}
												disabled={Boolean(item?.disabled)}>
												<ListItemIcon>{item.icon}</ListItemIcon>
												<ListItemText>{item.label}</ListItemText>
											</MenuItem>
										) : null
									)}
								</Menu>
							</Stack>
						</Stack>
						<Stack className='user_common_details'>
							<Stack
								justifyContent='space-between'
								alignItems='center'
								direction='row'
								flexWrap='wrap'>
								{showDetails['csinkManager'] ? (
									<TagComponent
										label='Csink Manager'
										value={userData?.csinkManagerName}
									/>
								) : null}
								{showDetails['Ba'] ? (
									<TagComponent
										label='BA Name'
										value={userData?.biomassAggregatorName ?? ''}
									/>
								) : null}
							</Stack>
							{showDetails['artisanProNetwork'] || showDetails['artisanPro'] ? (
								<Stack
									justifyContent='space-between'
									alignItems='center'
									direction='row'
									flexWrap='wrap'>
									{showDetails['artisanProNetwork'] ? (
										<TagComponent
											label='Artisan Pro Network'
											value={userData?.artisanProNetworkName ?? ''}
										/>
									) : null}
									{showDetails['artisanPro'] ? (
										<TagComponent
											label='Artisan Pro'
											value={userData?.artisanProName ?? ''}
										/>
									) : null}
								</Stack>
							) : null}
							{showDetails['network'] ? (
								<TagComponent
									label='Network'
									value={userData?.csinkNetworkName ?? ''}
								/>
							) : null}
							{showMediaCount ? (
								<TagComponent
									label='Pending Count'
									value={userData?.pendingUploadsCount ?? 0}
								/>
							) : null}
							{/* <TagComponent
								label='Pending Action Count'
								value={userData?.pendingActionCount ?? 0}
							/> */}
						</Stack>
					</Stack>
					<Stack className='user_details_section'>
						<Typography variant='body2'>User details:</Typography>
						{/* TODO: Need to add this check in TagComponent for generic */}
						{userData?.email ? (
							<Stack className='tag_component'>
								<Typography className='font_size_14 font_weight_600'>
									Email Address :
								</Typography>
								<Typography className='font_size_14'>
									{userData?.email || '-'}
								</Typography>
							</Stack>
						) : null}
						{userData?.number ? (
							<TagComponent
								label='Phone Number'
								value={`${userData?.countryCode}-${userData?.number}`}
							/>
						) : null}
						{(userData?.trainingImageUrls ?? [])?.length > 0 ? (
							<Typography className='training'>Training Images:</Typography>
						) : null}
						{
							<TrainingProofRenderer
								media={(userData?.trainingImageUrls ?? [])?.map((i) => ({
									...i,
									fileName: i?.path,
								}))}
								viewMode='table'
								hideTitle
								showInRow
								componentSize={40}
							/>
						}
						{/* render certificates here*/}
						{userData?.certificateDetails?.id ? (
							<Stack
								gap={1}
								sx={{
									paddingTop: '5px',
								}}>
								<Typography className='training'>Certificate:</Typography>
								<CertificateDialog
									certificateData={userData?.certificateDetails}
								/>
							</Stack>
						) : null}
					</Stack>
					{userData?.accountType === 'farmer' ? (
						<Stack className='farm_detail_section'>
							<Typography variant='body2'>Farm details:</Typography>
							<Stack className='farm_list'>
								{(getFarms?.data?.data ?? [])?.map((farm) => (
									<FarmRenderer farmDetail={farm} key={farm.id} />
								))}
							</Stack>
						</Stack>
					) : null}
				</Stack>
			</StyleContainer>
			{imageToShow?.length ? (
				<ImageCarouselDialog
					open={!!imageToShow}
					close={() => {
						setImageToView([])
					}}
					ImagesList={imageToShow ?? []}
					imageIndex={0}
					showDownload={false}
				/>
			) : null}
		</>
	)
}

const FarmRenderer: FC<{ farmDetail: IFarmForUser }> = ({ farmDetail }) => {
	return (
		<Stack className='farm_details'>
			<Stack direction='row' alignItems='center' columnGap={1}>
				<Typography className='font_size_14 font_weight_600'>
					Farm Address:
				</Typography>
				<Stack
					direction='row'
					alignItems='center'
					component={Link}
					target='_blank'
					underline='hover'
					textTransform='none'
					color='common.black'
					href={getGoogleMapLink(
						`${farmDetail?.farmLocation?.x || ''}`,
						`${farmDetail?.farmLocation?.y || ''}`
					)}>
					<FmdGoodOutlined color='error' sx={{ width: 20, height: 20 }} />
					<Typography className='font_size_14 first_letter_capitalize'>
						{farmDetail?.landmark} ({farmDetail?.fieldSize}{' '}
						{farmDetail?.fieldSizeUnit})
					</Typography>
				</Stack>
			</Stack>
			<ImageGrid imageList={farmDetail?.farmImages ?? []} componentSize={40} />
			<Stack>
				{farmDetail?.farmCrops?.map((crop) => (
					<Stack key={crop?.id} className='farm_crops'>
						<TagComponent
							label='Farm Crop'
							value={`${crop?.cropName} (${getFormattedDate(
								crop?.createdAt,
								dateFormats.dd_MM_yyyy
							)})`}
						/>
						<TagComponent
							label='Stage'
							value={crop.cropStage.replace(/[-_]/g, ' ')}
						/>
					</Stack>
				))}
			</Stack>
		</Stack>
	)
}

const TagComponent: FC<{
	label: string
	value: string | number
	className?: string
}> = ({ label, value, className }) => {
	return (
		<Stack className={`tag_component ${className}`}>
			<Typography className='font_size_14 font_weight_600'>{label}:</Typography>
			<Typography className='font_size_14 first_letter_capitalize'>
				{value}
			</Typography>
		</Stack>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'column',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']
			}`,
		'.account_type': {
			fontSize: theme.typography.overline.fontSize,
			color: theme.palette.neutral[300],
		},
	},
	'.container': {
		padding: theme.spacing(2, 2.125),
		gap: theme.spacing(4),
		'.user_connection_section': {
			paddingBottom: theme.spacing(2),
			gap: theme.spacing(2),
			borderBottom: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']
				}`,
			'.profile_section': {
				justifyContent: 'space-between',
				flexDirection: 'row',
			},
			'.user_common_details': {
				gap: theme.spacing(2),
				padding: theme.spacing(0, 1),
			},
		},
		'.user_details_section': {
			'.training': {
				fontSize: theme.typography.subtitle2.fontSize,
				fontWeight: theme.typography.caption.fontWeight,
			},
		},
		'.farm_detail_section': {
			gap: theme.spacing(2),
			'.farm_list': {
				rowGap: theme.spacing(3),
				paddingLeft: theme.spacing(3.25),
			},
			'.farm_details': {
				gap: theme.spacing(2),
				'.farm_crops': {
					gap: theme.spacing(1),
					paddingLeft: theme.spacing(3.25),
				},
			},
		},
	},
	'.tag_component': {
		flexDirection: 'row',
		columnGap: 5,
		alignItems: 'center',
	},
	'.font_size_14': {
		fontSize: theme.typography.subtitle2.fontSize,
	},
	'.font_weight_600': {
		fontWeight: theme.typography.caption.fontWeight,
	},
}))
