import { ILabelWithValue } from '@/types'
import {
	Autocomplete,
	CircularProgress,
	TextField,
	useTheme,
} from '@mui/material'
import React, { FC, useCallback, useEffect, useRef } from 'react'
import { useSearchParams } from 'react-router-dom'

export interface extentedILabelWithValue extends ILabelWithValue {
	isArtisan?: boolean
}

interface Props {
	queryKey: string
	label: string
	options?: extentedILabelWithValue[]
	initialValue?: string
	filtersToReset?: string[]
	hideIfFilterNotPresent?: string[]
	isDisable: boolean
	onChangeSelect?: (props: {
		selectedOption: extentedILabelWithValue | null
		nsp: URLSearchParams
	}) => URLSearchParams
	loading?: boolean
}

export const QueryAutoComplete: FC<Props> = ({
	queryKey = 'id',
	label = 'label',
	initialValue,
	options = [],
	filtersToReset = [],
	hideIfFilterNotPresent = [],
	isDisable = false,
	loading,
	onChangeSelect = null,
}) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const theme = useTheme()
	const initialsed = useRef(false)
	const hasOnlyOneOption = options.length === 1

	const handleOnChange = useCallback(
		(selectedOption: extentedILabelWithValue | null) => {
			const nsp = new URLSearchParams(searchParams)
			filtersToReset.forEach((key) => {
				nsp.delete(key)
			})
			if (selectedOption) {
				nsp.set(queryKey, selectedOption.value)
			} else {
				nsp.delete(queryKey)
			}
			let param = nsp
			if (onChangeSelect) {
				param = onChangeSelect({ selectedOption, nsp })
			}
			setSearchParams(param, {
				replace: true,
			})
		},
		[searchParams, queryKey, filtersToReset, onChangeSelect, setSearchParams]
	)

	useEffect(() => {
		// Don't proceed if already initialized
		if (initialsed.current) {
			return
		}

		const nsp = new URLSearchParams(searchParams)
		const currentValue = searchParams.get(queryKey ?? '')

		// Auto-select if there's exactly one option and no current selection
		if (options.length === 1 && !currentValue) {
			const singleOption = options[0]
			nsp.set(queryKey, singleOption.value)

			let param = nsp
			if (onChangeSelect) {
				param = onChangeSelect({ selectedOption: singleOption, nsp })
			}

			setSearchParams(param, { replace: true })
			initialsed.current = true
			return
		}

		initialsed.current = true
	}, [
		initialValue,
		onChangeSelect,
		options,
		queryKey,
		searchParams,
		setSearchParams,
	])
	useEffect(() => {
		// Handle initial value if provided
		const nsp = new URLSearchParams(searchParams)

		const currentValue = searchParams.get(queryKey ?? '')

		if (initialValue && !currentValue) {
			const isValueInDropDown =
				options.find((option) => option.value === initialValue) ?? null

			if (isValueInDropDown) {
				nsp.set(queryKey, initialValue)

				let param = nsp
				if (onChangeSelect) {
					param = onChangeSelect({ selectedOption: isValueInDropDown, nsp })
				}

				setSearchParams(param, { replace: true })
			}
		}
	}, [
		initialValue,
		onChangeSelect,
		options,
		queryKey,
		searchParams,
		setSearchParams,
	])
	if (
		hideIfFilterNotPresent.length > 0 &&
		!hideIfFilterNotPresent.every((key) => searchParams.has(key))
	) {
		return null
	}

	return (
		<>
			<Autocomplete
				loading={loading}
				value={
					options.find(
						(option) => option.value === searchParams.get(queryKey)
					) ?? null
				}
				options={options}
				renderInput={(params) => (
					<TextField
						{...params}
						label={label}
						InputProps={{
							...params.InputProps,
							endAdornment: (
								<React.Fragment>
									{loading ? (
										<CircularProgress color='inherit' size={20} />
									) : null}
									{params.InputProps.endAdornment}
								</React.Fragment>
							),
						}}
						{...(hasOnlyOneOption ? { defaultValue: options?.[0]?.value } : {})}
					/>
				)}
				onChange={(_: unknown, selectedOption) => {
					handleOnChange(selectedOption)
				}}
				disabled={hasOnlyOneOption || !isDisable}
				sx={{
					width: 200,
					'.MuiOutlinedInput-root': {
						height: 38,
						'.MuiInputBase-input': {
							paddingTop: '2.5px',
							fontSize: theme.typography.subtitle1,
						},
					},
				}}
			/>
		</>
	)
}
