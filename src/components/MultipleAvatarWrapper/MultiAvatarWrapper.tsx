import { ImageURL } from '@/interfaces'
import { Stack, Typography } from '@mui/material'
import { MultipleAvatar } from '../MultipleAvatar.tsx'

export const MultipleAvatarWrapper: React.FC<{
	images: ImageURL[]
	length: number
}> = ({ images, length }) => {
	const getCount = (length: number) => {
		if (length > 2) {
			return `+ ${length - 2}`
		}
		return ''
	}

	return (
		<Stack direction='row' alignItems='center' columnGap={1}>
			<MultipleAvatar size={30} imageList={images} MaxAvatar={3} />
			<Typography variant='subtitle1'>{getCount(length)}</Typography>
		</Stack>
	)
}
