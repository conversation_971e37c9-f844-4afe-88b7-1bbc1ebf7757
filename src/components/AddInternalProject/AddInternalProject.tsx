import { Close } from '@mui/icons-material'
import {
	Autocomplete,
	BaseTextFieldProps,
	Button,
	FormControl,
	FormHelperText,
	IconButton,
	InputLabel,
	MenuItem,
	OutlinedInput,
	Select,
	SelectChangeEvent,
	Stack,
	styled,
	TextField,
	Typography,
	useTheme,
} from '@mui/material'
import React, { useCallback, useEffect, useMemo } from 'react'
import {
	FieldErrors,
	useForm,
	UseFormClearErrors,
	UseFormGetValues,
	UseFormSetValue,
	UseFormWatch,
	useWatch,
} from 'react-hook-form'
import { addInternalProject, TAddInternalProject } from './schema'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import moment from 'moment'
import { ILabelWithValue } from '@/types'
import { LoadingButton } from '@mui/lab'
import { yupResolver } from '@hookform/resolvers/yup'
import { useMutation, useQuery } from '@tanstack/react-query'
import { toast } from 'react-toastify'
import { authAxios } from '@/contexts'
import {
	IGlobalProducers,
	IInternalProjectDetails,
	INetworkForInternalProjectResponse,
} from '@/interfaces'
import { TCustomCsinkManager } from '../SideDrawerComponents/AddEntity/schema'
import { AxiosError } from 'axios'
import { AnyObjectSchema } from 'yup'
import { CustomFileUploader } from '../CustomFileUploader'

const acceptedImageTypes = ['png', 'jpg', 'jpeg', 'webp']
const ITEM_HEIGHT = 48
const ITEM_PADDING_TOP = 8
const MenuProps = {
	PaperProps: {
		style: {
			maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
			width: 250,
		},
	},
}

type TProps = {
	setIsActionInfoDrawer: React.Dispatch<React.SetStateAction<boolean>>
	cb?: () => void
	isEdit?: boolean
	projectDetails?: IInternalProjectDetails | null
	isProjectCodeHidden?: boolean | null
}

type TOption = ILabelWithValue & {
	isArtisan: boolean
}

type TFieldRenderer = {
	type: string
	field: any
	textFieldProps: Omit<BaseTextFieldProps, 'label'>
	label: string
	options: TOption[] | ILabelWithValue[]
	getValues: UseFormGetValues<TAddInternalProject>
	setValue: UseFormSetValue<TAddInternalProject>
	clearErrors: UseFormClearErrors<TAddInternalProject>
	errors: FieldErrors<TAddInternalProject>
	schema?: AnyObjectSchema
	watch: UseFormWatch<TAddInternalProject>
	isEdit: boolean
}
interface OptionsType {
	csinkManager: ILabelWithValue[]
	selectedNetwork: TOption[]
	producerNumber: ILabelWithValue[]
	[key: string]: TOption[] | ILabelWithValue[]
}

const initialValue = {
	name: '',
	projectId: '',
	registryProjectName: '',
	projectCode: '',
	startDate: null,
	selectedNetwork: [],
	comment: '',
	csinkManager: '',
	projectLocation: '',
	projectSummary: '',
	projectImageId: '',
}

export const AddInternalProject: React.FC<TProps> = ({
	setIsActionInfoDrawer,
	cb,
	isEdit = false,
	projectDetails,
	isProjectCodeHidden,
}) => {
	const theme = useTheme()
	const {
		register,
		getValues,
		setValue,
		watch,
		clearErrors,
		control,
		formState: { errors },
		handleSubmit,
	} = useForm<TAddInternalProject>({
		defaultValues: initialValue,
		mode: 'all',
		resolver: yupResolver<TAddInternalProject>(
			addInternalProject(!isProjectCodeHidden, isEdit)
		),
	})

	const csinkManagerId = useWatch({ name: 'csinkManager', control })
	const fields = [
		{
			id: 'name',
			label: 'Name',
			component: 'text',
			hidden: false,
		},
		{
			id: 'projectId',
			label: 'Project ID',
			component: 'text',
			hidden: false,
		},
		{
			id: 'registryProjectName',
			label: 'Registry Project Name',
			component: 'text',
			hidden: false,
		},
		{
			id: 'projectCode',
			label: 'Project Code',
			component: 'text',
			hidden: isProjectCodeHidden,
		},
		{
			id: 'startDate',
			label: 'Start Date',
			component: 'datePicker',
			hidden: isEdit,
		},
		{
			id: 'csinkManager',
			label: 'Select Csink Manager',
			component: 'select',
			hidden: isEdit,
		},
		{
			id: 'producerNumber',
			label: 'Select Producer',
			component: 'select',
			hidden: projectDetails?.producerNumber ? true : false,
		},

		{
			id: 'selectedNetwork',
			label: 'Select Network',
			component: 'multiple-select',
			hidden: false,
		},
		{
			id: 'projectLocation',
			label: 'Project Location',
			component: 'text',
			hidden: false,
		},
		{
			id: 'projectSummary',
			label: 'Project Summary',
			component: 'textarea',
			hidden: false,
		},

		{
			id: 'comment',
			label: 'Comment',
			component: 'textarea',
			hidden: isEdit,
		},
	]

	const getNetworkForInternalProjects = useQuery({
		queryKey: ['networksForInternalProjects', csinkManagerId],
		queryFn: async () => {
			return await authAxios.get<INetworkForInternalProjectResponse>(
				`/internal-project/networks-without-internal-project?csinkManagerId=${csinkManagerId}`
			)
		},
		select: ({ data }) => {
			return (data?.networks ?? [])?.map((network) => ({
				label: `${network.name} (${network.shortName})`,
				value: network.id,
				isArtisan: network.isArtisan,
			}))
		},
		enabled: !!csinkManagerId,
	})

	const getGlobalProducersQuery = useQuery({
		queryKey: ['networksForInternalProjects', watch],
		queryFn: async () => {
			return await authAxios.get<IGlobalProducers[]>(`/global-csink/producers`)
		},
		select: ({ data }) => {
			return {
				producers: data,
				labelValue: (data ?? [])?.map((network) => ({
					label: `${network.operator_name} (${network.operator_number})`,
					value: network.operator_number,
				})),
			}
		},
	})

	const fetchCsinkManager = useQuery({
		queryKey: ['allCsinkManager'],
		queryFn: () => {
			const queryParams = new URLSearchParams({
				limit: '500',
				page: '0',
			})
			return authAxios.get<{
				csinkManagers: TCustomCsinkManager[]
			}>(`/csink-manager?${queryParams.toString()}`)
		},
		select: ({ data }) =>
			data?.csinkManagers?.map((item) => ({
				label: `${item.name} (${item.shortName})`,
				value: item.id,
			})),
	})

	const selectedNetworksOptions = useMemo(() => {
		let networks: TOption[] = []
		const artisanNetworks = projectDetails?.artisanProDetails?.map((i) => ({
			label: i?.name,
			value: i?.id,
			isArtisan: true,
		}))

		artisanNetworks?.length && networks.push(...artisanNetworks)
		projectDetails?.csinkNetworkDetails?.length &&
			networks.push(
				...projectDetails.csinkNetworkDetails.map((i) => ({
					label: i?.name,
					value: i?.id,
					isArtisan: false,
				}))
			)
		getNetworkForInternalProjects?.data?.length &&
			networks.push(...getNetworkForInternalProjects?.data)

		return networks
	}, [
		getNetworkForInternalProjects?.data,
		projectDetails?.artisanProDetails,
		projectDetails?.csinkNetworkDetails,
	])
	const getOptions: OptionsType = useMemo(() => {
		return {
			csinkManager: fetchCsinkManager?.data || [],
			selectedNetwork: selectedNetworksOptions || [],
			producerNumber: getGlobalProducersQuery?.data?.labelValue || [],
		}
	}, [
		fetchCsinkManager?.data,
		getGlobalProducersQuery?.data?.labelValue,
		selectedNetworksOptions,
	])

	const addProjectMutation = useMutation({
		mutationKey: ['addProject'],
		mutationFn: async (values: TAddInternalProject) => {
			const payload = {
				name: values?.name,
				comments: values?.comment ? values?.comment : null,
				projectId: values?.projectId,
				registryProjectName: values?.registryProjectName,
				code: values?.projectCode,
				csinkManagerId: values?.csinkManager,
				artisanProIds:
					values?.selectedNetwork
						?.filter((i) => i.isArtisan)
						.map((i) => i.id) ?? [],
				csinkNetworkIds:
					values?.selectedNetwork
						?.filter((i) => !i.isArtisan)
						.map((i) => i.id) ?? [],
				startTime: values?.startDate || null,
				producerId: values?.producerId,
				producerNumber: getGlobalProducersQuery?.data?.producers?.find(
					(i) => i.operator_number === values?.producerNumber
				)?.operator_number,
				projectLocation: values?.projectLocation,
				projectSummary: values?.projectSummary,
				projectImageId: values?.projectImageId,
			}
			return await authAxios.post(`internal-project/v2`, payload)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			cb?.()
		},
	})
	const editProjectMutation = useMutation({
		mutationKey: ['editProject'],
		mutationFn: async (values: TAddInternalProject) => {
			const payload = {
				name: values?.name,
				projectId: values?.projectId,
				registryProjectName: values?.registryProjectName,
				code: values?.projectCode ? values?.projectCode : null,
				artisanProIds:
					values?.selectedNetwork
						?.filter((i) => i.isArtisan)
						.map((i) => i.id) ?? [],
				csinkNetworkIds:
					values?.selectedNetwork
						?.filter((i) => !i.isArtisan)
						.map((i) => i.id) ?? [],
				producerId: values?.producerId,
				producerNumber: getGlobalProducersQuery?.data?.producers?.find(
					(i) => i.operator_number === values?.producerNumber
				)?.operator_number,
				projectLocation: values?.projectLocation,
				projectSummary: values?.projectSummary,
				projectImageId: values?.projectImageId,
			}

			return await authAxios.put(
				`internal-project/${projectDetails?.id}/edit`,
				payload
			)
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			setIsActionInfoDrawer(false)
			cb?.()
		},
	})
	const handleChangeCsinkManager = useCallback(
		(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
			setValue('csinkManager', e.target.value)
			clearErrors('csinkManager')
			setValue('selectedNetwork', [])
		},
		[clearErrors, setValue]
	)

	useEffect(() => {
		if (!isEdit || !projectDetails) return
		setValue('name', projectDetails?.name)
		const networks: { id: string; isArtisan: boolean }[] = []
		const artisanNetworks = projectDetails.artisanProDetails.map((i) => ({
			id: i?.id,
			isArtisan: true,
		}))

		projectDetails?.artisanProDetails?.length &&
			networks.push(...artisanNetworks)
		projectDetails?.csinkNetworkDetails?.length &&
			networks.push(
				...projectDetails.csinkNetworkDetails.map((i) => ({
					id: i?.id,
					isArtisan: false,
				}))
			)
		setValue('registryProjectName', projectDetails?.registryProjectName)
		setValue('csinkManager', projectDetails?.csinkManagerId)
		setValue('selectedNetwork', networks)
		setValue('producerNumber', projectDetails?.producerNumber)
		setValue('projectId', projectDetails?.projectID)
		setValue('projectImageId', projectDetails?.projectImage?.id)
		setValue('projectCode', projectDetails?.code)
		setValue('startDate', moment(projectDetails?.startTime).toDate())
		setValue('comment', projectDetails?.comments)
		setValue('projectLocation', projectDetails?.projectLocation)
		setValue('projectSummary', projectDetails?.projectSummary)
	}, [fetchCsinkManager.data, isEdit, projectDetails, setValue])

	const handleFormSubmit = useCallback(
		(values: TAddInternalProject) => {
			return isEdit
				? editProjectMutation.mutate(values)
				: addProjectMutation.mutate(values)
		},
		[addProjectMutation, editProjectMutation, isEdit]
	)

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h4'>
						{isEdit ? 'Edit' : 'Add'} Project
					</Typography>
					<IconButton onClick={() => setIsActionInfoDrawer(false)}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container'>
				{fields.map((field) => {
					return (
						<FieldRenderer
							key={field.id}
							field={field}
							type={field.component}
							watch={watch}
							isEdit={isEdit}
							schema={addInternalProject(!!isProjectCodeHidden, isEdit)}
							textFieldProps={{
								...register(field.id as keyof TAddInternalProject),
								...(field.id === 'csinkManager' && {
									onChange: (
										e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
									) => handleChangeCsinkManager(e),
									value: watch('csinkManager') || '',
								}),
								...(field.id === 'producerNumber' && {
									onChange: (
										e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
									) => setValue('producerNumber', e.target.value),
									value: watch('producerNumber') || '',
								}),
								error:
									!!errors?.[field?.id as keyof TAddInternalProject]?.message,
								helperText:
									errors?.[field?.id as keyof TAddInternalProject]?.message,
								disabled: field?.hidden || false,
							}}
							label={field.label}
							getValues={getValues}
							setValue={setValue}
							clearErrors={clearErrors}
							errors={errors}
							options={getOptions?.[field?.id] || []}
						/>
					)
				})}
				<FormControl>
					<Stack rowGap={2} width='100%'>
						<CustomFileUploader
							acceptFileTypes={acceptedImageTypes}
							heading='Add Project Image'
							sx={{
								height: { xs: 100, md: 150 },
								width: '100%',
							}}
							imageHeight={100}
							mediaType='image'
							setUploadData={(data) => {
								setValue('projectImageId', data?.id)
								clearErrors('projectImageId')
							}}
							imageUrl={projectDetails?.projectImage?.url}
						/>
						<FormHelperText error={Boolean(errors.projectImageId)}>
							{errors?.projectImageId?.message}
						</FormHelperText>
					</Stack>
				</FormControl>

				<Stack
					direction='row'
					justifyContent='space-between'
					className='buttonContainer'>
					<Button
						onClick={() => setIsActionInfoDrawer(false)}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>{' '}
					<LoadingButton
						loading={addProjectMutation.isPending}
						disabled={addProjectMutation.isPending}
						onClick={handleSubmit(handleFormSubmit)}
						variant='contained'>
						{isEdit ? 'Save' : 'Add'}
					</LoadingButton>
				</Stack>
			</Stack>
		</StyleContainer>
	)
}

const FieldRenderer: React.FC<TFieldRenderer> = ({
	type,
	textFieldProps,
	label,
	options,
	getValues,
	setValue,
	clearErrors,
	watch,
	isEdit,
	field,
	errors,
}) => {
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const { value } = textFieldProps

	const selectedNetworksValue = useMemo(
		() => watch('selectedNetwork')?.map((i) => i?.id),
		[watch('selectedNetwork')]
	)

	const handleSelectMultipleNetwork = (event: SelectChangeEvent<string[]>) => {
		const {
			target: { value },
		} = event

		const temp: TOption[] = []
		;((value as string[]) ?? [])?.forEach((id: string) => {
			const obj = options.find((i) => i.value === id)
			temp.push(obj as TOption)
		})

		setValue(
			'selectedNetwork',
			(temp ?? [])?.map((i) => ({
				id: i?.value,
				isArtisan: i?.isArtisan,
			}))
		)
		clearErrors('selectedNetwork')
	}

	switch (type) {
		case 'text':
			return (
				<TextField
					{...textFieldProps}
					label={label}
					InputLabelProps={{
						...(isEdit && { shrink: true }),
					}}
				/>
			)
		case 'datePicker':
			return (
				<FormControl fullWidth sx={{ flex: 1 }} error={textFieldProps.error}>
					<LocalizationProvider dateAdapter={AdapterMoment}>
						<DatePicker
							label={label}
							value={
								getValues('startDate') ? moment(getValues('startDate')) : null
							}
							onChange={(newValue: moment.Moment | null) => {
								setValue('startDate', moment(newValue).set('hour', 11).toDate())
								clearErrors('startDate')
							}}
							format='DD/MM/YYYY'
							disabled={isEdit && !!getValues('startDate')}
							slotProps={{
								layout: {
									sx: {
										'.MuiPickersDay-root': {
											color: 'black',
											fontWeight: 450,
										},
										'.MuiPickersDay-root.Mui-selected': {
											color: 'white',
										},
										'.MuiPickersDay-root.Mui-disabled': {
											color: 'gray',
										},
									},
								},
							}}
						/>
					</LocalizationProvider>

					<FormHelperText error={!!errors?.startDate?.message}>
						{errors?.startDate?.message}
					</FormHelperText>
				</FormControl>
			)
		case 'multiple-select':
			return (
				<FormControl fullWidth error={textFieldProps.error}>
					<InputLabel>{label}</InputLabel>
					<Select
						value={selectedNetworksValue}
						renderValue={(val) => {
							const render = val.map(
								(v) => options.find((op) => op.value === v)?.label ?? ''
							)
							return render.join(', ')
						}}
						multiple
						onChange={handleSelectMultipleNetwork}
						input={<OutlinedInput label={label} />}
						MenuProps={MenuProps}>
						{options.map((option) => (
							<MenuItem key={option.value} value={option.value}>
								{option.label}
							</MenuItem>
						))}
					</Select>
					<FormHelperText error={textFieldProps.error}>
						{textFieldProps.helperText}
					</FormHelperText>
				</FormControl>
			)
		case 'select': {
			return (
				<Autocomplete
					disabled={
						isEdit &&
						(field.id === 'csinkManager' ||
							(field.id === 'producerNumber' && field?.hidden))
					}
					value={options.find((option) => option.value === value) || null}
					options={options}
					getOptionLabel={(option) => option.label}
					renderInput={(params) => (
						<TextField {...params} label={field.label} variant='outlined' />
					)}
					onChange={(_, selectedOption) => {
						setValue(field.id, selectedOption ? selectedOption.value : '')
					}}
					clearOnEscape
					disableClearable={false}
					fullWidth
				/>
			)
		}
		case 'textarea':
			return <TextField {...textFieldProps} label={label} multiline rows={6} />
		default:
			return null
	}
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		gap: theme.spacing(2),
		'.formcontrol': {
			gap: theme.spacing(0.6),
			'.label': {
				color: theme.palette.neutral[500],
			},
		},
		'.buttonContainer': {
			columnGap: theme.spacing(2),
			button: {
				width: '100%',
				height: theme.spacing(4.5),
				padding: theme.spacing(1, 2.5),
			},
		},
	},
}))
