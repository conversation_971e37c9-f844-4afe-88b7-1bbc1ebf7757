import { Add, Close } from '@mui/icons-material'
import { Button, IconButton, Stack, styled, Typography } from '@mui/material'
import { TagComponent } from '../CustomTagComponent'
import { theme } from '@/lib/theme/theme'
import { useCallback } from 'react'
import { FormProvider, useFieldArray, useForm } from 'react-hook-form'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { EntityEnum, IBiomassReference } from '@/interfaces'
import { ICrop } from '@/types'
import { AddBiomassPreProcessFields } from './AddBiomassReferenceFields'
import { LoadingButton } from '@mui/lab'
import { toast } from 'react-toastify'
import { yupResolver } from '@hookform/resolvers/yup'
import { addBiomassReferenceSchema, TAddBiomassreference } from './schema'

interface IProps {
	onClose: () => void
	networkType: EntityEnum
	id: string
}
const handleRefetchQueries = (networkType: EntityEnum) => {
	switch (networkType) {
		case EntityEnum.cSinkManager:
			return 'allCsinkManager'
		case EntityEnum.cSinkNetwork:
			return 'allcSinkNetwork'
		case EntityEnum.aps:
			return 'allaps'
		case EntityEnum.ba:
			return 'allBA'
		default:
			return ''
	}
}
export const AddBiomassReference = ({ onClose, networkType, id }: IProps) => {
	const queryClient = useQueryClient()
	const getApiRoutes = useCallback(() => {
		switch (networkType) {
			case EntityEnum.cSinkManager:
				return 'csink-manager'
			case EntityEnum.aps:
				return 'artisian-pro'
			case EntityEnum.ba:
				return 'biomass-aggregator'
			case EntityEnum.cSinkNetwork:
				return 'cs-network'
		}
	}, [networkType])
	const biomassReferenceQuery = useQuery({
		queryKey: ['biomassReferenceQuery', id],
		queryFn: async () => {
			const apiRoute = `${getApiRoutes()}/${id}/biomass-reference`
			return await authAxios.get<{ biomass: IBiomassReference[] }>(apiRoute)
		},
		enabled: !!id,
	})
	const fetchBiomassTypeList = useQuery({
		queryKey: ['fetchBiomassTypeList'],
		queryFn: async () => {
			const { data } = await authAxios.get<{ count: number; crops: ICrop[] }>(
				`/crops?limit=5000`
			)
			return data
		},

		select: (data) =>
			data?.crops?.map((item) => ({
				label: item?.name,
				value: item?.id,
			})),
	})

	const form = useForm<TAddBiomassreference>({
		defaultValues: { biomass: [] },
		resolver: yupResolver(addBiomassReferenceSchema),
	})
	const { control, handleSubmit } = form

	const { fields, append } = useFieldArray({
		control,
		name: 'biomass',
	})
	const handleAdd = useCallback(() => {
		append({
			id: Math.random().toString(36).substring(2, 9),
			biomassTypeId: '',
			fieldSize: 1,
			fieldSizeUnit: 'hectare',
			biomassQuantity: 0,
		})
	}, [append])

	const addBiomassReferencesMutation = useMutation({
		mutationKey: ['AddBiomassReferencesMutation', networkType],
		mutationFn: async (values: TAddBiomassreference) => {
			const apiRoute = `/${getApiRoutes()}/${id}/biomass-reference`

			const { data } = await authAxios.post(apiRoute, values)
			return data
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient?.refetchQueries({
				queryKey: [handleRefetchQueries(networkType)],
			})
			onClose()
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
	})
	const handleSave = useCallback((values: TAddBiomassreference) => {
		addBiomassReferencesMutation.mutate(values)
	}, [])

	return (
		<StyledStack
			sx={{
				display: 'flex',
				flexDirection: 'column',
				height: '100vh',
			}}>
			<Stack className='header'>
				<Typography variant='body2'>Biomass Reference</Typography>
				<IconButton onClick={onClose}>
					<Close />
				</IconButton>
			</Stack>
			<FormProvider {...form}>
				<Stack className='container'>
					{biomassReferenceQuery?.data?.data?.biomass?.map((item) => (
						<Stack className='reference-container'>
							<Stack flexDirection='row' gap={2}>
								<Stack sx={{ flexDirection: 'column' }} width='100%'>
									<TagComponent
										label='Biomass Name'
										labelClassName='label'
										value={item?.biomassName}
									/>
									<TagComponent
										label='Biomass Qty'
										labelClassName='label'
										value={item?.biomassQuantity}
									/>
								</Stack>
							</Stack>
							<Stack flexDirection='row'>
								<TagComponent
									label='Field Size'
									labelClassName='label'
									value={`${item?.fieldSize} ${item?.fieldSizeUnit}`}
								/>
							</Stack>
						</Stack>
					))}

					<Stack>
						<Button
							startIcon={<Add color='primary' />}
							onClick={handleAdd}
							sx={{
								flex: 'right',
								justifyContent: 'flex-end',
							}}>
							Add Biomass Reference
						</Button>
					</Stack>
					<Stack gap={theme.spacing(2)}>
						{fields?.map((x, index) => (
							<AddBiomassPreProcessFields
								key={x.id}
								fieldIndex={index}
								crops={fetchBiomassTypeList?.data ?? []}
							/>
						))}
					</Stack>
				</Stack>
				<Stack className='buttonContainer'>
					<LoadingButton
						loading={addBiomassReferencesMutation?.isPending}
						disabled={addBiomassReferencesMutation?.isPending}
						onClick={handleSubmit(handleSave)}
						variant='contained'>
						Save
					</LoadingButton>
					<Button
						onClick={onClose}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
				</Stack>
			</FormProvider>
		</StyledStack>
	)
}

const StyledStack = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	height: '100vh',
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		gap: theme.spacing(3),
		padding: theme.spacing(0, 2),
		flex: 1,
		'.reference-container': {
			flexDirection: 'row',
			justifyContent: 'space-between',
			width: '100%',
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
			padding: theme.spacing(2),
			'.label': {
				fontWeight: 500,
			},
		},
		'.refernceFields-container': {
			gap: theme.spacing(4),
			padding: theme.spacing(3, 0),
			borderBottom: `${theme.spacing(0.125)} solid ${
				theme.palette.neutral['100']
			}`,
		},
	},
	'.buttonContainer': {
		gap: theme.spacing(2),
		flexDirection: 'row',
		padding: theme.spacing(2),
		button: {
			width: theme.spacing(30),
			height: theme.spacing(4.5),
			padding: theme.spacing(1, 2.5),
			marginBottom: theme.spacing(5),
		},
	},
}))
