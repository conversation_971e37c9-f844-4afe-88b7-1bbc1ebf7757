import * as yup from 'yup'
export const addBiomassReferenceSchema = yup.object({
	biomass: yup
		.array()
		.of(
			yup.object({
				id: yup.string().notRequired(),
				biomassTypeId: yup.string().nullable().required(),
				fieldSize: yup
					.number()
					.nullable()
					.required()
					.typeError('field size is required'),
				fieldSizeUnit: yup.string().nullable().required(),
				biomassQuantity: yup
					.number()
					.nullable()
					.required()
					.typeError('Biomass Quantity is required'),
			})
		)
		.notRequired(),
})

export type TAddBiomassreference = yup.InferType<
	typeof addBiomassReferenceSchema
>
