import { useFormContext } from 'react-hook-form'
import { TwoColumnLayout } from '../TwoColumnLayout'
import {
	FormControl,
	InputAdornment,
	MenuItem,
	Stack,
	TextField,
} from '@mui/material'
import { CustomTextField } from '@/utils/components'
import { theme } from '@/lib/theme/theme'

const unitlist = [
	{
		label: 'Hectare',
		value: 'hectare',
	},
	{
		label: 'Acre',
		value: 'acre',
	},
]
interface IProps {
	fieldIndex: number
	crops: {
		label: string
		value: string
	}[]
}
export const AddBiomassPreProcessFields = ({ fieldIndex, crops }: IProps) => {
	const { register, watch } = useFormContext()

	return (
		<Stack className='refernceFields-container'>
			<TwoColumnLayout
				gridBreakpoints={[6, 6]}
				spacing={2}
				left={
					<FormControl fullWidth>
						<TextField
							select
							id='biomassTypeId'
							value={watch(`biomass.${fieldIndex}.biomassTypeId`)}
							label='Select Biomass Type'
							placeholder='Biomass Type'
							{...register(`biomass.${fieldIndex}.biomassTypeId`)}
							SelectProps={{
								MenuProps: {
									PaperProps: {
										sx: {
											maxHeight: theme.spacing(40),
										},
									},
									anchorOrigin: {
										vertical: 'bottom',
										horizontal: 'center',
									},
								},
							}}>
							{crops?.map((item, idx) => (
								<MenuItem key={idx + item?.value} value={item?.value}>
									{item?.label}
								</MenuItem>
							))}
						</TextField>
					</FormControl>
				}
				right={
					<TextField
						type='number'
						InputProps={{
							endAdornment: (
								<InputAdornment position='start'>kg</InputAdornment>
							),
						}}
						onKeyDown={(e) => {
							if (e.key === '.') {
								e.preventDefault()
							}
						}}
						onPaste={(e) => {
							const pasted = e.clipboardData.getData('text')
							if (pasted.includes('.')) {
								e.preventDefault()
							}
						}}
						id='biomassQuantity'
						value={watch(`biomass.${fieldIndex}.biomassQuantity`)}
						label='Biomass Quantity'
						placeholder='Enter Biomass Quantity'
						{...register(`biomass.${fieldIndex}.biomassQuantity`)}
					/>
				}
			/>
			<TwoColumnLayout
				gridBreakpoints={[6, 6]}
				spacing={2}
				right={
					<FormControl fullWidth>
						<TextField
							select
							id='fieldSizeUnit'
							value={watch(`biomass.${fieldIndex}.fieldSizeUnit`)}
							label='Select Field Unit'
							placeholder='Field Unit'
							{...register(`biomass.${fieldIndex}.fieldSizeUnit`)}
							SelectProps={{
								MenuProps: {
									anchorOrigin: {
										vertical: 'bottom',
										horizontal: 'center',
									},
								},
							}}>
							{unitlist?.map((item, idx) => (
								<MenuItem key={idx + item?.value} value={item?.value}>
									{item?.label}
								</MenuItem>
							))}
						</TextField>
					</FormControl>
				}
				left={
					<FormControl fullWidth>
						<CustomTextField
							hideNumberArrows
							type='number'
							id='fieldSize'
							value={watch(`biomass.${fieldIndex}.fieldSize`)}
							label='Field Size'
							placeholder='Enter Field Size'
							{...register(`biomass.${fieldIndex}.fieldSize`)}
						/>
					</FormControl>
				}
			/>
		</Stack>
	)
}
