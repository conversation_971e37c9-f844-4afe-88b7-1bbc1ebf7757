import { authAxios } from '@/contexts'
import { ICsinkOperator } from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import { TModal } from '@/types'
import { Cancel } from '@mui/icons-material'
import {
	Dialog,
	IconButton,
	DialogTitle,
	DialogContent,
	Stack,
	DialogActions,
	Button,
	MenuItem,
	Select,
	FormControl,
	InputLabel,
	OutlinedInput,
} from '@mui/material'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useState } from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'

interface IProps extends TModal {
	id: string
}
export const AssignOperatortoFarmer = ({ open, onClose, id }: IProps) => {
	const [selectedOperators, setSelectedOperators] = useState<string[]>([])
	const queryClient = useQueryClient()
	const { cSinkNetworkId } = useParams()

	const fetchUnassignedOperators = useQuery({
		queryKey: ['fetchUnassignedOperators', cSinkNetworkId, id],
		queryFn: () => {
			return authAxios<{ count: number; operators: ICsinkOperator[] }>(
				`/cs-network/${cSinkNetworkId}/site/${id}/operator`
			)
		},
		enabled: !!cSinkNetworkId && !!id,
	})
	const assignOperatorMutation = useMutation({
		mutationKey: ['AssignOperator', selectedOperators],
		mutationFn: async () => {
			const payload = {
				operatorIds: selectedOperators,
			}
			return authAxios.post(
				`/cs-network/${cSinkNetworkId}/site/${id}/operator/assign`,
				payload
			)
		},
		onSuccess: (data) => {
			toast(data?.data?.message)
			queryClient.refetchQueries({ queryKey: ['allCsinkManager'] })
			onClose()
			setSelectedOperators([])
		},
		onError: (err: AxiosError) => {
			toast((err?.response?.data as { messageToUser: string })?.messageToUser)
		},
	})

	return (
		<Dialog
			open={open}
			onClose={onClose}
			fullWidth
			sx={{
				'.MuiPaper-root': {
					padding: 2,
				},
			}}
			maxWidth='sm'>
			<IconButton sx={{ position: 'absolute', right: 10 }} onClick={onClose}>
				<Cancel />
			</IconButton>
			<DialogTitle textAlign='center'>Assign Operator</DialogTitle>
			<DialogContent>
				<Stack marginTop={3} rowGap={3}>
					<FormControl fullWidth>
						<InputLabel>Select the Operator</InputLabel>
						<Select
							id='selectOperator'
							label='Select the Operator'
							multiple
							value={selectedOperators}
							input={
								<OutlinedInput
									label='Select Farmers'
									sx={{
										borderRadius: '8px',
									}}
								/>
							}
							MenuProps={{
								anchorOrigin: {
									vertical: 'bottom',
									horizontal: 'center',
								},
								PaperProps: {
									style: {
										maxHeight: theme.spacing(50),
									},
								},
							}}
							onChange={(e) =>
								setSelectedOperators(e.target.value as string[])
							}>
							{fetchUnassignedOperators?.data?.data?.operators?.map(
								(operator) => (
									<MenuItem key={operator?.id} value={operator?.id}>
										{operator?.name}
									</MenuItem>
								)
							)}
						</Select>
					</FormControl>
				</Stack>
			</DialogContent>
			<DialogActions sx={{ display: 'flex', justifyContent: 'center' }}>
				<Button
					onClick={() => assignOperatorMutation.mutate()}
					disabled={assignOperatorMutation.isPending}
					variant='contained'>
					Save
				</Button>
			</DialogActions>
		</Dialog>
	)
}
