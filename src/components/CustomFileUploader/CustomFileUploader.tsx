import { IImage, IMedia } from '@/interfaces'
import { handleImageUpload } from '@/utils/helper'
import {
	CancelOutlined,
	CloudUploadOutlined,
	DescriptionOutlined,
} from '@mui/icons-material'
import {
	Box,
	CircularProgress,
	IconButton,
	Stack,
	SxProps,
	Theme,
	Typography,
} from '@mui/material'
import { useCallback, useEffect, useState } from 'react'
import { FileUploader } from 'react-drag-drop-files'
import { toast } from 'react-toastify'
import { ImageCarouselDialog } from '../ImageCarousel'
import { AllFileTypes } from '@/utils/constant'

interface IProps {
	heading?: string
	sx?: SxProps<Theme>
	setUploadData: (data: IMedia) => void
	imageHeight?: number | `${number}${'px' | 'cm' | 'mm' | 'em'}`
	required?: boolean
	imageUrl?: string
	acceptFileTypes?: string[]
	mediaType?: string
	type?: string
	directionColumnReverse?: boolean
	publicRoute?: boolean
}

export const CustomFileUploader = ({
	heading,
	sx,
	setUploadData,
	imageHeight,
	required,
	imageUrl,
	acceptFileTypes = AllFileTypes,
	mediaType,
	type,
	directionColumnReverse = false,
	publicRoute = false,
}: IProps) => {
	const [url, setUrl] = useState<string>(imageUrl ?? '')
	const [file, setFile] = useState<{
		id: string
		fileName: string
		url: string
		type: string
	}>({ id: '', fileName: '', url: '', type: '' })
	const [uploading, setUploading] = useState<boolean>(false)

	useEffect(() => {
		setUrl(imageUrl ?? '')
	}, [imageUrl])

	const handleChange = useCallback(
		async (file: any) => {
			setUploading(true)
			try {
				const data = await handleImageUpload(file, type, publicRoute)
				setUploadData({
					id: data.id,
					url: data.url,
					fileName: data.fileName,
					path: '',
				})
				setUrl(data.url)
				setFile({
					id: data.id,
					url: data.url,
					fileName: data.fileName,
					type: file?.type?.split('/')?.[0],
				})
			} catch (err) {
				toast('Image upload failed')
			}
			setUploading(false)
		},
		[setUploadData, type]
	)

	const [imageList, setImageList] = useState<IImage[]>([])

	return (
		<>
			{uploading ? (
				<Box component={CircularProgress} alignSelf='center' mb={2}/>
			) : null}
			{!uploading && !url ? (
				<Stack sx={{ ...sx }}>
					<FileUploader
						handleChange={handleChange}
						name='file'
						types={acceptFileTypes}
						required={required}>
						<IconButton
							sx={{
								border: '1px dashed #BBB',
								width: '100%',
								height: 300,
								borderRadius: 1,
								...sx,
							}}>
							<Stack
								direction={directionColumnReverse ? 'column-reverse' : 'row'}
								columnGap={2}
								alignItems='center'
								flexWrap='wrap'
								justifyContent='center'>
								<Typography variant={publicRoute ? 'caption' : 'body1'} flexWrap='wrap'>
									{heading ?? 'Upload or Drag the image'}
								</Typography>
								<CloudUploadOutlined />
							</Stack>
						</IconButton>
					</FileUploader>
				</Stack>
			) : (
				<Box position='relative' mt={4}>
					{url && (file.type === 'image' || mediaType === 'image') && (
						<IconButton
							sx={{
								position: 'absolute',
								right: -10,
								top: -10,
								color: 'common.black',
								borderRadius: '50%',
								padding: 0.5,
							}}
							onClick={() => {
								setUrl('')
								setUploadData({
									id: '',
									url: '',
									fileName: '',
									path: ' ',
								})
							}}>
							<CancelOutlined fontSize='medium' />
						</IconButton>
					)}
					{!uploading && (
						<>
							{file?.type === 'image' || mediaType === 'image' ? (
								<Box
									component='img'
									src={url}
									onClick={() => {
										setImageList([
											{
												id: file.id,
												url: url || file.url,
												fileName: '',
												path: '',
											},
										])
									}}
									sx={{
										width: '100%',
										height: imageHeight ?? 300,
										objectFit: publicRoute ? 'cover' : 'contain',
										...(publicRoute ? sx : {})
									}}
								/>
							) : (
								<Box
									width={imageHeight ?? 100}
									height={imageHeight ?? 100}
									borderRadius={1}
									display='flex'
									flexDirection='column'
									justifyContent='center'
									rowGap={1}
									position='relative'
									alignItems='center'>
									<IconButton
										sx={{
											position: 'absolute',
											right: -10,
											top: -10,
											color: 'common.black',
											borderRadius: '50%',
											padding: 0.5,
										}}
										onClick={() => {
											setUrl('')
											setUploadData({
												id: '',
												url: '',
												fileName: '',
												path: '',
											})
										}}>
										<CancelOutlined fontSize='medium' />
									</IconButton>
									<DescriptionOutlined sx={{ fontSize: 50, color: 'grey.500' }} />
									<Typography
										sx={{ wordBreak: 'break-all' }}
										textAlign='center'
										px={1}
										fontSize='12px'>
										{file?.fileName}
									</Typography>
								</Box>
							)}
						</>
					)}
				</Box>
			)}
			{imageList?.length ? (
				<ImageCarouselDialog
					open={!!imageList?.length}
					close={() => {
						setImageList([])
					}}
					ImagesList={imageList ?? []}
					showDownload={false}
				/>
			) : null}
		</>
	)
}
