import { IMedia, IMediaWithDeleteParams } from '@/interfaces'
import { proxyImage } from '@/utils/helper'
import {
	Check,
	Close,
	Info,
	KeyboardArrowLeft,
	KeyboardArrowRight,
} from '@mui/icons-material'
import {
	<PERSON>,
	Button,
	Chip,
	Dialog,
	DialogContent,
	MobileStepper,
	Stack,
	styled,
	Tooltip,
	useTheme,
} from '@mui/material'
import {
	FC,
	SyntheticEvent,
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from 'react'
import { Carousel } from 'react-responsive-carousel'
import 'react-responsive-carousel/lib/styles/carousel.min.css'
import { toast } from 'react-toastify'
import copy from 'copy-to-clipboard'
import { MediaActionStatus, userRoles } from '@/utils/constant'
import { useAuthContext } from '@/contexts'
import { AddDeleteReasonModal } from '../AddDeleteReason'
import { Confirmation } from '../Confirmation'

const statusObject = {
	[MediaActionStatus.deleted]: {
		title: 'Are you sure you want to delete this media ?',
		status: MediaActionStatus.deleted,
	},
	[MediaActionStatus.accepted]: {
		title: 'Are you sure you want to accept the request to delete this media ?',
		status: MediaActionStatus.deleted,
	},
	[MediaActionStatus.rejected]: {
		title: 'Are you sure you want to reject the request to delete this media ?',
		status: MediaActionStatus.rejected,
	},
}
interface IProp {
	open: boolean
	onClose: () => void
	activeMedia?: string
	deleteMedia?: boolean
	typeMedia?: string
	handleDelete?: ({
		activeId,
		status,
		reason,
		type,
	}: {
		activeId: string
		status: MediaActionStatus
		reason: string
		type?: string
	}) => void
	gallery: IMediaWithDeleteParams[]
}

export const MediaCarousal = ({
	open,
	deleteMedia = false,
	onClose,
	handleDelete,
	activeMedia,
	gallery,
	typeMedia,
}: IProp) => {
	const theme = useTheme()
	const activeIndex = activeMedia
		? gallery?.findIndex((i) => i.id === activeMedia)
		: 0

	const [activeItemIndex, setActiveItemIndex] = useState(0)
	const [isReasoningDialogOpen, setIsReasoningDialogOpen] =
		useState<boolean>(false)
	const [isConfirmationDialogOpen, setIsConfirmationDialogOpen] =
		useState<boolean>(false)
	const [
		actionStatusForConfirmationDialog,
		setActionStatusForConfirmationDialog,
	] = useState<MediaActionStatus>(MediaActionStatus.deleted)
	const maxSteps = gallery?.length

	const { userDetails } = useAuthContext()
	const handleNext = () => {
		setActiveItemIndex((prevActiveItem) => prevActiveItem + 1)
	}

	const handleBack = () => {
		setActiveItemIndex((prevActiveItem) => prevActiveItem - 1)
	}
	const copyToClipboard = useCallback((e: SyntheticEvent, url: string) => {
		e.preventDefault()
		copy(url)
		toast('Copied to Clipboard')
	}, [])

	const handleChipClick = useCallback(
		(status: MediaActionStatus) => {
			if (
				[userRoles.Admin, userRoles.CsinkManager].includes(
					userDetails?.accountType as userRoles
				)
			) {
				setIsConfirmationDialogOpen(true)
				setActionStatusForConfirmationDialog(status)
				return
			}
			setIsReasoningDialogOpen(true)
		},
		[userDetails?.accountType]
	)
	const handleRequestToDeleteMedia = useCallback(
		async (status: MediaActionStatus, reason: string) => {
			const activeId = gallery.find((_, idx) => idx === activeItemIndex)?.id
			handleDelete?.({
				activeId: activeId ?? '',
				status,
				reason,
				...(typeMedia && { type: typeMedia }),
			})
			setIsConfirmationDialogOpen(false)
		},
		[activeItemIndex, gallery, handleDelete, typeMedia]
	)

	useEffect(() => {
		if (!activeIndex) return
		if (activeIndex === -1) {
			setActiveItemIndex(0)
			return
		}
		setActiveItemIndex(activeIndex)
	}, [activeIndex])

	return (
		<Dialog open={open} onClose={onClose} fullWidth maxWidth='sm'>
			<DialogContent sx={{ p: 0 }}>
				<StyledContainer>
					<Carousel
						showArrows={false}
						showStatus={false}
						autoFocus
						dynamicHeight
						infiniteLoop
						showThumbs={false}
						showIndicators={false}
						selectedItem={activeItemIndex === -1 ? 0 : activeItemIndex}>
						{gallery.map((item, index) => (
							<Box key={`${item.id}-${index}`}>
								<Stack flexDirection='row' gap={2} className='button-container'>
									<Chip
										label='Download'
										className='copy-btn'
										onClick={() => {
											const link = document.createElement('a')
											link.href = item?.url
											document.body.appendChild(link)
											link.click()
											toast('Media Downloaded')
										}}
									/>
									{deleteMedia ? (
										<>
											<Chip
												label='Copy'
												className='copy-btn'
												onClick={(e) => copyToClipboard(e, item?.url)}
											/>
											<RequestOrDeleteProductionMediaChip
												onClick={handleChipClick}
												item={item}
												role={userDetails?.accountType as userRoles}
											/>
										</>
									) : null}
								</Stack>
								{Math.abs(activeItemIndex - index) <= 2 ? (
									<RenderMediaComponent
										item={item}
										activeId={
											gallery?.find((_i, idx) => activeItemIndex === idx)?.id ??
											''
										}
									/>
								) : null}
							</Box>
						))}
					</Carousel>

					{gallery?.length > 1 ? (
						<MobileStepper
							steps={maxSteps}
							position='static'
							activeStep={activeItemIndex}
							nextButton={
								<Button
									size='small'
									onClick={handleNext}
									disabled={activeItemIndex === maxSteps - 1}>
									Next
									{theme.direction === 'rtl' ? (
										<KeyboardArrowLeft />
									) : (
										<KeyboardArrowRight />
									)}
								</Button>
							}
							backButton={
								<Button
									size='small'
									onClick={handleBack}
									disabled={activeItemIndex === 0}>
									{theme.direction === 'rtl' ? (
										<KeyboardArrowRight />
									) : (
										<KeyboardArrowLeft />
									)}
									Back
								</Button>
							}
						/>
					) : null}

					{isReasoningDialogOpen ? (
						<AddDeleteReasonModal
							open={isReasoningDialogOpen}
							onClose={() => setIsReasoningDialogOpen(false)}
							title='Add Reason'
							handleOnClick={(status, reason) =>
								handleRequestToDeleteMedia(status as MediaActionStatus, reason)
							}
							confirmationText='Are you sure you want to request to delete this media ?'
						/>
					) : null}
					{isConfirmationDialogOpen ? (
						<Confirmation
							open={isConfirmationDialogOpen}
							handleClose={() => setIsConfirmationDialogOpen(false)}
							handleNoClick={() => setIsConfirmationDialogOpen(false)}
							handleYesClick={() => {
								handleRequestToDeleteMedia(
									statusObject[
										actionStatusForConfirmationDialog as MediaActionStatus
									].status,
									''
								)
							}}
							confirmationText={
								statusObject[
									actionStatusForConfirmationDialog as MediaActionStatus
								].title
							}
						/>
					) : null}
				</StyledContainer>
			</DialogContent>
		</Dialog>
	)
}

const RenderMediaComponent = ({
	item,
	activeId,
}: {
	item: IMedia
	activeId: string
}) => {
	const videoRef = useRef<HTMLVideoElement | null>(null)

	useEffect(() => {
		if (activeId === item.id && item.type !== 'video') return
		if (!videoRef?.current) return
		videoRef?.current?.pause() // To pause the current video if swicthed to another media
	}, [activeId, item.id, item.type])

	switch (item.type) {
		case 'video':
			return (
				<Box
					component='video'
					ref={videoRef}
					src={item.url}
					controls
					width='100%'
					height={450}
					poster={item?.thumbnailURL ?? ''}>
					<source src={item.url} />
				</Box>
			)
		case 'image':
		default:
			return (
				<Box
					component='img'
					sx={{
						height: 450,
						display: 'block',
						overflow: 'hidden',
						objectFit: 'contain',
						width: '100%',
					}}
					src={proxyImage(item.path as string, '800:')}
					alt='timeline media'
				/>
			)
	}
}

const RequestOrDeleteProductionMediaChip: FC<{
	onClick: (status: MediaActionStatus) => void
	item: IMediaWithDeleteParams
	role: userRoles
}> = ({ onClick, item, role }) => {
	const chipText = useMemo(() => {
		switch (item.deletionStatus) {
			case 'requested':
				return 'Delete Request Pending'
			case 'rejected':
			default: {
				if ([userRoles.Admin, userRoles.CsinkManager].includes(role)) {
					return 'Delete'
				}
				return 'Request to Delete'
			}
		}
	}, [item.deletionStatus, role])

	const renderChips = useMemo(() => {
		switch (true) {
			case [
				userRoles.BiomassAggregator,
				userRoles.ArtisanPro,
				userRoles.cSinkNetwork,
			].includes(role): {
				return (
					<Chip
						label={chipText}
						className='delete-chip'
						{...(item.deletionStatus === 'requested'
							? {}
							: { onClick: () => onClick(MediaActionStatus.deleted) })}
					/>
				)
			}

			case [userRoles.Admin, userRoles.CsinkManager].includes(role): {
				if (item.deletionStatus === 'requested') {
					return (
						<Stack
							direction='row'
							columnGap={1}
							className='delete-chip'
							alignItems='center'>
							<Chip
								className='accept-btn'
								label='Accept'
								onClick={() => onClick(MediaActionStatus.accepted)}
								icon={<Check fontSize='small' color='inherit' />}
							/>
							<Chip
								label='Reject'
								color='error'
								onClick={() => onClick(MediaActionStatus.rejected)}
								icon={<Close fontSize='small' />}
							/>
							<Tooltip title={item.deletionReason} arrow>
								<Info color='primary' />
							</Tooltip>
						</Stack>
					)
				}
				return (
					<Chip
						label={chipText}
						className='delete-chip'
						onClick={() => onClick(MediaActionStatus.deleted)}
					/>
				)
			}

			default:
				return null
		}
	}, [chipText, item.deletionReason, item.deletionStatus, onClick, role])

	return renderChips
}

const StyledContainer = styled(Box)(({ theme }) => ({
	'.button-container': {
		position: 'absolute',
		right: 5,
		top: 5,
	},
	'.delete-chip': {
		bgcolor: theme.palette.grey[300],
		color: theme.palette.error.main,
		fontWeight: 600,
		zIndex: 10,
	},
	'.accept-btn': {
		color: theme.palette.common.white,
		backgroundColor: theme.palette.success.main,

		':hover': {
			color: theme.palette.common.black,
			backgroundColor: theme.palette.success.light,
		},
	},
	'.copy-btn': {
		color: theme.palette.primary.main,
		fontWeight: 600,
		zIndex: 10,
		bgcolor: theme.palette.grey[300],
	},
	'.MuiTooltip-arrow': {
		boxShadow: '0px 4px 4px rgba(0, 0, 0, 0.25)',
		color: theme.palette.common.white,
		fontSize: '15px',
		padding: theme.spacing(1),
		wordWrap: 'break-word',
		bgcolor: theme.palette.common.black,
		textTransform: 'capitalize',
	},
}))
