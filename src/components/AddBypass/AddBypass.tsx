import CloseIcon from '@/assets/icons/arrow-bar-right.svg'
import {
	Autocomplete,
	Box,
	Button,
	FormControl,
	FormHelperText,
	IconButton,
	MenuItem,
	Stack,
	styled,
	TextField,
	Typography,
} from '@mui/material'
import { useAddBypass } from './useAddBypass'
import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { AddBypassSchema, IAddBypassSchema } from './schema'
import { useCallback, useEffect, useState } from 'react'
import { ImgInfo } from '@/interfaces/projects.type'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'react-toastify'
import { authAxios, useAuthContext } from '@/contexts'
import { PhoneInputComponent } from '../PhoneInput'
import { userRoles } from '@/utils/constant'
import { theme } from '@/lib/theme/theme'

export interface INetworkManager {
	certificateImage?: ImgInfo
	managerId: string
	managerName: string
	managerPhone: string
	managerEmail: string
	trained: boolean
	trainingImages?: ImgInfo[]
	countryCode: string
	language: string
	certificateUrl: ImgInfo
	certificateStatus: string
	managerAddress: string
}
interface IPayload {
	number: string
	countryCode: string
	biomassAggregatorId?: string 
	csinkManagerId?: string
}

interface IProps {
	close: () => void
}

export const AddBypass = ({ close }: IProps) => {
	const { bioMassAggregatorList } = useAddBypass()
	const [isCSinkManager, setCSinkManager] = useState(true)
	const { userDetails } = useAuthContext()
	const initialValues = {
		countryCode: '',
		phoneNumber: '',
		baId: '',
	}
	const queryClient = useQueryClient()
	const {
		handleSubmit,
		formState: { errors },
		watch,
		setValue,
		clearErrors,
		setError,
	} = useForm<IAddBypassSchema>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<IAddBypassSchema>(AddBypassSchema),
	})
	useEffect(()=>{
		userDetails?.accountType===userRoles.CsinkManager && setValue('baId',userDetails?.csinkManagerId)
	},[])
	const [searchValue, setSearchValue] = useState('')

	const handleAddBypassNumber = handleSubmit((formValues) => {

		const csinkPayload = {
			csinkManagerId: userDetails?.accountType === userRoles.Admin
			? formValues.baId
			: userDetails?.csinkManagerId,
			// : userDetails?.id,
		}
		const baPayload = {
			biomassAggregatorId: formValues.baId
		}

		const payload = {
			number: formValues.phoneNumber,
			countryCode: formValues.countryCode,
			...(
				(userDetails?.accountType === userRoles.Admin && isCSinkManager) ||
			userDetails?.accountType === userRoles.CsinkManager ? csinkPayload : baPayload
			)
		}


		addBypassNumberMutate.mutate(payload)
	})

	const addBypassNumberMutate = useMutation({
		mutationKey: ['addBypassNumber'],
		mutationFn: (payload: IPayload) => authAxios.post(`/by-pass`, payload),
		onSuccess: () => {
			toast('Otp-Bypass Added')
			queryClient.refetchQueries({
				queryKey: ['getOtpBypassList'],
				// exact: true,
				// refetchType: 'active',
			})
			close()
		},
		onError: (error: Error) => {
			toast(error?.message)
		},
	})

	const handleOnChange = useCallback(
		(value: string, dialCode: string) => {
			setValue('countryCode', `+${dialCode}`)
			setValue('phoneNumber', `${value}`)
			if (value.length === 0) {
				setError('phoneNumber', { message: 'Please enter phone number' })
				return
			}
			clearErrors('phoneNumber')
		},
		[clearErrors, setError, setValue]
	)
	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack direction='row' spacing={theme.spacing(1)} alignItems='center'>
					<IconButton onClick={close}>
						<Box
							component='img'
							src={CloseIcon}
							alt='closeIcon'
							height={20}
							width={20}
						/>
					</IconButton>
					<Typography variant='h4'>Add Bypass Number</Typography>
				</Stack>
			</Stack>
			<Stack className='container' component='form' gap={theme.spacing(3)}>
				<FormControl>
					{userDetails?.accountType===userRoles.Admin && <Autocomplete
						fullWidth
						id='BA'
						onChange={(_event, newValue: any) => {
							setCSinkManager(newValue?.isCsinkManager)
							setValue('baId', newValue?.id)
							clearErrors('baId')
						}}
						autoHighlight
						inputValue={searchValue}
						onInputChange={(_event, newInputValue) => {
							setSearchValue(newInputValue)
						}}
						noOptionsText='No data found'
						options={bioMassAggregatorList || []}
						getOptionLabel={(option) => {
							if (typeof option !== 'string')
								return `${option?.name} (${option?.shortName}) `
							return option
						}}
						renderInput={(params) => (
							<TextField {...params} label='BA/ Csink' />
						)}
						renderOption={(
							props,
							{
								name,
								shortName,
							}: {
								id: string
								name: string
								email: string
								managerDetails?: INetworkManager[]
								shortName?: string
							}
						) => (
							<Stack
								component={MenuItem}
								value={name}
								{...props}
								sx={{
									'&.MuiAutocomplete-option': {
										alignItems: 'flex-start',
									},
								}}>
								<Typography textTransform='capitalize'>
									{name} ({shortName})
								</Typography>
							</Stack>
						)}
					/>}
					<FormHelperText error={Boolean(errors?.baId)}>
						{errors?.baId && (
							<Typography color='error' variant='caption'>
								{errors?.baId?.message}
							</Typography>
						)}
					</FormHelperText>
				</FormControl>
				<PhoneInputComponent
					value={watch('phoneNumber')}
					handleOnChange={handleOnChange}
					isValid={!!errors.phoneNumber?.message}
					getSelectedCountryDialCode={(dialCode) =>
						setValue('countryCode', dialCode)
					}
				/>
				<FormHelperText error={Boolean(errors?.phoneNumber)}>
					{errors?.phoneNumber && (
						<Typography color='error' variant='caption'>
							{errors?.phoneNumber?.message}
						</Typography>
					)}
				</FormHelperText>
				<Button onClick={handleAddBypassNumber} variant='contained'>
					Save
				</Button>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		padding: theme.spacing(2, 4),
		maxHeight: 'calc( 100vh - 200px)',
		overflowY: 'scroll',
	},
}))
