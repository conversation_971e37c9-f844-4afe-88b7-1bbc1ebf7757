import { GoogleMapsWithNonDraggableMarker } from '@/components/GoogleMap'
import { yupResolver } from '@hookform/resolvers/yup'
import {
	Button,
	FormControl,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	Typography,
	useTheme,
} from '@mui/material'
import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { addCSinkManagerSchema, TAddCSinkManager } from '../schema'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios, useAuthContext } from '@/contexts'
import { toast } from 'react-toastify'
import { LoadingButton } from '@mui/lab'
import { CsinkManager } from '@/interfaces'
import { ICrop } from '@/types'
import { userRoles } from '@/utils/constant'
import { TwoColumnLayout } from '@/components/TwoColumnLayout'
import { Add, Remove } from '@mui/icons-material'
import { AddManagerDetailsFields } from '@/components/AddManagerDetails'
import { CustomTextField } from '@/utils/components'

type TProps = {
	handleCloseDrawer: () => void
	editMode?: boolean
	cSinkId?: string
	type: string
}

const initialValues = {
	name: '',
	shortCode: '',
	locationName: '',
	managerDetails: null,
	createEntityOnly: true,
	latitude: 0,
	longitude: 0,
	cropIds: [],
}

export const AddCSinkManager: FC<TProps> = ({
	handleCloseDrawer,
	editMode = false,
	cSinkId,
}) => {
	const theme = useTheme()
	const queryClient = useQueryClient()
	const { userDetails } = useAuthContext()

	const form = useForm<TAddCSinkManager>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TAddCSinkManager>(addCSinkManagerSchema),
	})

	const {
		register,
		formState: { errors },
		watch,
		setValue,
		handleSubmit,
	} = form

	const [showAssignBiomassButton, setShowAssignBiomassButton] = useState(false)

	const handleAddManagerDetails = useCallback(() => {
		setValue('createEntityOnly', false)
		setValue('managerDetails', {
			name: '',
			email: '',
			phoneNo: null,
			countryCode: null,
			trained: 'no',
			profileImageId: null,
			trainingImages: [],
		})
	}, [setValue])

	const handleRemoveManagerDetails = useCallback(() => {
		setValue('createEntityOnly', true)
		setValue('managerDetails', null)
	}, [setValue])

	const setMapCenter = useCallback(
		(lat: number, lng: number) => {
			setValue('latitude', Number(lat.toFixed(6)))
			setValue('longitude', Number(lng.toFixed(6)))
		},
		[setValue]
	)

	const getCurrentLocation = useCallback(() => {
		navigator.geolocation.getCurrentPosition((position) => {
			setMapCenter(position.coords.latitude, position.coords.longitude)
		})
	}, [setMapCenter])

	const addCSinkManager = useMutation({
		mutationKey: ['addCSinkManager'],
		mutationFn: async ({
			name,
			latitude,
			longitude,
			locationName,
			shortCode,
			cropIds,
			createEntityOnly,
			managerDetails,
		}: TAddCSinkManager) => {
			const { phoneNo, ...rest } = managerDetails ?? {}
			const payload = {
				name,
				latitude,
				longitude,
				cropIds,
				createEntityOnly,
				manager: createEntityOnly
					? null
					: {
							...rest,
							phoneNumber: phoneNo,
							trained: managerDetails?.trained === 'yes',
							profileImageId: managerDetails?.profileImageId?.id,
							trainingImages:
								managerDetails?.trainingImages?.map(({ id }) => id) ?? [],
					  },
				locationName,
				shortCode: shortCode ? shortCode : null,
			}
			return await authAxios.post('/csink-manager', payload)
		},
		onError: (error: any) => {
			toast(error?.response?.data?.messageToUser)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allCsinkManager'] })
			queryClient.refetchQueries({ queryKey: ['entityTabsCountQuery'] })
			handleCloseDrawer()
		},
	})

	const handleAddCSinkManager = useCallback(
		(values: TAddCSinkManager) => {
			addCSinkManager.mutate(values)
		},
		[addCSinkManager]
	)
	const fetchBiomass = useQuery({
		queryKey: ['getBiomassTypes'],
		queryFn: () => {
			return authAxios<{ cropDetails: ICrop[] }>(`/drop-down/crops`)
		},
		enabled: userDetails?.accountType === userRoles.Admin,
	})

	useQuery({
		queryKey: ['cSinkDetail', cSinkId],
		queryFn: async () => {
			try {
				const { data } = await authAxios.get<CsinkManager>(
					`/csink-manager/${cSinkId}`
				)
				const [latitude, longitude] = data.location.slice(1, -1).split(',')
				setValue('name', data?.name)
				setValue('locationName', data?.locationName)
				setValue('shortCode', data?.shortName)
				setValue('latitude', Number(latitude || 0))
				setValue('longitude', Number(longitude || 0))

				return data
			} catch (error: any) {
				toast(error?.response?.data?.messageToUser)
				return null
			}
		},
		enabled: editMode,
	})

	const editCSink = useMutation({
		mutationKey: ['editCSink'],
		mutationFn: async (payload: TAddCSinkManager) => {
			const updatedPayload = {
				...payload,
				manager: payload?.createEntityOnly
					? null
					: {
							...payload?.managerDetails,
							trained: payload?.managerDetails?.trained === 'yes',
							profileImageId: payload?.managerDetails?.profileImageId?.id,
							trainingImages:
								payload?.managerDetails?.trainingImages?.map(({ id }) => id) ??
								[],
					  },
				latitude: String(payload.latitude),
				longitude: String(payload.longitude),
			}
			return await authAxios.put(`/csink-manager/${cSinkId}`, updatedPayload)
		},
		onError: (err: any) => {
			toast(err?.response?.data?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['allCsinkManager'] })
			handleCloseDrawer()
		},
	})

	const handleEditCSink = useCallback(
		(values: TAddCSinkManager) => {
			editCSink.mutate(values)
		},
		[editCSink]
	)

	const handleSave = useCallback(
		(values: TAddCSinkManager) =>
			editMode ? handleEditCSink(values) : handleAddCSinkManager(values),
		[editMode, handleAddCSinkManager, handleEditCSink]
	)

	const isLoading = useMemo(
		() => editCSink?.isPending || addCSinkManager.isPending,
		[addCSinkManager.isPending, editCSink?.isPending]
	)

	useEffect(() => {
		if (editMode) return
		getCurrentLocation()
	}, [editMode, getCurrentLocation])

	return (
		<FormProvider {...form}>
			<Stack gap={5}>
				<CustomTextField
					schema={addCSinkManagerSchema}
					fullWidth
					id='name'
					type='text'
					label='Enter Name '
					variant='outlined'
					error={!!errors.name?.message}
					helperText={errors?.name?.message}
					{...register('name')}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
				/>

				<CustomTextField
					schema={addCSinkManagerSchema}
					id='shortCode'
					label='Enter short code'
					type='text'
					variant='outlined'
					placeholder='Please Enter Your short code'
					fullWidth
					inputProps={{
						maxLength: 4,
					}}
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
					value={watch('shortCode')}
					error={!!errors.shortCode?.message}
					helperText={errors?.shortCode?.message}
					{...register('shortCode', {
						setValueAs: (value) => {
							if (/^[a-zA-Z0-9]*$/.test(value)) return value
							return watch('shortCode')
						},
					})}
				/>
				<CustomTextField
					schema={addCSinkManagerSchema}
					id='locationName'
					label='Enter the Full Address'
					variant='outlined'
					autoComplete='off'
					fullWidth
					InputLabelProps={
						editMode
							? {
									shrink: editMode,
							  }
							: {}
					}
					error={!!errors.locationName?.message}
					helperText={errors?.locationName?.message}
					{...register('locationName')}
				/>
				<Stack direction='row' columnGap={2}>
					<CustomTextField
						schema={addCSinkManagerSchema}
						id='latitude'
						label='Latitude'
						variant='outlined'
						fullWidth
						type='number'
						inputProps={{
							step: 'any',
						}}
						error={!!errors.latitude?.message}
						helperText={errors?.latitude?.message}
						sx={{
							'& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button':
								{
									display: 'none',
								},
							'& input[type=number]': {
								MozAppearance: 'textfield',
							},
						}}
						{...register('latitude', {
							setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
						})}
					/>
					<CustomTextField
						schema={addCSinkManagerSchema}
						id='longitude'
						label='Longitude'
						variant='outlined'
						fullWidth
						type='number'
						inputProps={{
							step: 'any',
						}}
						sx={{
							'& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button':
								{
									display: 'none',
								},
							'& input[type=number]': {
								MozAppearance: 'textfield',
							},
						}}
						error={!!errors.longitude?.message}
						helperText={errors?.longitude?.message}
						{...register('longitude', {
							setValueAs: (value) => Number(parseFloat(value).toFixed(6)),
						})}
					/>
				</Stack>

				{showAssignBiomassButton && !editMode ? (
					<FormControl>
						<InputLabel id='selectBiomass-label'>Select the Biomass</InputLabel>
						<Select
							sx={{ height: theme.spacing(7) }}
							labelId='selectBiomass-label'
							id='selectBiomass'
							label='Select the Biomass'
							multiple
							value={watch('cropIds')}
							{...register('cropIds')}
							MenuProps={{
								anchorOrigin: {
									vertical: 'bottom',
									horizontal: 'center',
								},
								PaperProps: {
									style: {
										maxHeight: theme.spacing(50),
									},
								},
							}}>
							{fetchBiomass?.data?.data?.cropDetails?.map((crop) => (
								<MenuItem key={crop?.id} value={crop?.id}>
									{crop?.name}
								</MenuItem>
							))}
						</Select>
					</FormControl>
				) : null}
				{!showAssignBiomassButton && !editMode ? (
					<TwoColumnLayout
						gridBreakpoints={[6]}
						right={
							<Button
								variant='text'
								onClick={() => setShowAssignBiomassButton(true)}>
								Assign Biomass
							</Button>
						}
						left={<></>}
					/>
				) : null}
				<GoogleMapsWithNonDraggableMarker
					center={{
						lat: Number(watch('latitude')),
						lng: Number(watch('longitude')),
					}}
					setMapCenter={setMapCenter}
					mapContainerStyle={{
						width: '100%',
						height: 380,
						position: 'relative',
					}}
				/>
				{!editMode ? (
					<Stack flexDirection='column'>
						{!watch('createEntityOnly') ? (
							<Stack gap={2}>
								<Button
									variant='text'
									sx={{
										justifyContent: 'flex-start',
									}}
									onClick={handleRemoveManagerDetails}
									startIcon={<Remove color='primary' />}>
									Remove Manager Details
								</Button>
								<Typography variant='body2' pb={theme.spacing(1)}>
									Manager Details:
								</Typography>
								<AddManagerDetailsFields
									editMode={editMode}
									schema={addCSinkManagerSchema}
								/>
							</Stack>
						) : (
							<Button
								variant='text'
								sx={{
									justifyContent: 'flex-start',
								}}
								onClick={handleAddManagerDetails}
								startIcon={<Add color='primary' />}>
								Add Manager Details
							</Button>
						)}
					</Stack>
				) : null}
				<Stack
					direction='row'
					gap={3}
					justifyContent='space-between'
					className='buttonContainer'>
					<Button
						onClick={handleCloseDrawer}
						sx={{ border: ` 1px solid ${theme.palette.primary.main}` }}>
						Cancel
					</Button>
					<LoadingButton
						loading={isLoading}
						disabled={isLoading}
						onClick={handleSubmit(handleSave)}
						variant='contained'>
						{editMode ? 'Save' : 'Add'}
					</LoadingButton>
				</Stack>
			</Stack>
		</FormProvider>
	)
}
