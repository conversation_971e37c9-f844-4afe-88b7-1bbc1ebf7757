import * as Yup from 'yup'

export const addBiomass = Yup.object({
	name: Yup.string().required('Please enter crop/biomass name'),
	density: Yup.number().nullable(),
	biomassImageId: Yup.string().required('Please upload crop image'),
	biomassType: Yup.string().required('Please enter biomass type'),
	description: Yup.string(),
	season: Yup.string().when(['biomassType'], {
		is: (biomassType: string) => biomassType === 'crop',
		then: (schema) => schema.required('Please enter crop type'),
		otherwise: (schema) => schema,
	}),
})
