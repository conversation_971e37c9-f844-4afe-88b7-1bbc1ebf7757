import { defaultLimit, defaultPage } from '@/utils/constant'
import { Pagination, styled } from '@mui/material'
import { useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'

interface IProps {
	rowCount?: number
	pageName?: string
	limitName?: string
	customDefaultLimit?:number
}

export const CustomPagination = ({ rowCount, pageName, limitName, customDefaultLimit}: IProps) => {
	const navigate = useNavigate()
	const [searchParams] = useSearchParams()
	const paramsLimit = searchParams.get(limitName || 'limit') ?? customDefaultLimit ?? defaultLimit
	const paramsPage = searchParams.get(pageName || 'page') ?? defaultPage

	const handleChangePage = useCallback(
		(_: unknown, newPage: number) => {
			const nsp = new URLSearchParams(window.location.search)
			nsp.set(pageName || 'page', String(newPage - 1))
			navigate(`?${nsp.toString()}`, { replace: true })
		},
		[navigate, pageName]
	)
	if (rowCount === 0) return null

	return (
		// <Table sx={{ width: 'auto' }}>
		// 	<TableBody>
		// 		<TableRow>
		<StyledPagination
			count={Math.ceil(Number(rowCount) / Number(paramsLimit)) ?? 0}
			onChange={handleChangePage}
			page={Number(paramsPage) + 1}
			defaultPage={1}
			shape='rounded'
			color='primary'
			variant='outlined'
		/>
		// 		</TableRow>
		// 	</TableBody>
		// </Table>
	)
}

const StyledPagination = styled(Pagination)(({ theme }) => ({
	'.MuiPaginationItem-outlined': {
		borderWidth: 0,
		...theme.typography.body1,
	},
}))
