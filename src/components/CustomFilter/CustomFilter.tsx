import OutlinedInput from '@mui/material/OutlinedInput'
import InputLabel from '@mui/material/InputLabel'
import MenuItem from '@mui/material/MenuItem'
import FormControl from '@mui/material/FormControl'
import ListItemText from '@mui/material/ListItemText'
import Select, { SelectChangeEvent } from '@mui/material/Select'
import Checkbox from '@mui/material/Checkbox'
import { useEffect, useRef } from 'react'
import { useSearchParams } from 'react-router-dom'
import { ILabelWithValue } from '@/types'

type CustomFilterProps = {
	options?: ILabelWithValue[]
	initialValue?: string[]
	onChangeSelect?: (param: {
		selectedOptions: ILabelWithValue[]
		nsp: URLSearchParams
	}) => URLSearchParams
	filtersToReset?: string[]
	queryKey?: string
	label?: string
	multiple?: boolean
}

export const CustomFilter = ({
	options = [],
	initialValue = [],
	onChangeSelect,
	filtersToReset = [],
	label = 'Biomass',
	queryKey = 'cropIds',
	multiple = true,
}: CustomFilterProps) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const initialised = useRef(false)

	useEffect(() => {
		if (
			initialValue.length > 0 &&
			!searchParams.getAll(queryKey ?? '').length
		) {
			const nsp = new URLSearchParams(searchParams)
			initialValue.forEach((value) => {
				nsp.append(queryKey ?? '', value)
			})

			let param = nsp
			if (onChangeSelect) {
				const selectedOptions = options.filter((crop) =>
					initialValue.includes(crop.value)
				)
				param = onChangeSelect({ selectedOptions, nsp })
			}
			setSearchParams(param, { replace: true })
		}
		initialised.current = true
	}, [
		initialValue,
		queryKey,
		options,
		searchParams,
		setSearchParams,
		onChangeSelect,
	])

	const currentValues = searchParams.getAll(queryKey ?? '')

	const handleChange = (event: SelectChangeEvent<typeof currentValues>) => {
		const {
			target: { value },
		} = event

		const nsp = new URLSearchParams(searchParams)
		filtersToReset.forEach((key) => {
			nsp.delete(key)
		})
		nsp.delete(queryKey ?? '')
		const selectedIds = typeof value === 'string' ? value.split(',') : value

		selectedIds.forEach((id) => {
			nsp.append(queryKey ?? '', id)
		})

		let param = nsp
		if (onChangeSelect) {
			const selectedOptions = options.filter((crop) =>
				selectedIds.includes(crop.value)
			)
			param = onChangeSelect({ selectedOptions, nsp })
		}
		setSearchParams(param, { replace: true })
	}

	return (
		<FormControl sx={{ width: 220 }}>
			<InputLabel id={`${label}-select-label`}>{label}</InputLabel>
			<Select
				labelId={`{label}-select-label`}
				id={`${label}-select`}
				multiple={multiple}
				sx={{ height: 40 }}
				value={currentValues}
				onChange={handleChange}
				input={<OutlinedInput label={label} />}
				renderValue={(selected) =>
					selected
						.map((id) => options.find((crop) => crop.value === id)?.label)
						.join(', ')
				}
				MenuProps={{
					PaperProps: {
						style: {
							maxHeight: 48 * 4.5 + 9,
							width: 220,
						},
					},
				}}>
				{options.map((crop) => (
					<MenuItem key={crop.value} value={crop.value}>
						{multiple && (
							<Checkbox checked={currentValues.includes(crop.value)} />
						)}
						<ListItemText primary={crop.label} />
					</MenuItem>
				))}
			</Select>
		</FormControl>
	)
}
