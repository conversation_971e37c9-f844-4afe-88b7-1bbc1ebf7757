import {
	// FormHelperText,
	MenuItem,
	Stack,
	Typography,
} from '@mui/material'
import { useFormContext, useWatch } from 'react-hook-form'
import { MultipleFileUploader } from '../MultipleFileUploader'
import { CompensationType, MethaneType } from '@/utils/constant'
import { theme } from '@/lib/theme/theme'
import { BiomassOptionType } from '@/types'
import { AnyObjectSchema } from 'yup'
import { CustomTextField } from '@/utils/components'

interface IProps {
	editMode?: boolean
	fieldIndex: number
	schema?: AnyObjectSchema
	fetchBiomassTypeList?: BiomassOptionType[]
}

export const AddMethaneCompensateFields = ({
	editMode,
	schema,
	fieldIndex,
	fetchBiomassTypeList,
}: IProps) => {
	const { register, clearErrors, watch, setValue } = useFormContext()

	const methaneType = useWatch({
		name: `methaneCompensateStrategies.${fieldIndex}.methaneCompensateType`,
	})

	return (
		<Stack gap={theme.spacing(3)}>
			{/* Methane Compensate Type */}
			<CustomTextField
				schema={schema}
				select
				id='selectType'
				value={watch(
					`methaneCompensateStrategies.${fieldIndex}.methaneCompensateType`
				)}
				label='Select Type'
				placeholder='Compensate'
				onChange={(e) => {
					const value = e.target.value
					setValue(
						`methaneCompensateStrategies.${fieldIndex}.methaneCompensateType`,
						value
					)
					if (value !== MethaneType.Compensation) {
						setValue(
							`methaneCompensateStrategies.${fieldIndex}.compensateType`,
							null
						)
					}
				}}
				InputLabelProps={editMode ? { shrink: editMode } : {}}
				SelectProps={{
					MenuProps: {
						anchorOrigin: {
							vertical: 'bottom',
							horizontal: 'center',
						},
					},
				}}>
				<MenuItem value={MethaneType.Avoidance}>Avoidance</MenuItem>
				<MenuItem value={MethaneType.Compensation}>Compensation</MenuItem>
			</CustomTextField>

			{/* Biomass Type */}
			<CustomTextField
				schema={schema}
				select
				id='selectBiomassType'
				value={watch(`methaneCompensateStrategies.${fieldIndex}.biomassId`)}
				label='Select Biomass Type'
				placeholder='Biomass Type'
				{...register(`methaneCompensateStrategies.${fieldIndex}.biomassId`)}
				InputLabelProps={editMode ? { shrink: editMode } : {}}
				SelectProps={{
					MenuProps: {
						anchorOrigin: {
							vertical: 'bottom',
							horizontal: 'center',
						},
					},
				}}>
				{fetchBiomassTypeList?.map((item, idx) => (
					<MenuItem key={idx + item?.value} value={item?.value}>
						{item?.label}
					</MenuItem>
				))}
			</CustomTextField>

			{/* Compensate Type */}
			{methaneType === MethaneType.Compensation ? (
				<CustomTextField
					schema={schema}
					select
					id='selectCompensate'
					value={watch(
						`methaneCompensateStrategies.${fieldIndex}.compensateType`
					)}
					label='Select Compensate Type'
					placeholder='Compensate'
					{...register(
						`methaneCompensateStrategies.${fieldIndex}.compensateType`
					)}
					InputLabelProps={editMode ? { shrink: editMode } : {}}
					SelectProps={{
						MenuProps: {
							anchorOrigin: {
								vertical: 'bottom',
								horizontal: 'center',
							},
						},
					}}>
					<MenuItem value={CompensationType.SPC}>SPC</MenuItem>
					<MenuItem value={CompensationType.TreePlanting}>
						Tree Planting
					</MenuItem>
					<MenuItem value={CompensationType.Others}>Others</MenuItem>
				</CustomTextField>
			) : null}

			{/* Description */}
			<CustomTextField
				schema={schema}
				id='methaneCompensationStrategy'
				multiline
				rows={4}
				label='Methane Compensation Strategy'
				variant='outlined'
				fullWidth
				autoFocus={false}
				autoComplete='off'
				{...register(`methaneCompensateStrategies.${fieldIndex}.description`)}
				InputLabelProps={editMode ? { shrink: editMode } : {}}
			/>

			{/* Media Upload */}
			<Stack rowGap={2} width='100%'>
				<Typography variant='subtitle1'>Upload Strategy Media</Typography>
				<MultipleFileUploader
					sx={{
						height: { xs: 100, md: 150 },
						width: '100%',
					}}
					imageHeight={100}
					{...register(`methaneCompensateStrategies.${fieldIndex}.documentIds`)}
					data={watch(`methaneCompensateStrategies.${fieldIndex}.documentIds`)}
					training={false}
					heading='Add Strategy Media'
					setUploadData={(data) => {
						setValue(
							`methaneCompensateStrategies.${fieldIndex}.documentIds`,
							data
						)
						clearErrors(`methaneCompensateStrategies.${fieldIndex}.documentIds`)
					}}
				/>
			</Stack>
		</Stack>
	)
}
