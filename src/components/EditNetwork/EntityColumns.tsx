import {
	IArtisanProDetails,
	IContainerDetails,
	IFpu,
	IImage,
	IKiln,
	ILocation,
	IMedia,
	INetwork,
	IPackagingBag,
	ISite,
	IVehicle,
} from '@/interfaces'
import { theme } from '@/lib/theme/theme'
import {
	Stack,
	Typography,
	Button,
	Avatar,
	Menu,
	MenuItem,
	ListItemText,
	IconButton,
} from '@mui/material'
import { useCallback, useState } from 'react'
import { Confirmation } from '../Confirmation'
import { ActionInformationDrawer } from '../ActionInformationDrawer'
import { AddPackagingBag } from '../AddPackagingBag'
import { AddContainer } from '../AddContainer'
import { EntityTabEnum } from '@/utils/constant'
import { AddKiln } from '../AddKiln'
import { AddBiomassSource } from '../AddBiomassSource'
import { EditMethaneCompensate } from './EditMethaneCompensate'
import { AddVehicle } from '../AddVehicle'
import React from 'react'
import { MoreVert } from '@mui/icons-material'
import { SetURLSearchParams } from 'react-router-dom'
import KMLFileUploader from './KMLfileUploader'
import { ImageCarouselDialog } from '../ImageCarousel'

type DetailProps = {
	data: {
		name: string
		landmark: string
		coordinate?: string | null
		farmArea?: ILocation[]
		id: string
	}
	handleViewKMLFile: (
		farmCoordinates: ILocation[],
		center: { x: string; y: string },
		farmId: string
	) => void
	setSearchParams: SetURLSearchParams
	setShowMap: (bool: boolean) => void
}

interface IProps {
	icon?: IMedia
	entityName: string
	subText?: string | JSX.Element
	editButton?: boolean
	deleteButton?: boolean
	showAddandUploadButton?: boolean
	handleDelete?: () => void
	subheadingButtons?: JSX.Element
	entityType?: EntityTabEnum
	bagDetails?: IPackagingBag
	isCsink?: boolean
	containerDetails?: IContainerDetails
	siteDetails?: ISite
	csinkNetworkDetails?: INetwork
	artisanProDetails?: IArtisanProDetails
	kilnDetails?: IKiln
	subheading?: string
	biomassSourceDetails?: IFpu
	methaneCompensateId?: string
	vehicleDetails?: IVehicle
	detailProps?: DetailProps
	handleSaveKml?: () => void
}

export const EntityColumns = ({
	icon,
	entityName,
	subText,
	editButton = false,
	deleteButton = false,
	showAddandUploadButton = true,
	biomassSourceDetails,
	entityType,
	bagDetails,
	isCsink = true,
	containerDetails,
	subheading,
	subheadingButtons,
	artisanProDetails,
	kilnDetails,
	vehicleDetails,
	csinkNetworkDetails,
	methaneCompensateId,
	handleDelete,
	detailProps,
}: IProps) => {
	const [deleteItem, setDeleteItem] = useState(false)
	const [editItem, setEditItem] = useState(false)
	const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
	const open = Boolean(anchorEl)
	const handleOpenMenu = (event: React.MouseEvent<HTMLButtonElement>) => {
		setAnchorEl(event.currentTarget)
	}
	const handleCloseMenu = () => {
		setAnchorEl(null)
	}

	const convertStringToArray = useCallback((farmLocation: string): string[] => {
		const coordinate: string[] = farmLocation.slice(1, -1).split(',')
		return coordinate
	}, [])

	const [imageList, setImageList] = useState<IImage[]>([])
	const handleAvatarClick = (
		e: React.MouseEvent<HTMLDivElement, MouseEvent>,
		icon: IMedia | undefined
	) => {
		if (icon?.fileName || icon?.path) {
			e.stopPropagation()
			setImageList([
				{
					fileName: icon?.fileName,
					id: icon?.id || '',
					path: icon?.path || '',
					url: icon?.url || '',
				},
			])
		}
	}

	return (
		<>
			<Stack
				flexDirection='row'
				justifyContent='space-between'
				width='100%'
				padding={theme.spacing(1)}>
				<Stack flexDirection='row' gap={2}>
					{entityType !== EntityTabEnum.biomassSource && (
						<Avatar
							src={icon?.url}
							alt='image'
							sx={{
								width: theme.spacing(5),
								height: theme.spacing(5),
								bgcolor: 'grey.300',
							}}
							onClick={(e) => handleAvatarClick(e, icon)}
						/>
					)}
					<Stack sx={{ flexDirection: 'column' }} width='100%'>
						<Typography variant='body1' fontWeight='bold'>
							{entityName}
						</Typography>
						{subText ? (
							<Typography variant='subtitle1'>{subText}</Typography>
						) : null}
						{subheadingButtons ?? null}
					</Stack>
				</Stack>
				{entityType === EntityTabEnum.biomassSource ? (
					<>
						<IconButton onClick={handleOpenMenu}>
							<MoreVert />
						</IconButton>
						<Menu
							id='basic-menu'
							anchorEl={anchorEl}
							open={open}
							onClose={handleCloseMenu}
							MenuListProps={{ 'aria-labelledby': 'basic-button' }}>
							{editButton && (
								<MenuItem
									onClick={() => {
										setEditItem(true)
										handleCloseMenu()
									}}>
									<ListItemText>Edit</ListItemText>
								</MenuItem>
							)}
							{deleteButton && (
								<MenuItem
									onClick={() => {
										setDeleteItem(true)
										handleCloseMenu()
									}}>
									<ListItemText>Delete</ListItemText>
								</MenuItem>
							)}
							{showAddandUploadButton && (
								<MenuItem>
									<ListItemText
										onClick={(e) => {
											e.stopPropagation()
											const location = convertStringToArray(
												detailProps?.data?.coordinate ?? '(0,0)'
											)
											detailProps?.setSearchParams(
												(urlParams) => {
													urlParams.set(
														'networkId',
														detailProps?.data?.id ?? ''
													)
													urlParams.set('lat', location[0])
													urlParams.set('long', location[1])
													return urlParams
												},
												{ replace: true }
											)
											detailProps?.setShowMap(true)
											handleCloseMenu()
										}}>
										Add KML file
									</ListItemText>
								</MenuItem>
							)}
							{detailProps?.data?.farmArea?.length ? (
								<MenuItem
									onClick={() => {
										detailProps?.setSearchParams(
											(urlParams) => {
												urlParams.set('networkId', detailProps?.data?.id ?? '')
												urlParams.set(
													'lat',
													String(detailProps?.data?.farmArea?.[0]?.x)
												)
												urlParams.set(
													'long',
													String(detailProps?.data?.farmArea?.[0]?.y)
												)
												return urlParams
											},
											{ replace: true }
										)
										detailProps?.handleViewKMLFile(
											detailProps?.data?.farmArea ?? [],
											{
												x: String(detailProps?.data?.farmArea?.[0]?.x) || '',
												y: String(detailProps?.data?.farmArea?.[0]?.y) || '',
											},
											detailProps?.data?.id || ''
										)
										handleCloseMenu()
									}}>
									View KML file
								</MenuItem>
							) : null}
							{showAddandUploadButton && (
								<MenuItem>
									<KMLFileUploader isFpu fpuId={detailProps?.data?.id} />
								</MenuItem>
							)}
						</Menu>
					</>
				) : (
					<Stack flexDirection='row'>
						{editButton ? (
							<Button
								sx={{ color: theme?.palette?.neutral[300] }}
								variant='text'
								onClick={() => setEditItem(true)}>
								Edit
							</Button>
						) : null}
						{deleteButton ? (
							<Button variant='text' onClick={() => setDeleteItem(true)}>
								Delete
							</Button>
						) : null}
					</Stack>
				)}

				{editItem && entityType === EntityTabEnum.bags ? (
					<ActionInformationDrawer
						open={editItem}
						onClose={() => setEditItem(false)}
						anchor='right'
						component={
							<AddPackagingBag
								csinkNetworkDetails={csinkNetworkDetails}
								subheading={subheading}
								artisanProDetails={artisanProDetails}
								bagDetails={bagDetails}
								isCsink={isCsink}
								editMode
								handleCloseDrawer={() => setEditItem(false)}
							/>
						}
					/>
				) : null}
				{editItem && entityType === EntityTabEnum.vehicles ? (
					<ActionInformationDrawer
						open={editItem}
						onClose={() => setEditItem(false)}
						anchor='right'
						component={
							<AddVehicle
								subheading={subheading}
								vehicleDetails={vehicleDetails}
								handleCloseDrawer={() => setEditItem(false)}
							/>
						}
					/>
				) : null}
				{editItem && entityType === EntityTabEnum.kilns ? (
					<ActionInformationDrawer
						open={editItem}
						onClose={() => setEditItem(false)}
						anchor='right'
						component={
							<AddKiln
								handleClose={() => setEditItem(false)}
								subheading={subheading}
								editMode={true}
								kilnDetails={kilnDetails}
							/>
						}
					/>
				) : null}
				{editItem && entityType === EntityTabEnum.containers ? (
					<ActionInformationDrawer
						open={editItem}
						onClose={() => setEditItem(false)}
						anchor='right'
						component={
							<AddContainer
								editMode
								isCsink={isCsink}
								cSinkNetworkDetails={csinkNetworkDetails}
								handleCloseDrawer={() => setEditItem(false)}
								subheading={subheading}
								containerDetails={containerDetails}
							/>
						}
					/>
				) : null}
				{editItem && entityType === EntityTabEnum.samplingContainer ? (
					<ActionInformationDrawer
						open={editItem}
						onClose={() => setEditItem(false)}
						anchor='right'
						component={
							<AddContainer
								editMode
								isSamplingContainer={true}
								subheading={subheading}
								cSinkNetworkDetails={csinkNetworkDetails}
								handleCloseDrawer={() => setEditItem(false)}
								containerDetails={containerDetails}
							/>
						}
					/>
				) : null}
				{editItem && entityType === EntityTabEnum.biomassSource ? (
					<ActionInformationDrawer
						open={editItem}
						onClose={() => setEditItem(false)}
						anchor='right'
						component={
							<AddBiomassSource
								handleClose={() => setEditItem(false)}
								editMode
								biomassSourceDetails={biomassSourceDetails}
							/>
						}
					/>
				) : null}

				{editItem &&
				entityType === EntityTabEnum.methaneCompensationStrategy ? (
					<ActionInformationDrawer
						open={editItem}
						onClose={() => setEditItem(false)}
						anchor='right'
						component={
							<EditMethaneCompensate
								editMode
								isCsink={isCsink}
								artisanProDetails={artisanProDetails}
								selectedMethaneCompensateId={methaneCompensateId}
								csinkNetworkDetails={csinkNetworkDetails}
								handleCloseDrawer={() => setEditItem(false)}
							/>
						}
					/>
				) : null}

				{deleteItem ? (
					<Confirmation
						confirmationText={`Are you sure you want to delete ${entityName}?`}
						handleClose={() => setDeleteItem(false)}
						handleNoClick={() => setDeleteItem(false)}
						handleYesClick={() => {
							handleDelete?.()
							setDeleteItem(false)
						}}
						open={deleteItem}
					/>
				) : null}
			</Stack>
			{imageList?.length ? (
				<ImageCarouselDialog
					open={!!imageList?.length}
					close={() => {
						setImageList([])
					}}
					ImagesList={imageList ?? []}
					showDownload={false}
				/>
			) : null}
		</>
	)
}
