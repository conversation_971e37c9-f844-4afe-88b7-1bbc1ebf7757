import {
	Box,
	Button,
	Divider,
	FormControl,
	IconButton,
	InputLabel,
	MenuItem,
	Select,
	Stack,
	Tab,
	Typography,
	styled,
} from '@mui/material'
import { Close, Edit } from '@mui/icons-material'
import { theme } from '@/lib/theme/theme'
import { <PERSON>ading<PERSON>utton, Tab<PERSON>ontext, Tab<PERSON>ist, TabPanel } from '@mui/lab'

import { useEditNetworks } from './useEditNetworks'
import { EntityColumns } from './EntityColumns'
import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import { IArtisanProDetails, INetwork } from '@/interfaces'
import { AddEntityType } from './AddEntityType'
import { NoData } from '../NoData'
import EditIcon from '@/assets/icons/editIcon.svg'
import { EntityTabEnum, userRoles } from '@/utils/constant'
import { capitalizeFirstLetter } from '@/utils/helper'
import { StrategyContent } from './StrategyContent'
import { ActionInformationDrawer } from '../ActionInformationDrawer'
import { AddEntity } from '../SideDrawerComponents'
import { useParams } from 'react-router-dom'

const tabs = ({
	isCsink,
	isAdmin,
}: {
	isCsink: boolean
	isAdmin?: boolean
}) => [
		{
			label: 'Bags',
			value: EntityTabEnum.bags,
			hidden: false,
		},
		{
			label: 'Containers',
			value: EntityTabEnum.containers,
			hidden: !isCsink,
		},
		// {
		// 	label: 'Vehicles',
		// 	value: EntityTabEnum.vehicles,
		// 	hidden: isCsink,
		// },
		{
			label: 'Preferred Biomass',
			value: EntityTabEnum.preferredBiomass,
			hidden: false,
		},
		{
			label: 'Admins',
			value: EntityTabEnum.admins,
			hidden: false,
		},
		{
			label: 'Mixing Type',
			value: EntityTabEnum.mixingTypes,
			hidden: !isAdmin,
		},
		{
			label: 'Application Type',
			value: EntityTabEnum.applicationTypes,
			hidden: !isAdmin,
		},
		{
			label: 'Methane Compensation Strategy',
			value: EntityTabEnum.methaneCompensationStrategy,
			hidden: isCsink,
		},
		{
			label: 'Assign Kilns',
			value: EntityTabEnum.assignkiln,
			hidden: false,
		},
		{
			label: 'Assign Measuring Container',
			value: EntityTabEnum.assignMeasuringContainer,
			hidden: false,
		},
	]

export const EditNetwork = ({
	csinkNetworkDetails,
	artisanProDetails,
	openTab,
	isCsink = true,
	handleClose,
}: {
	csinkNetworkDetails?: INetwork
	artisanProDetails?: IArtisanProDetails
	handleClose: () => void
	isCsink?: boolean
	openTab?: EntityTabEnum
}) => {
	const {
		bagsList,
		containerList,
		preferredBiomassList,
		vehicleList,
		handleDeleteBag,
		handleDeleteVehicle,
		handleDeleteContainer,
		setTab,
		tab,
		mixingTypeQuery,
		applicationTypeQuery,
		setUserAllDetails,
		setAddNewNetwork,
		addNewNetwork,
		otherNetwork,
		handleSave,
		userDetails,
		assignedKilnsListQuery,
		assignedMeasuringContainersListQuery,
	} = useEditNetworks({ isCsink })

	const handleTabChange = useCallback(
		(_: unknown, newValue: EntityTabEnum) => {
			setTab(newValue)
		},
		[setTab]
	)

	const [showEditNetworkDetails, handleShowEditNetworkDetails] = useState(false)
	const { artisanProId, cSinkNetworkId } = useParams()

	const canDeleteContainer = useMemo(
		() =>
			userDetails?.accountType === userRoles.Admin ||
			userDetails?.accountType === userRoles.CsinkManager,
		[userDetails?.accountType]
	)

	const canHideAssignAdminButton = () => {
		return (
			userDetails?.accountType === userRoles.cSinkNetwork ||
			userDetails?.accountType === userRoles.ArtisanPro
		)
	}

	const entityDetails = {
		name: isCsink ? csinkNetworkDetails?.name : artisanProDetails?.name,
		networkId: isCsink
			? csinkNetworkDetails?.shortName
			: artisanProDetails?.shortCode,
		location: isCsink
			? csinkNetworkDetails?.locationName
			: artisanProDetails?.address,
		managerDetails: isCsink
			? csinkNetworkDetails?.managerDetails
			: artisanProDetails?.managerDetails,
	}

	const entitySubHeading = `${entityDetails?.name} (${entityDetails?.networkId})`

	// const disableEditandAdd = useMemo(() => {
	// 	return (
	// 		artisanProDetails?.isCsinkManagerSuspended ||
	// 		artisanProDetails?.isBiomassAggregatorSuspended ||
	// 		artisanProDetails?.isArtisanProNetworkSuspended ||
	// 		artisanProDetails?.isArtisanProSuspended ||
	// 		csinkNetworkDetails?.isCsinkManagerSuspended ||
	// 		csinkNetworkDetails?.isBiomassAggregatorSuspended ||
	// 		csinkNetworkDetails?.isCsinkNetworkSuspended
	// 	)
	// }, [
	// 	artisanProDetails?.isArtisanProNetworkSuspended,
	// 	artisanProDetails?.isArtisanProSuspended,
	// 	artisanProDetails?.isBiomassAggregatorSuspended,
	// 	artisanProDetails?.isCsinkManagerSuspended,
	// 	csinkNetworkDetails?.isBiomassAggregatorSuspended,
	// 	csinkNetworkDetails?.isCsinkManagerSuspended,
	// 	csinkNetworkDetails?.isCsinkNetworkSuspended,
	// ])

	const AssignAdmin: FC = () => {
		if (canHideAssignAdminButton()) return null
		return addNewNetwork === false ? (
			<Button
				sx={{
					color: 'red',
				}}
				// disabled={disableEditandAdd}
				onClick={() => {
					setUserAllDetails(isCsink ? csinkNetworkDetails : artisanProDetails)
					setAddNewNetwork(true)
				}}>
				+ Assign Admin
			</Button>
		) : (
			<Box
				sx={{
					width: '100%',
					height: '100%',
				}}>
				<FormControl sx={{ width: '100%' }}>
					<InputLabel
						id='network-manager-label'
						sx={{
							fontSize: '1.2rem',
						}}>
						Assign Network Manager
					</InputLabel>
					<Select
						variant='outlined'
						labelId='network-manager-label'
						value={addNewNetwork}
						label='Assign Network Manager'
						onChange={(e) => {
							setAddNewNetwork(e.target.value as string)
						}}>
						{isCsink ? (
							addNewNetwork !== false ? (
								otherNetwork?.data?.kilnOperatorOrFarmer?.length ? (
									(() => {
										const filteredItems =
											otherNetwork?.data?.kilnOperatorOrFarmer?.filter(
												(item) =>
													item.accountType === 'network_admin' ||
													item.accountType === 'artisan_pro_admin'
											)

										return filteredItems?.length ? (
											filteredItems?.map((item, idx) => (
												<MenuItem key={idx} value={item.id}>
													{item.name}
												</MenuItem>
											))
										) : (
											<MenuItem disabled>No options available</MenuItem>
										)
									})()
								) : (
									<MenuItem disabled>No options available</MenuItem>
								)
							) : null
						) : addNewNetwork !== false ? (
							otherNetwork?.data?.artisianProOperators?.length ? (
								(() => {
									const filteredItems =
										otherNetwork?.data?.artisianProOperators?.filter(
											(item) =>
												item.accountType === 'network_admin' ||
												item.accountType === 'artisan_pro_admin'
										)

									return filteredItems?.length ? (
										filteredItems?.map((item, idx) => (
											<MenuItem key={idx} value={item.id}>
												{item.name}
											</MenuItem>
										))
									) : (
										<MenuItem disabled>No options available</MenuItem>
									)
								})()
							) : (
								<MenuItem disabled>No options available</MenuItem>
							)
						) : null}
					</Select>
					<Box
						sx={{
							display: 'flex',
							paddingTop: 2,
							justifyContent: 'center',
							alignItems: 'center',
							gap: 3,
						}}>
						<LoadingButton
							variant='outlined'
							onClick={() => {
								setAddNewNetwork(false)
							}}>
							Cancel
						</LoadingButton>
						<LoadingButton
							variant='contained'
							disabled={addNewNetwork === true ? true : false}
							onClick={() => {
								handleSave(addNewNetwork)
								setAddNewNetwork(false)
							}}>
							Save
						</LoadingButton>
					</Box>
				</FormControl>
			</Box>
		)
	}

	useEffect(() => {
		if (openTab) {
			setTab(openTab)
		}
	}, [openTab, setTab])

	return (
		<StyleContainer>
			<Stack className='header'>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Stack>
						<Typography variant='h5'>
							{entityDetails?.name}
							{(artisanProId || cSinkNetworkId) && (
								<IconButton
									onClick={() => handleShowEditNetworkDetails(true)}
								// disabled={disableEditandAdd}
								>
									<Edit fontSize='small' />{' '}
								</IconButton>
							)}
						</Typography>

						<ActionInformationDrawer
							open={showEditNetworkDetails}
							onClose={() => handleShowEditNetworkDetails(false)}
							anchor='right'
							component={
								<AddEntity
									handleCloseDrawer={() => handleShowEditNetworkDetails(false)}
									editMode={!!entityDetails?.networkId}
									apsId={artisanProId}
									cSinkNetworkId={cSinkNetworkId}
									type={artisanProId ? 'aps' : 'cSinkNetwork'}
								/>
							}
						/>

						<Typography variant='subtitle1'>
							Network Id: {entityDetails?.networkId} {entityDetails?.location}
						</Typography>
					</Stack>
					<IconButton onClick={handleClose}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Stack className='container' component='form'>
				<TabContext value={tab}>
					<TabList className='tabList' onChange={handleTabChange}>
						{tabs({
							isCsink,
							isAdmin: userDetails?.accountType === userRoles.Admin,
						}).map(
							({ label, value, hidden }, index) =>
								!hidden && <Tab key={index} label={label} value={value} />
						)}
					</TabList>
					<TabPanel value={EntityTabEnum.bags} sx={{ padding: 0 }}>
						<Stack>
							<AddEntityType
								subheading={entitySubHeading}
								isCsink={isCsink}
								// disabled={!!disableEditandAdd}
								artisanProDetails={artisanProDetails}
								cSinkNetworkDetails={csinkNetworkDetails}
								label='Bag'
								entityType={EntityTabEnum.bags}
							/>
							<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
								{bagsList?.length ? (
									bagsList?.map((bag, index) => {
										const bagType =
											bag?.measuringType === 'volume_based'
												? 'Volume-based'
												: 'Weight-based'
										const subText = `${bagType} (${bag?.quantity} ${bag?.quantityUnit})`
										return (
											<Stack key={index} alignItems='center'>
												<EntityColumns
													isCsink={isCsink}
													subheading={entitySubHeading}
													entityType={EntityTabEnum.bags}
													csinkNetworkDetails={csinkNetworkDetails}
													artisanProDetails={artisanProDetails}
													bagDetails={bag}
													icon={bag?.imageURLs?.[0]}
													entityName={bag?.name}
													// editButton={!disableEditandAdd}
													editButton
													deleteButton
													handleDelete={() => handleDeleteBag(bag?.id)}
													subText={subText}
												/>
												<Divider
													color={theme.palette.custom.grey[200]}
													sx={{
														width: '80%',
													}}
												/>
											</Stack>
										)
									})
								) : (
									<NoData size='small' />
								)}
							</Stack>
						</Stack>
					</TabPanel>
					<TabPanel value={EntityTabEnum.containers} sx={{ padding: 0 }}>
						<Stack>
							<AddEntityType
								subheading={entitySubHeading}
								isCsink={isCsink}
								cSinkNetworkDetails={csinkNetworkDetails}
								// disabled={!!disableEditandAdd}
								label='Conatiner'
								entityType={EntityTabEnum.containers}
							/>
							<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
								{containerList?.length ? (
									containerList?.map((container, index) => {
										const subText = `${container?.shape} (${container?.volume} ltrs)`
										return (
											<Stack key={index} alignItems='center'>
												<EntityColumns
													subheading={entitySubHeading}
													icon={container?.imageURLs?.[0]}
													containerDetails={container}
													csinkNetworkDetails={csinkNetworkDetails}
													entityType={EntityTabEnum.containers}
													entityName={container?.name}
													handleDelete={() =>
														handleDeleteContainer(container?.id)
													}
													// editButton={!disableEditandAdd}
													editButton
													deleteButton={canDeleteContainer}
													subText={subText}
												/>
												<Divider
													color={theme.palette.custom.grey[200]}
													sx={{
														width: '80%',
													}}
												/>
											</Stack>
										)
									})
								) : (
									<NoData size='small' />
								)}
							</Stack>
						</Stack>
					</TabPanel>
					<TabPanel value={EntityTabEnum.vehicles} sx={{ padding: 0 }}>
						<Stack>
							<AddEntityType
								subheading={entitySubHeading}
								cSinkNetworkDetails={csinkNetworkDetails}
								// disabled={!!disableEditandAdd}
								label='Vehicle'
								entityType={EntityTabEnum.vehicles}
							/>
							<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
								{vehicleList?.length ? (
									vehicleList?.map((vehicle, index) => {
										const subText = `${vehicle?.number ?? ''
											} (${vehicle?.fuelType?.replace('_', ' ')})`
										return (
											<Stack key={index} alignItems='center'>
												<EntityColumns
													subheading={entitySubHeading}
													icon={vehicle?.imageURLs?.[0]}
													entityName={vehicle?.name ?? ''}
													subText={subText}
													entityType={EntityTabEnum.vehicles}
													deleteButton
													handleDelete={() => {
														handleDeleteVehicle(vehicle?.id ?? '')
													}}
												/>
												<Divider
													color={theme.palette.custom.grey[200]}
													sx={{
														width: '80%',
													}}
												/>
											</Stack>
										)
									})
								) : (
									<NoData size='small' />
								)}
							</Stack>
						</Stack>
					</TabPanel>
					<TabPanel
						value={EntityTabEnum.methaneCompensationStrategy}
						sx={{ padding: 0 }}>
						<Stack>
							<AddEntityType
								subheading={entitySubHeading}
								cSinkNetworkDetails={csinkNetworkDetails}
								artisanProDetails={artisanProDetails}
								// disabled={!!disableEditandAdd}
								label='Methane'
								isCsink={isCsink}
								showSearchField={false}
								entityType={EntityTabEnum.methaneCompensationStrategy}
							/>
							<StrategyContent
								// disabled={!!disableEditandAdd}
								handleClose={handleClose}
								type={EntityTabEnum.methaneCompensationStrategy}
								artisanProDetails={artisanProDetails}
								csinkNetworkDetails={csinkNetworkDetails}
							/>
						</Stack>
					</TabPanel>
					<TabPanel value={EntityTabEnum.preferredBiomass} sx={{ padding: 0 }}>
						<Stack>
							<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
								<AddEntityType
									subheading={entitySubHeading}
									cSinkNetworkDetails={csinkNetworkDetails}
									artisanProDetails={artisanProDetails}
									// disabled={!!disableEditandAdd}
									preferredBiomassList={preferredBiomassList}
									isCsink={isCsink}
									label='Biomass'
									showSearchField={false}
									{...(!preferredBiomassList?.length
										? {
											iconButton: (
												<Box
													component='img'
													src={EditIcon}
													alt='edit-icon'
													height={28}
													width={24}
												/>
											),
										}
										: {})}
									entityType={EntityTabEnum.preferredBiomass}
								/>
								{preferredBiomassList?.length ? (
									preferredBiomassList?.map((biomass, index) => {
										const utcDate = new Date(String(biomass?.createdAt))
										return (
											<Stack key={index} alignItems='center'>
												<EntityColumns
													subheading={entitySubHeading}
													icon={biomass?.image}
													entityName={biomass?.name ?? ''}
													subText={utcDate.toLocaleDateString()}
												/>
												<Divider
													color={theme.palette.custom.grey[200]}
													sx={{
														width: '80%',
													}}
												/>
											</Stack>
										)
									})
								) : (
									<NoData size='small' />
								)}
							</Stack>
						</Stack>
					</TabPanel>
					<TabPanel value={EntityTabEnum.assignkiln} sx={{ padding: 0 }}>
						<Stack>
							<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
								<AddEntityType
									subheading={entitySubHeading}
									cSinkNetworkDetails={csinkNetworkDetails}
									artisanProDetails={artisanProDetails}
									isCsink={isCsink}
									// disabled={!!disableEditandAdd}
									label='Assign Kilns'
									showSearchField={false}
									iconButton={
										<Box
											component='img'
											src={EditIcon}
											alt='edit-icon'
											height={28}
											width={24}
										/>
									}
									entityType={EntityTabEnum.assignkiln}
								/>
								{assignedKilnsListQuery?.data?.kilns?.length ? (
									assignedKilnsListQuery?.data?.kilns?.map((kiln, index) => {
										const subText = kiln?.kilnShape?.replace('_', ' ')
										return (
											<Stack key={index} alignItems='center'>
												<EntityColumns
													subheading={entitySubHeading}
													entityName={kiln?.name ?? ''}
													subText={subText}
												/>
												<Divider
													color={theme.palette.custom.grey[200]}
													sx={{
														width: '80%',
													}}
												/>
											</Stack>
										)
									})
								) : (
									<NoData size='small' />
								)}
							</Stack>
						</Stack>
					</TabPanel>
					<TabPanel
						value={EntityTabEnum.assignMeasuringContainer}
						sx={{ padding: 0 }}>
						<Stack>
							<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
								<AddEntityType
									subheading={entitySubHeading}
									cSinkNetworkDetails={csinkNetworkDetails}
									artisanProDetails={artisanProDetails}
									preferredBiomassList={preferredBiomassList}
									// disabled={!!disableEditandAdd}
									isCsink={isCsink}
									label='Assign Measuring Containers'
									showSearchField={false}
									iconButton={
										<Box
											component='img'
											src={EditIcon}
											alt='edit-icon'
											height={28}
											width={24}
										/>
									}
									entityType={EntityTabEnum.assignMeasuringContainer}
								/>
								{assignedMeasuringContainersListQuery?.data?.containers
									?.length ? (
									assignedMeasuringContainersListQuery?.data?.containers?.map(
										(container, index) => {
											const subText = `${container?.shape?.replace(
												'_',
												' '
											)} (${container?.volume} ltrs)`

											return (
												<Stack key={index} alignItems='center'>
													<EntityColumns
														subheading={entitySubHeading}
														entityName={container?.name ?? ''}
														subText={subText}
													/>
													<Divider
														color={theme.palette.custom.grey[200]}
														sx={{
															width: '80%',
														}}
													/>
												</Stack>
											)
										}
									)
								) : (
									<NoData size='small' />
								)}
							</Stack>
						</Stack>
					</TabPanel>
					<TabPanel value={EntityTabEnum.admins} sx={{ padding: 0 }}>
						<Stack>
							<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
								{entityDetails?.managerDetails?.length ? (
									entityDetails?.managerDetails.map((manager, index) => {
										const subtext = (
											<>
												{manager?.countryCode && (
													<>
														({manager.countryCode} {manager.managerPhone})
														<br />
													</>
												)}
												{manager?.managerEmail}
											</>
										)
										return (
											<Stack key={index} alignItems='center'>
												<EntityColumns
													subheading={entitySubHeading}
													icon={manager?.profileImageUrl}
													entityName={manager?.managerName}
													subText={subtext}
												/>
												<Divider
													color={theme.palette.custom.grey[200]}
													sx={{
														width: '80%',
													}}
												/>
											</Stack>
										)
									})
								) : (
									<>
										<NoData size='small' />
										<AssignAdmin />
									</>
								)}
								{entityDetails?.managerDetails?.length ? (
									<Box
										sx={{
											display: 'flex',
											width: '100%',
											justifyContent: 'end',
											paddingTop: 2,
										}}>
										<AssignAdmin />
									</Box>
								) : null}
							</Stack>
						</Stack>
					</TabPanel>
					<TabPanel value={EntityTabEnum.mixingTypes} sx={{ padding: 0 }}>
						<Stack>
							<Stack>
								<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
									<AddEntityType
										subheading={entitySubHeading}
										cSinkNetworkDetails={csinkNetworkDetails}
										artisanProDetails={artisanProDetails}
										mixingTypeQuery={mixingTypeQuery}
										// disabled={!!disableEditandAdd}
										isCsink={isCsink}
										label='Edit'
										showSearchField={false}
										{...(!mixingTypeQuery?.data?.length
											? {
												iconButton: (
													<Box
														component='img'
														src={EditIcon}
														alt='edit-icon'
														height={28}
														width={24}
													/>
												),
											}
											: {})}
										entityType={EntityTabEnum.mixingTypes}
									/>
									{mixingTypeQuery?.data?.length ? (
										mixingTypeQuery?.data?.map((mixingItem, index) => {
											const subText = mixingItem?.type
											return (
												<Stack key={index} alignItems='center'>
													<EntityColumns
														subheading={entitySubHeading}
														entityName={mixingItem?.name ?? ''}
														subText={subText}
													/>
													<Divider
														color={theme.palette.custom.grey[200]}
														sx={{
															width: '100%',
														}}
													/>
												</Stack>
											)
										})
									) : (
										<NoData size='small' />
									)}
								</Stack>
							</Stack>
						</Stack>
					</TabPanel>
					<TabPanel value={EntityTabEnum.applicationTypes} sx={{ padding: 0 }}>
						<Stack>
							<Stack>
								<Stack paddingTop={theme.spacing(2.5)} gap={theme.spacing(1.5)}>
									<AddEntityType
										subheading={entitySubHeading}
										cSinkNetworkDetails={csinkNetworkDetails}
										artisanProDetails={artisanProDetails}
										applicationTypeQuery={applicationTypeQuery}
										// disabled={!!disableEditandAdd}
										isCsink={isCsink}
										label='Edit'
										showSearchField={false}
										{...(!applicationTypeQuery?.data?.length
											? {
												iconButton: (
													<Box
														component='img'
														src={EditIcon}
														alt='edit-icon'
														height={28}
														width={24}
													/>
												),
											}
											: {})}
										entityType={EntityTabEnum.applicationTypes}
									/>
									{applicationTypeQuery?.data?.length ? (
										applicationTypeQuery?.data?.map(
											(applicationItem, index) => {
												const subText = applicationItem?.category
												return (
													<Stack key={index} alignItems='center'>
														<EntityColumns
															subheading={entitySubHeading}
															entityName={capitalizeFirstLetter(
																applicationItem?.type ?? ''
															)}
															subText={subText}
														/>
														<Divider
															color={theme.palette.custom.grey[200]}
															sx={{
																width: '100%',
															}}
														/>
													</Stack>
												)
											}
										)
									) : (
										<NoData size='small' />
									)}
								</Stack>
							</Stack>
						</Stack>
					</TabPanel>
				</TabContext>
			</Stack>
		</StyleContainer>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(4.5, 2),
		borderBottom: `${theme.spacing(0.125)} solid ${theme.palette.neutral['100']
			}`,
	},
	'.tabList': {
		'& .MuiTabs-scroller': {
			overflow: 'auto !important',
		},
		'& ::-webkit-scrollbar': {
			display: 'none',
		},
		'-ms-overflow-style': 'none' /* IE and Edge */,
		'scrollbar-width': 'none' /* Firefox */,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
		gap: theme.spacing(2.5),
		'.add_btn': {
			textTransform: 'none',
			maxHeight: theme.spacing(6),
			// width: '100%',
			maxWidth: theme.spacing(28.2),
			minWidth: theme.spacing(20.2),
			borderColor: theme.palette.neutral[300],
			color: theme.palette.neutral[300],
			borderRadius: 8,
		},
	},
}))
