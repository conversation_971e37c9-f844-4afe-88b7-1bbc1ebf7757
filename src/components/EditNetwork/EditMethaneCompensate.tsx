import { FC, useCallback } from 'react'
import { FormProvider, useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'

import {
	But<PERSON>,
	Divider,
	IconButton,
	Stack,
	Typography,
	useTheme,
} from '@mui/material'
import { LoadingButton } from '@mui/lab'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { authAxios } from '@/contexts'
import { toast } from 'react-toastify'
import { AddMethaneCompensateFields } from '@/components/AddMethaneCompensate'
import { AxiosError } from 'axios'
import * as Yup from 'yup'
import { Close } from '@mui/icons-material'
import { IArtisanProDetails, INetwork } from '@/interfaces'

type TProps = {
	handleCloseDrawer: () => void
	editMode?: boolean
	isCsink?: boolean
	artisanProDetails?: IArtisanProDetails
	csinkNetworkDetails?: INetwork
	selectedMethaneCompensateId?: string
	setSelectedMethaneCompensateId?: React.Dispatch<React.SetStateAction<string>>
}

const schema = Yup.object({
	methaneCompensateStrategies: Yup.array()
		.of(
			Yup.object().shape({
				methaneCompensateId: Yup.string().nullable(),
				biomassId: Yup.string().nullable(),
				methaneCompensateType: Yup.string().nullable(),
				description: Yup.string().nullable(),
				compensateType: Yup.string().nullable(),
				documentIds: Yup.array()
					.of(
						Yup.object().shape({
							id: Yup.string().nullable(),
							url: Yup.string().nullable(),
							fileName: Yup.string().nullable(),
						})
					)
					.nullable(),
			})
		)
		.optional(),
})

export type TEditMethaneCompensate = Yup.InferType<typeof schema>

export const EditMethaneCompensate: FC<TProps> = ({
	handleCloseDrawer,
	editMode,
	csinkNetworkDetails,
	artisanProDetails,
	isCsink = true,
	selectedMethaneCompensateId = '',
	setSelectedMethaneCompensateId,
}) => {
	const networkDetails = isCsink ? csinkNetworkDetails : artisanProDetails
	const selectedMethaneCompensate =
		networkDetails?.methaneCompensateStrategies?.find(
			(methaneCompensate) =>
				methaneCompensate.id === selectedMethaneCompensateId
		)

	// Specifically done inside the array
	const initialValues = {
		methaneCompensateStrategies: [
			{
				methaneCompensateId: selectedMethaneCompensateId || null,
				biomassId: selectedMethaneCompensate?.biomassId || null,
				description: selectedMethaneCompensate?.description || null,
				methaneCompensateType:
					selectedMethaneCompensate?.methaneCompensateType || null,
				compensateType: selectedMethaneCompensate?.compensateType || null,
				documentIds: selectedMethaneCompensate?.documents || [],
			},
		],
	}

	const theme = useTheme()
	const queryClient = useQueryClient()
	const form = useForm<TEditMethaneCompensate>({
		defaultValues: initialValues,
		mode: 'all',
		resolver: yupResolver<TEditMethaneCompensate>(schema),
	})

	const { handleSubmit } = form

	const editMethaneCompensate = useMutation({
		mutationKey: ['editMethaneCompensate'],
		mutationFn: async ({
			methaneCompensateStrategies,
		}: TEditMethaneCompensate) => {
			const api = isCsink
				? `/cs-network/${csinkNetworkDetails?.id}`
				: `/artisan-pro-network/${artisanProDetails?.artisianProNetworkId}/artisian-pro/${artisanProDetails?.id}`
			const artisanPayload = {
				address: artisanProDetails?.address,
				artisianProNetworkID: artisanProDetails?.artisianProNetworkId,
				name: artisanProDetails?.name,
				bighaInHectare: artisanProDetails?.bighaInHectare,
			}
			const csinkPayload = {
				name: csinkNetworkDetails?.name,
				locationName: csinkNetworkDetails?.locationName,
				bighaInHectare: csinkNetworkDetails?.bighaInHectare,
			}
			const newPayload = {
				...(isCsink ? csinkPayload : artisanPayload),
				methaneCompensateStrategies: [
					{
						...methaneCompensateStrategies?.[0],
						documentIds: methaneCompensateStrategies?.[0]?.documentIds?.map(
							(item) => item?.id
						),
						methaneCompensateId: undefined,
					},
				],
			}

			return await authAxios.put(api, newPayload)
		},

		onError: (err: AxiosError) => {
			toast((err?.response?.data as { userToMessage: string })?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			if (isCsink) {
				queryClient.refetchQueries({
					queryKey: ['cSinkNetworkDetails'],
				})
			} else {
				queryClient.refetchQueries({
					queryKey: ['artisanProDetail'],
				})
			}
			setSelectedMethaneCompensateId?.('')
			handleCloseDrawer()
		},
	})

	const editSelectedMethaneCompensate = useMutation({
		mutationKey: ['editSelectedMethaneCompensate'],
		mutationFn: async ({
			methaneCompensateStrategies,
		}: TEditMethaneCompensate) => {
			const api = isCsink
				? `/cs-network/${csinkNetworkDetails?.id}/methane-compensate-strategy/${methaneCompensateStrategies?.[0]?.methaneCompensateId}`
				: `/artisian-pro/${artisanProDetails?.artisianProNetworkId}}/methane-compensate-strategy/${methaneCompensateStrategies?.[0]?.methaneCompensateId}`

			const payload = {
				...methaneCompensateStrategies?.[0],
				documentIds: methaneCompensateStrategies?.[0]?.documentIds?.map(
					(item) => item?.id
				),
			}

			return await authAxios.put(api, payload)
		},

		onError: (err: AxiosError) => {
			toast((err?.response?.data as { userToMessage: string })?.userToMessage)
		},
		onSuccess: ({ data }) => {
			toast(data?.message)
			if (isCsink) {
				queryClient.refetchQueries({
					queryKey: ['cSinkNetworkDetails'],
				})
			} else {
				queryClient.refetchQueries({
					queryKey: ['artisanProDetail'],
				})
			}
			setSelectedMethaneCompensateId?.('')
			handleCloseDrawer()
		},
	})

	const handleEditMethaneCompensate = useCallback(
		async (values: TEditMethaneCompensate) => {
			if (selectedMethaneCompensateId) {
				editSelectedMethaneCompensate.mutate(values)
			} else {
				editMethaneCompensate.mutate(values)
			}
		},
		[
			editMethaneCompensate,
			editSelectedMethaneCompensate,
			selectedMethaneCompensateId,
		]
	)

	const fetchBiomassTypesQuery = useQuery({
		queryKey: ['fetchBiomassTypeList'],
		queryFn: async () => {
			const { data } = await authAxios.get(`/drop-down/crops`)
			return data
		},
		select: (data) =>
			data?.cropDetails?.map((item: { id: string; name: string }) => ({
				label: item?.name,
				value: item?.id,
			})),
	})

	return (
		<Stack>
			<Stack padding={theme.spacing(2)}>
				<Stack
					direction='row'
					spacing={1}
					alignItems='center'
					width='100%'
					justifyContent='space-between'>
					<Typography variant='h5'>Edit Methane Compensate Strategy</Typography>
					<IconButton onClick={handleCloseDrawer}>
						<Close />
					</IconButton>
				</Stack>
			</Stack>
			<Divider />

			<FormProvider {...form}>
				<Stack gap={theme.spacing(3)} padding={theme.spacing(3)}>
					<AddMethaneCompensateFields
						editMode={editMode}
						fieldIndex={0}
						fetchBiomassTypeList={fetchBiomassTypesQuery?.data}
					/>
					<Stack
						direction='row'
						justifyContent='space-between'
						gap={2}
						className='buttonContainer'>
						<Button
							onClick={handleCloseDrawer}
							sx={{ border: `1px solid ${theme.palette.primary.main}` }}>
							Cancel
						</Button>
						<LoadingButton
							loading={editMethaneCompensate.isPending}
							disabled={editMethaneCompensate.isPending}
							onClick={handleSubmit(handleEditMethaneCompensate)}
							variant='contained'>
							Save
						</LoadingButton>
					</Stack>
				</Stack>
			</FormProvider>
		</Stack>
	)
}
