import { authAxios, useAuthContext } from '@/contexts'
import {
	IContainerDetails,
	IIPreferredBiomass,
	IPackagingBag,
	IOtherBuyers,
	IVehicle,
	ICsinkMixingType,
	ICsinkApplicationType,
	IArtisanProDetails,
	INetwork,
	otherNetworkType,
	GlobalKilnResponse,
} from '@/interfaces'
import {
	QueryFunctionContext,
	useMutation,
	useQuery,
	useQueryClient,
} from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import { useParams } from 'react-router-dom'
import { toast } from 'react-toastify'
import { AxiosError } from 'axios'
import { EntityTabEnum } from '@/utils/constant'

const getArtisanBagsList = async ({ queryKey }: QueryFunctionContext) => {
	const [artisanProId] = queryKey.slice(1)
	try {
		const { data } = await authAxios.get<{
			count: number
			bags: IPackagingBag[]
		}>(`/artisian-pro/${artisanProId}/bags`)
		return data
	} catch (err: any) {
		toast(err.response?.data?.messageToUser)
	}
}
const getCsinkBagsList = async ({ queryKey }: QueryFunctionContext) => {
	const [cSinkNetworkId] = queryKey.slice(2)
	const { data } = await authAxios<{
		count: number
		bags: IPackagingBag[]
	}>(`/cs-network/${cSinkNetworkId}/bags`)
	return data
}
const getCsinkOtherNetworkList = async ({ queryKey }: QueryFunctionContext) => {
	const cSinkNetworkId = queryKey[2] || ''
	const { data } = await authAxios<otherNetworkType>(
		`/cs-network/${cSinkNetworkId}/farmers-kiln-operator-only?limit=1000&page=0`
	)
	return data
}
const getArtisanProOtherNetworkList = async ({
	queryKey,
}: QueryFunctionContext) => {
	try {
		const artisanProId = queryKey[1] || ''
		const userAllDetails = queryKey[3] as IArtisanProDetails
		const { data } = await authAxios<otherNetworkType>(
			`/artisan-pro-network/${userAllDetails?.artisianProNetworkId}/artisian-pro/${artisanProId}/operator?limit=10000&page=0`
		)
		return data
	} catch (error) {
		throw error
	}
}

const getArtisanContainerList = async ({ queryKey }: QueryFunctionContext) => {
	const [artisanProId] = queryKey.slice(1)
	try {
		const { data } = await authAxios<{
			count: number
			containers: IContainerDetails[]
		}>(`artisian-pro/${artisanProId}/container`)
		return data
	} catch (err: any) {
		toast(err.response?.data?.messageToUser)
	}
}

const getArtisanPreferredBiomassList = async ({
	queryKey,
}: QueryFunctionContext) => {
	const [artisanProId] = queryKey.slice(1)
	try {
		const { data } = await authAxios<IIPreferredBiomass[]>(
			`artisian-pro/${artisanProId}/preferred-biomass-type`
		)
		return data
	} catch (err: any) {
		toast(err.response?.data?.messageToUser)
	}
}

const getArtisanProMixingType = async ({ queryKey }: QueryFunctionContext) => {
	const [artisanProId] = queryKey.slice(1)
	try {
		const { data } = await authAxios<ICsinkMixingType[]>(
			`/artisian-pro/${artisanProId}/mixing-types`
		)
		return data
	} catch (err: any) {
		toast(err.response?.data?.messageToUser)
	}
}
const getArtisanProApplicationType = async ({
	queryKey,
}: QueryFunctionContext) => {
	const [artisanProId] = queryKey.slice(1)
	try {
		const { data } = await authAxios<ICsinkApplicationType[]>(
			`/artisian-pro/${artisanProId}/application-types`
		)
		return data
	} catch (err: any) {
		toast(err.response?.data?.messageToUser)
	}
}
const getApiforMeasuringContainers = (
	isCsink?: boolean,
	artisanProId?: string,
	csinkNetworkId?: string
) => {
	if (isCsink)
		return `/cs-network/${csinkNetworkId}/measuring-container-template?isAssigned=true`
	else if (artisanProId)
		return `/artisian-pro/${artisanProId}/measuring-container-template?isAssigned=true`
	return ''
}
const getApiforGlobalKilns = (
	isCsink?: boolean,
	artisanProId?: string,
	csinkNetworkId?: string
) => {
	if (isCsink)
		return `/cs-network/${csinkNetworkId}/kiln-template?isAssigned=true`
	else if (artisanProId)
		return `/artisian-pro/${artisanProId}/kiln-template?isAssigned=true`
	return ''
}
export const useEditNetworks = ({ isCsink }: { isCsink: boolean }) => {
	const [tab, setTab] = useState(EntityTabEnum.bags)

	const { cSinkNetworkId, artisanProId } = useParams()
	const [addNewNetwork, setAddNewNetwork] = useState<Boolean | string>(false)
	const [userAllDetails, setUserAllDetails] = useState<
		IArtisanProDetails | INetwork
	>()
	const { userDetails } = useAuthContext()

	const queryClient = useQueryClient()
	const bagsListQuery = useQuery({
		queryKey: ['getBagsList', artisanProId, cSinkNetworkId],
		queryFn: isCsink ? getCsinkBagsList : getArtisanBagsList,
		enabled: tab === EntityTabEnum.bags,
	})
	const deletebagMutation = useMutation({
		mutationKey: ['deleteBagMutation'],
		mutationFn: async (id: string) => {
			const api = isCsink
				? `/cs-network/${cSinkNetworkId}/bags/${id}`
				: `/artisian-pro/${artisanProId}/bags/${id}`
			const { data } = await authAxios.delete(api)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['getBagsList'] })
		},
	})

	const handleDeleteBag = useCallback(
		(id: string) => {
			deletebagMutation.mutate(id)
		},
		[deletebagMutation]
	)

	const deleteVehicleMutation = useMutation({
		mutationKey: ['deleteVehicleMutation'],
		mutationFn: async (id: string) => {
			const api = `/cs-network/${cSinkNetworkId}/vehicles/${id}`
			const { data } = await authAxios.delete(api)
			return data
		},
		onError: (error: AxiosError) => {
			toast(
				(error?.response?.data as { messageToUser: string })?.messageToUser ??
					'Error deleting the vehicle.'
			)
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient.refetchQueries({ queryKey: ['getVehicleList'] })
		},
	})

	const handleDeleteVehicle = useCallback(
		(id: string) => {
			deleteVehicleMutation.mutate(id)
		},
		[deleteVehicleMutation]
	)

	const containerListQuery = useQuery({
		queryKey: [
			'getContainerList',
			artisanProId,
			cSinkNetworkId,
			tab,
			EntityTabEnum,
		],
		queryFn: !isCsink
			? getArtisanContainerList
			: async () => {
					const { data } = await authAxios<{
						count: number
						containers: IContainerDetails[]
					}>(`/cs-network/${cSinkNetworkId}/container`)
					return data
			  },
		enabled: tab === EntityTabEnum.containers,
	})
	const deleteContainerMutation = useMutation({
		mutationKey: ['deleteContainerMutation'],
		mutationFn: async (id: string) => {
			const { data } = await authAxios.delete(
				`/cs-network/${cSinkNetworkId}/container/${id}`
			)
			return data
		},
		onError: (error: AxiosError) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			containerListQuery.refetch()
		},
	})

	const handleDeleteContainer = useCallback(
		(id: string) => {
			deleteContainerMutation.mutate(id)
		},
		[deleteContainerMutation]
	)

	const vehicleListQuery = useQuery({
		queryKey: ['getVehicleList', artisanProId, cSinkNetworkId],
		queryFn: async () => {
			const { data } = await authAxios<{
				count: number
				vehicles: IVehicle[]
			}>(`/cs-network/${cSinkNetworkId}/vehicles`)
			return data
		},
		enabled: isCsink && tab === EntityTabEnum.vehicles,
	})
	const buyerListQuery = useQuery({
		queryKey: ['getBuyersList', artisanProId, cSinkNetworkId],
		queryFn: async () => {
			const { data } = await authAxios<{
				count: number
				othersBuyers: IOtherBuyers[]
			}>(`/cs-network/${cSinkNetworkId}/other-buyer`)
			return data
		},
		enabled: false,
	})
	const preferredBiomassListQuery = useQuery({
		queryKey: ['getPreferredBiomassList', artisanProId, cSinkNetworkId],
		queryFn: !isCsink
			? getArtisanPreferredBiomassList
			: async () => {
					const { data } = await authAxios<IIPreferredBiomass[]>(
						`/cs-network/${cSinkNetworkId}/preferred-biomass-type`
					)
					return data
			  },
		enabled: tab === EntityTabEnum.preferredBiomass,
	})
	const assignedKilnsListQuery = useQuery({
		queryKey: ['assignedKilnsListQuery', isCsink, artisanProId, cSinkNetworkId],
		queryFn: async () => {
			const { data } = await authAxios<GlobalKilnResponse>(
				getApiforGlobalKilns(isCsink, artisanProId, cSinkNetworkId)
			)
			return data
		},
		enabled: tab === EntityTabEnum.assignkiln,
	})
	const assignedMeasuringContainersListQuery = useQuery({
		queryKey: [
			'assignedMeasuringContainersListQuery',
			artisanProId,
			cSinkNetworkId,
			isCsink,
		],
		queryFn: async () => {
			const { data } = await authAxios<{
				containers: IContainerDetails[]
			}>(getApiforMeasuringContainers(isCsink, artisanProId, cSinkNetworkId))
			return data
		},
		enabled: tab === EntityTabEnum.assignMeasuringContainer,
	})

	const mixingTypeQuery = useQuery({
		queryKey: ['getMixingTypeQuery', artisanProId, cSinkNetworkId],
		queryFn: !isCsink
			? getArtisanProMixingType
			: async () => {
					const { data } = await authAxios.get<ICsinkMixingType[]>(
						`/cs-network/${cSinkNetworkId}/mixing-types`
					)
					return data
			  },
		enabled: tab === EntityTabEnum.mixingTypes,
	})

	const applicationTypeQuery = useQuery({
		queryKey: ['applicationTypeQuery', artisanProId, cSinkNetworkId],
		queryFn: !isCsink
			? getArtisanProApplicationType
			: async () => {
					const { data } = await authAxios.get<ICsinkApplicationType[]>(
						`/cs-network/${cSinkNetworkId}/application-types`
					)
					return data
			  },
		enabled: tab === EntityTabEnum.applicationTypes,
	})

	const saveOtherNetworkMutate = useMutation({
		mutationKey: ['saveOtherNetworkMutate'],
		mutationFn: async (id: string) => {
			const payload = isCsink
				? { csinkNetworkManagerId: id }
				: { artisanProManagerId: id }
			const endpoint = isCsink
				? `/cs-network/${cSinkNetworkId}/assign-manager`
				: `/artisan-pro-network/${
						(userAllDetails as IArtisanProDetails)?.artisianProNetworkId
				  }/artisian-pro/${artisanProId}/artisian-pro-manager/assign`
			const { data } = await authAxios.post(endpoint, payload)
			return data
		},
		onError: (error: any) => {
			toast((error?.response?.data as { messageToUser: string })?.messageToUser)
		},
		onSuccess: (data) => {
			toast(data?.message)
			queryClient.refetchQueries({
				queryKey: [isCsink ? 'cSinkNetworkDetails' : 'artisanProDetail'],
			})
		},
	})

	const otherNetwork = useQuery<otherNetworkType, Error>({
		queryKey: ['otherNetwork', artisanProId, cSinkNetworkId, userAllDetails],
		queryFn: isCsink ? getCsinkOtherNetworkList : getArtisanProOtherNetworkList,
		enabled: addNewNetwork !== false,
	})

	const handleSave = (id: string | Boolean) => {
		if (id === true) {
			toast('Please Select a Network Manager')
		} else {
			saveOtherNetworkMutate.mutate(id as string)
		}
	}

	return {
		bagsList: bagsListQuery.data?.bags ?? [],
		containerList: containerListQuery?.data?.containers ?? [],
		buyerList: buyerListQuery?.data?.othersBuyers ?? [],
		vehicleList: vehicleListQuery?.data?.vehicles ?? [],
		preferredBiomassList: preferredBiomassListQuery?.data ?? [],
		buyerListQuery,
		handleDeleteBag,
		handleDeleteVehicle,
		handleDeleteContainer,
		vehicleListQuery,
		preferredBiomassListQuery,
		containerListQuery,
		tab,
		setTab,
		bagsListQuery,
		mixingTypeQuery,
		applicationTypeQuery,
		setUserAllDetails,
		otherNetwork,
		addNewNetwork,
		setAddNewNetwork,
		assignedMeasuringContainersListQuery,
		userDetails,
		assignedKilnsListQuery,
		handleSave,
	}
}
