import { EntityTabEnum } from '@/utils/constant'
import { Button, IconButton, Stack, styled, Typography } from '@mui/material'
import { TwoColumnLayout } from '../TwoColumnLayout'
import { Add, Close, EditOutlined } from '@mui/icons-material'
import { IArtisanProDetails, IFileData, IMedia, INetwork } from '@/interfaces'
import { useState, useMemo } from 'react'
import { ActionInformationDrawer } from '../ActionInformationDrawer'
import { EditBiomassPreProcess } from './EditBiomassPreProcess'
import { EditMethaneCompensate } from './EditMethaneCompensate'
import { capitalizeFirstLetter } from '@/utils/helper'
import { NoData } from '../NoData'
import { TrainingProofRenderer } from '../TrainingProofRenderer'

interface StrategyContentProps {
	type: EntityTabEnum
	csinkNetworkDetails?: INetwork
	artisanProDetails?: IArtisanProDetails
	handleClose: () => void
	disabled?: boolean
}

const TagLine = ({ label, value = '' }: { label: string; value?: string }) => (
	<Stack gap={1} flexDirection={'row'} height={'100%'} alignItems={'center'}>
		<Typography variant='subtitle2'>{label}:</Typography>
		<Typography variant='subtitle1'>{value || '-'}</Typography>
	</Stack>
)

const EditableItem = ({
	label,
	value,
	onEdit,
	isEdit = true,
	disabled = false,
}: {
	label: string
	value: string | undefined
	onEdit: () => void
	isEdit?: boolean
	disabled?: boolean
}) => (
	<TwoColumnLayout
		left={<TagLine label={label} value={value} />}
		right={
			isEdit ? (
				<IconButton onClick={onEdit} size='small' disabled={disabled}>
					<EditOutlined fontSize='small' />
				</IconButton>
			) : (
				<></>
			)
		}
		gridBreakpoints={[10, 2]}
	/>
)

const MediaSection = ({ documents }: { documents?: IFileData[] }) => (
	<Stack flexWrap='wrap' mb={1}>
		<TrainingProofRenderer
			viewMode='table'
			showDocumentName
			componentSize={60}
			ShowDeleteOption={false}
			media={(documents as IMedia[]) || []}
		/>
	</Stack>
)

export const StrategyContent = ({
	type,
	csinkNetworkDetails,
	handleClose,
	artisanProDetails,
	disabled=false
}: StrategyContentProps) => {
	const data =
		csinkNetworkDetails?.biomassPreprocessingDetails ??
		artisanProDetails?.biomassPreprocessingDetails

	const methaneData =
		csinkNetworkDetails?.methaneCompensateStrategies ??
		artisanProDetails?.methaneCompensateStrategies

	const showDirectEditComponent = () => {
		if(disabled) return false
		// function  returns true only if every value is null
		else if (type === EntityTabEnum.methaneCompensationStrategy)
			return methaneData?.every(
				(value) =>
					value === null || (Array.isArray(value) && value?.length === 0)
			)
		else
			return Object.values(data as object).every(
				(value) =>
					value === null || (Array.isArray(value) && value?.length === 0)
			)
	}

	const [editItem, setEditItem] = useState<boolean>(
		showDirectEditComponent() ?? false
	)
	const [selectedMethaneCompensateId, setSelectedMethaneCompensateId] =
		useState<string>('')

	const displayContent = useMemo(() => {
		const renderBiomassStrategy = (
			<Stack gap={1.5}>
				<EditableItem
					label='Drying Type'
					value={capitalizeFirstLetter(data?.dryingType).split('_').join(' ')}
					onEdit={() => setEditItem(true)}
				/>
				<Typography variant='subtitle2'>More About it:</Typography>
				<Typography variant='subtitle1'>
					{capitalizeFirstLetter(data?.dryingStrategy).split('_').join(' ') ||
						'-'}
				</Typography>
				<Typography variant='subtitle2'>Attached Media: </Typography>
				<MediaSection documents={data?.dryingDocuments ?? []} />

				<EditableItem
					label='Shredding Type'
					value={capitalizeFirstLetter(data?.shreddingType)
						.split('_')
						.join(' ')}
					onEdit={() => setEditItem(true)}
					isEdit={false}
				/>
				<Typography variant='subtitle2'>More About it:</Typography>
				<Typography variant='subtitle1'>
					{capitalizeFirstLetter(data?.shreddingStrategy)
						.split('_')
						.join(' ') || '-'}
				</Typography>
				<Typography variant='subtitle2'>Attached Media: </Typography>
				<MediaSection documents={data?.shreddingDocuments ?? []} />
			</Stack>
		)

		const renderMethaneStrategy = methaneData?.length ? (
			<Stack gap={4}>
				{methaneData?.map((methaneDetails, idx) => (
					<Stack gap={1.5} key={idx}>
						<EditableItem
							label='Methane Type'
							value={
								capitalizeFirstLetter(methaneDetails?.methaneCompensateType)
									.split('_')
									.join(' ') || '-'
							}
							disabled={disabled}
							onEdit={() => {
								setSelectedMethaneCompensateId(methaneDetails?.id ?? '')
								setEditItem(true)
							}}
						/>
						<TagLine
							label={'Biomass Type'}
							value={
								capitalizeFirstLetter(methaneDetails?.biomassName)
									.split('_')
									.join(' ') || '-'
							}
						/>
						{methaneDetails?.compensateType ? (
							<TagLine
								label={'Compensate Type'}
								value={
									capitalizeFirstLetter(methaneDetails?.compensateType)
										.split('_')
										.join(' ') || '-'
								}
							/>
						) : null}
						<Typography variant='subtitle2'>More About it:</Typography>
						<Typography variant='subtitle1'>
							{methaneDetails?.description || '-'}
						</Typography>

						<Typography variant='subtitle2'>Attached Media:</Typography>
						<MediaSection documents={methaneDetails?.documents || []} />
					</Stack>
				))}
				{csinkNetworkDetails?.id ? (
					<Button
						variant='text'
						sx={{
							justifyContent: 'flex-start',
						}}
						onClick={() => setEditItem(true)}
						startIcon={<Add color='primary' />}>
						Add Methane Compensate
					</Button>
				) : null}
			</Stack>
		) : (
			<NoData size='small' />
		)

		switch (type) {
			case EntityTabEnum.biomassProcessingStrategy:
				return renderBiomassStrategy
			case EntityTabEnum.methaneCompensationStrategy:
				return renderMethaneStrategy
			default:
				return <></>
		}
	}, [
		data?.dryingType,
		data?.dryingStrategy,
		data?.dryingDocuments,
		data?.shreddingType,
		data?.shreddingStrategy,
		data?.shreddingDocuments,
		methaneData,
		csinkNetworkDetails?.id,
		type,
	])

	return (
		<>
			{editItem && type === EntityTabEnum.biomassProcessingStrategy && (
				<ActionInformationDrawer
					open={editItem}
					onClose={() => setEditItem(false)}
					anchor='right'
					component={
						<EditBiomassPreProcess
							isCsink={!!csinkNetworkDetails?.id}
							handleCloseDrawer={() => setEditItem(false)}
							csinkNetworkDetails={csinkNetworkDetails}
							artisanProDetails={artisanProDetails}
						/>
					}
				/>
			)}
			{editItem && type === EntityTabEnum.methaneCompensationStrategy && (
				<ActionInformationDrawer
					open={editItem}
					onClose={() => setEditItem(false)}
					anchor='right'
					component={
						<EditMethaneCompensate
							isCsink={!!csinkNetworkDetails?.id}
							selectedMethaneCompensateId={selectedMethaneCompensateId}
							setSelectedMethaneCompensateId={setSelectedMethaneCompensateId}
							artisanProDetails={artisanProDetails}
							handleCloseDrawer={() => setEditItem(false)}
							csinkNetworkDetails={csinkNetworkDetails}
						/>
					}
				/>
			)}
			<StyleContainer>
				{type !== EntityTabEnum.methaneCompensationStrategy ||
				csinkNetworkDetails?.id ? (
					<Stack className='header'>
						<Stack
							direction='row'
							spacing={1}
							alignItems='center'
							width='100%'
							justifyContent='space-between'>
							<Typography variant='body2'>
								{type === EntityTabEnum.biomassProcessingStrategy
									? 'Biomass Preprocessing Strategy'
									: 'Methane Compensate Strategy'}
							</Typography>
							<IconButton onClick={handleClose}>
								<Close />
							</IconButton>
						</Stack>
					</Stack>
				) : null}

				<Stack className='container' gap={2}>
					{displayContent}
				</Stack>
			</StyleContainer>
		</>
	)
}

const StyleContainer = styled(Stack)(({ theme }) => ({
	gap: theme.spacing(2),
	'.header': {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		padding: theme.spacing(2),
		borderBottom: `${theme.spacing(0.125)} solid ${
			theme.palette.neutral['100']
		}`,
	},
	'.container': {
		height: '100%',
		padding: theme.spacing(2, 3),
	},
}))
