import { IFarm } from "@/interfaces"

export const generateKML = (data: IFarm) => {
	let latLong = ''
	data.farmArea?.forEach(({ x, y }) => {
		latLong += `${y},${x}\n`
	})
	const kml = `<?xml version="1.0" encoding="UTF-8"?>
  <kml xmlns="http://www.opengis.net/kml/2.2">
    <Document>
      <name>${data.name} Farm Map</name>
      <description/>
      <Style id="poly-FF5252-1200-77-normal">
        <LineStyle>
          <color>ff5252ff</color>
          <width>1.2</width>
        </LineStyle>
        <PolyStyle>
          <color>4d5252ff</color>
          <fill>1</fill>
          <outline>1</outline>
        </PolyStyle>
      </Style>
      <Style id="poly-FF5252-1200-77-highlight">
        <LineStyle>
          <color>ff5252ff</color>
          <width>1.8</width>
        </LineStyle>
        <PolyStyle>
          <color>4d5252ff</color>
          <fill>1</fill>
          <outline>1</outline>
        </PolyStyle>
      </Style>
      <StyleMap id="poly-FF5252-1200-77">
        <Pair>
          <key>normal</key>
          <styleUrl>#poly-FF5252-1200-77-normal</styleUrl>
        </Pair>
        <Pair>
          <key>highlight</key>
          <styleUrl>#poly-FF5252-1200-77-highlight</styleUrl>
        </Pair>
      </StyleMap>
      <Folder>
        <name>Untitled layer</name>
        <Placemark>
          <name>${data.name}</name>
          <description><![CDATA[Farmer Name: ${data.name}<br>LandMark: ${data?.landmark}<br>LandMark: ${data?.size} ${data?.sizeUnit}]]></description>
          <styleUrl>#poly-FF5252-1200-77</styleUrl>
          <Polygon>
            <outerBoundaryIs>
              <LinearRing>
                <tessellate>1</tessellate>
                <coordinates>
                  ${latLong}
                </coordinates>
              </LinearRing>
            </outerBoundaryIs>
          </Polygon>
        </Placemark>
      </Folder>
    </Document>
  </kml>`

	const downloadTxtFile = () => {
		const element = document.createElement('a')
		const file = new Blob([kml], { type: 'text/plain' })
		element.href = URL.createObjectURL(file)
		element.download = `${data.name}-${data.landmark}.kml`
		document.body.appendChild(element)
		element.click()
	}
	downloadTxtFile()
}

export const generateKMLForFPU = (data: IFarm) => {
	let latLong = ''
	data.farmArea?.forEach(({ x, y }) => {
		latLong += `${y},${x}\n`
	})
	const kml = `<?xml version="1.0" encoding="UTF-8"?>
  <kml xmlns="http://www.opengis.net/kml/2.2">
    <Document>
      <name>${data.name} Biomass Source Map</name>
      <description/>
      <Style id="poly-FF5252-1200-77-normal">
        <LineStyle>
          <color>ff5252ff</color>
          <width>1.2</width>
        </LineStyle>
        <PolyStyle>
          <color>4d5252ff</color>
          <fill>1</fill>
          <outline>1</outline>
        </PolyStyle>
      </Style>
      <Style id="poly-FF5252-1200-77-highlight">
        <LineStyle>
          <color>ff5252ff</color>
          <width>1.8</width>
        </LineStyle>
        <PolyStyle>
          <color>4d5252ff</color>
          <fill>1</fill>
          <outline>1</outline>
        </PolyStyle>
      </Style>
      <StyleMap id="poly-FF5252-1200-77">
        <Pair>
          <key>normal</key>
          <styleUrl>#poly-FF5252-1200-77-normal</styleUrl>
        </Pair>
        <Pair>
          <key>highlight</key>
          <styleUrl>#poly-FF5252-1200-77-highlight</styleUrl>
        </Pair>
      </StyleMap>
      <Folder>
        <name>Untitled layer</name>
        <Placemark>
          <name>${data?.name}</name>
          <description><![CDATA[Biomass Source Name: ${data?.name}<br>Location: ${data?.landmark}</description>
          <styleUrl>#poly-FF5252-1200-77</styleUrl>
          <Polygon>
            <outerBoundaryIs>
              <LinearRing>
                <tessellate>1</tessellate>
                <coordinates>
                  ${latLong}
                </coordinates>
              </LinearRing>
            </outerBoundaryIs>
          </Polygon>
        </Placemark>
      </Folder>
    </Document>
  </kml>`

	const downloadTxtFile = () => {
		const element = document.createElement('a')
		const file = new Blob([kml], { type: 'text/plain' })
		element.href = URL.createObjectURL(file)
		element.download = `${data.name}-${data.landmark}.kml`
		document.body.appendChild(element)
		element.click()
	}
	downloadTxtFile()
}
