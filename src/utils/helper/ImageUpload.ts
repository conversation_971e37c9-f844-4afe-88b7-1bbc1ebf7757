import { ContentType, authAxios, publicAxios } from '@/contexts'

export const handleImageUpload = async (
	file: File | undefined,
	type?: string,
	publicRoute: boolean = false
) => {
	try {
		if (!file) return

		const formData = new FormData()
		formData.append('file', file)
		formData.append('type', type ?? 'misc')

		const axiosInstance = publicRoute ? publicAxios : authAxios
		const endpoint = publicRoute ? '/public-upload' : '/upload'

		const { data } = await axiosInstance.post(endpoint, formData, {
			headers: { 'Content-Type': ContentType.FormData },
		})

		return data
	} catch (err) {
		throw err
	}
}

export const handleVideoUpload = async (
	file: File | undefined,
	type?: string
) => {
	try {
		if (!file) return
		const formData = new FormData()
		formData.append('file', file)
		formData.append('type', type ?? 'misc')
		const { data } = await authAxios.post('/upload', formData, {
			headers: { 'Content-Type': ContentType.FormData },
		})
		return data
	} catch (err) {
		throw err
	}
}
